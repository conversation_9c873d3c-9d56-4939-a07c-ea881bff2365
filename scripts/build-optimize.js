#!/usr/bin/env node

/**
 * 构建优化脚本
 * 提供构建分析、优化建议和自动化优化功能
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chalk = require('chalk');

/**
 * 构建分析器
 */
class BuildAnalyzer {
  constructor() {
    this.distPath = path.join(process.cwd(), 'dist');
    this.srcPath = path.join(process.cwd(), 'src');
  }

  /**
   * 分析构建结果
   */
  async analyze() {
    console.log(chalk.blue('🔍 开始分析构建结果...'));
    
    const analysis = {
      bundleSize: await this.analyzeBundleSize(),
      assetOptimization: await this.analyzeAssets(),
      codeQuality: await this.analyzeCodeQuality(),
      performance: await this.analyzePerformance(),
    };

    this.generateReport(analysis);
    this.generateOptimizationSuggestions(analysis);
    
    return analysis;
  }

  /**
   * 分析包大小
   */
  async analyzeBundleSize() {
    const results = {
      totalSize: 0,
      jsSize: 0,
      cssSize: 0,
      assetSize: 0,
      chunks: [],
      largeFiles: [],
    };

    if (!fs.existsSync(this.distPath)) {
      console.log(chalk.yellow('⚠️  构建目录不存在，请先执行构建'));
      return results;
    }

    const files = this.getAllFiles(this.distPath);
    
    files.forEach(file => {
      const stats = fs.statSync(file);
      const size = stats.size;
      const relativePath = path.relative(this.distPath, file);
      const ext = path.extname(file);
      
      results.totalSize += size;
      
      if (ext === '.js') {
        results.jsSize += size;
        results.chunks.push({ path: relativePath, size });
      } else if (ext === '.css') {
        results.cssSize += size;
      } else {
        results.assetSize += size;
      }
      
      // 标记大文件（>100KB）
      if (size > 100 * 1024) {
        results.largeFiles.push({ path: relativePath, size });
      }
    });

    // 按大小排序
    results.chunks.sort((a, b) => b.size - a.size);
    results.largeFiles.sort((a, b) => b.size - a.size);

    return results;
  }

  /**
   * 分析资源优化
   */
  async analyzeAssets() {
    const results = {
      images: [],
      fonts: [],
      unoptimizedImages: [],
      unusedAssets: [],
    };

    const assetFiles = this.getAllFiles(this.distPath, ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.woff', '.woff2', '.ttf']);
    
    assetFiles.forEach(file => {
      const stats = fs.statSync(file);
      const size = stats.size;
      const relativePath = path.relative(this.distPath, file);
      const ext = path.extname(file);
      
      if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(ext)) {
        results.images.push({ path: relativePath, size });
        
        // 检查是否需要优化（>50KB的图片）
        if (size > 50 * 1024) {
          results.unoptimizedImages.push({ path: relativePath, size });
        }
      } else if (['.woff', '.woff2', '.ttf'].includes(ext)) {
        results.fonts.push({ path: relativePath, size });
      }
    });

    return results;
  }

  /**
   * 分析代码质量
   */
  async analyzeCodeQuality() {
    const results = {
      duplicateCode: [],
      unusedExports: [],
      circularDependencies: [],
      complexityIssues: [],
    };

    try {
      // 检查重复代码
      const duplicates = await this.findDuplicateCode();
      results.duplicateCode = duplicates;

      // 检查未使用的导出
      const unusedExports = await this.findUnusedExports();
      results.unusedExports = unusedExports;

      // 检查循环依赖
      const circularDeps = await this.findCircularDependencies();
      results.circularDependencies = circularDeps;

    } catch (error) {
      console.log(chalk.yellow('⚠️  代码质量分析失败:', error.message));
    }

    return results;
  }

  /**
   * 分析性能指标
   */
  async analyzePerformance() {
    const results = {
      loadTime: 0,
      renderTime: 0,
      bundleParseTime: 0,
      recommendations: [],
    };

    // 估算加载时间（基于文件大小）
    const bundleSize = await this.analyzeBundleSize();
    
    // 假设 3G 网络速度 (1.6 Mbps = 200 KB/s)
    const networkSpeed = 200 * 1024; // bytes per second
    results.loadTime = (bundleSize.totalSize / networkSpeed) * 1000; // ms

    // 估算解析时间（基于 JS 大小）
    results.bundleParseTime = (bundleSize.jsSize / (1024 * 1024)) * 100; // ms per MB

    // 生成建议
    if (results.loadTime > 3000) {
      results.recommendations.push('考虑启用代码分割以减少初始加载时间');
    }
    
    if (bundleSize.jsSize > 500 * 1024) {
      results.recommendations.push('JavaScript 包过大，建议进行代码分割');
    }
    
    if (bundleSize.largeFiles.length > 0) {
      results.recommendations.push('存在大文件，建议进行资源优化');
    }

    return results;
  }

  /**
   * 获取所有文件
   */
  getAllFiles(dir, extensions = null) {
    const files = [];
    
    const traverse = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          traverse(fullPath);
        } else if (stats.isFile()) {
          if (!extensions || extensions.includes(path.extname(item))) {
            files.push(fullPath);
          }
        }
      });
    };
    
    if (fs.existsSync(dir)) {
      traverse(dir);
    }
    
    return files;
  }

  /**
   * 查找重复代码
   */
  async findDuplicateCode() {
    // 简化实现，实际项目中可以使用 jscpd 等工具
    return [];
  }

  /**
   * 查找未使用的导出
   */
  async findUnusedExports() {
    // 简化实现，实际项目中可以使用 ts-unused-exports 等工具
    return [];
  }

  /**
   * 查找循环依赖
   */
  async findCircularDependencies() {
    // 简化实现，实际项目中可以使用 circular-dependency-plugin 等工具
    return [];
  }

  /**
   * 生成分析报告
   */
  generateReport(analysis) {
    console.log(chalk.green('\n📊 构建分析报告'));
    console.log(chalk.gray('=' * 50));
    
    // 包大小报告
    console.log(chalk.blue('\n📦 包大小分析:'));
    console.log(`总大小: ${this.formatSize(analysis.bundleSize.totalSize)}`);
    console.log(`JavaScript: ${this.formatSize(analysis.bundleSize.jsSize)}`);
    console.log(`CSS: ${this.formatSize(analysis.bundleSize.cssSize)}`);
    console.log(`资源文件: ${this.formatSize(analysis.bundleSize.assetSize)}`);
    
    if (analysis.bundleSize.largeFiles.length > 0) {
      console.log(chalk.yellow('\n⚠️  大文件 (>100KB):'));
      analysis.bundleSize.largeFiles.slice(0, 5).forEach(file => {
        console.log(`  ${file.path}: ${this.formatSize(file.size)}`);
      });
    }
    
    // 资源优化报告
    if (analysis.assetOptimization.unoptimizedImages.length > 0) {
      console.log(chalk.yellow('\n🖼️  需要优化的图片 (>50KB):'));
      analysis.assetOptimization.unoptimizedImages.slice(0, 5).forEach(img => {
        console.log(`  ${img.path}: ${this.formatSize(img.size)}`);
      });
    }
    
    // 性能报告
    console.log(chalk.blue('\n⚡ 性能分析:'));
    console.log(`预估加载时间: ${Math.round(analysis.performance.loadTime)}ms`);
    console.log(`预估解析时间: ${Math.round(analysis.performance.bundleParseTime)}ms`);
    
    if (analysis.performance.recommendations.length > 0) {
      console.log(chalk.yellow('\n💡 优化建议:'));
      analysis.performance.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
    }
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions(analysis) {
    const suggestions = [];
    
    // 基于分析结果生成建议
    if (analysis.bundleSize.totalSize > 1024 * 1024) {
      suggestions.push({
        type: 'bundle-size',
        priority: 'high',
        title: '包体积过大',
        description: '总包大小超过 1MB，建议进行代码分割和资源优化',
        actions: [
          '启用动态导入进行代码分割',
          '移除未使用的依赖',
          '使用 Tree Shaking 移除死代码',
          '压缩图片和其他资源'
        ]
      });
    }
    
    if (analysis.bundleSize.jsSize > 500 * 1024) {
      suggestions.push({
        type: 'js-size',
        priority: 'medium',
        title: 'JavaScript 包过大',
        description: 'JS 包大小超过 500KB，影响页面加载速度',
        actions: [
          '使用代码分割将大包拆分',
          '延迟加载非关键代码',
          '使用更小的替代库',
          '启用 gzip 压缩'
        ]
      });
    }
    
    if (analysis.assetOptimization.unoptimizedImages.length > 0) {
      suggestions.push({
        type: 'image-optimization',
        priority: 'medium',
        title: '图片需要优化',
        description: `发现 ${analysis.assetOptimization.unoptimizedImages.length} 个大图片文件`,
        actions: [
          '压缩图片文件',
          '使用 WebP 格式',
          '实现图片懒加载',
          '使用 CDN 加速图片加载'
        ]
      });
    }
    
    // 输出建议
    if (suggestions.length > 0) {
      console.log(chalk.green('\n🚀 详细优化建议:'));
      console.log(chalk.gray('=' * 50));
      
      suggestions.forEach((suggestion, index) => {
        const priorityColor = suggestion.priority === 'high' ? chalk.red : 
                             suggestion.priority === 'medium' ? chalk.yellow : chalk.blue;
        
        console.log(`\n${index + 1}. ${chalk.bold(suggestion.title)} ${priorityColor(`[${suggestion.priority.toUpperCase()}]`)}`);
        console.log(`   ${suggestion.description}`);
        console.log('   建议操作:');
        suggestion.actions.forEach(action => {
          console.log(`   • ${action}`);
        });
      });
    }
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

/**
 * 主函数
 */
async function main() {
  const analyzer = new BuildAnalyzer();
  
  try {
    await analyzer.analyze();
    console.log(chalk.green('\n✅ 构建分析完成!'));
  } catch (error) {
    console.error(chalk.red('\n❌ 构建分析失败:'), error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { BuildAnalyzer };
