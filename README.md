# NoeMo App

一个基于 Taro 的多端应用，支持微信小程序、抖音小程序、小红书小程序和 H5 等多个平台。

## ✨ 特性

- 🚀 **多端支持**: 一套代码，多端运行
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🎨 **设计系统**: 完整的 UI 组件库和设计令牌
- 🔧 **TypeScript**: 完整的类型定义和类型安全
- 📊 **状态管理**: 基于 Zustand 的轻量级状态管理
- 🛠 **工具函数**: 丰富的工具函数库
- 🎯 **性能优化**: 内置性能监控和优化
- 🔍 **错误监控**: 完善的错误捕获和上报机制
- 📝 **日志系统**: 分级日志记录和分析
- 🧪 **测试覆盖**: 完整的单元测试和集成测试

## 🏗 技术栈

- **框架**: [Taro](https://taro.aotu.io/) 3.x
- **语言**: TypeScript
- **UI 库**: 自研组件库
- **样式**: Less + CSS Modules
- **状态管理**: [Zustand](https://github.com/pmndrs/zustand)
- **构建工具**: Vite / Webpack
- **测试**: Jest + Testing Library
- **代码规范**: ESLint + Prettier
- **提交规范**: Conventional Commits

## 📦 安装

```bash
# 克隆项目
git clone https://github.com/your-org/noemo-app.git

# 进入项目目录
cd noemo-app

# 安装依赖
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

## 🚀 快速开始

### 开发环境

```bash
# 微信小程序
npm run dev:weapp

# H5
npm run dev:h5

# 抖音小程序
npm run dev:tt

# 支付宝小程序
npm run dev:alipay

# 百度小程序
npm run dev:swan

# QQ 小程序
npm run dev:qq

# 京东小程序
npm run dev:jd
```

### 生产构建

```bash
# 构建微信小程序
npm run build:weapp

# 构建 H5
npm run build:h5

# 构建所有平台
npm run build:all

# 构建并分析包大小
npm run build:analyze

# 构建并优化
npm run build:prod
```

## 📁 项目结构

```
noemo-app/
├── src/                    # 源代码目录
│   ├── components/         # 组件库
│   │   ├── Button/        # 按钮组件
│   │   ├── Input/         # 输入框组件
│   │   ├── Modal/         # 模态框组件
│   │   ├── Layout/        # 布局组件
│   │   ├── Animation/     # 动画组件
│   │   └── Icon/          # 图标组件
│   ├── pages/             # 页面文件
│   ├── stores/            # 状态管理
│   ├── utils/             # 工具函数
│   ├── services/          # API 服务
│   ├── styles/            # 样式文件
│   ├── types/             # 类型定义
│   ├── config/            # 配置文件
│   └── app.tsx            # 应用入口
├── config/                # 构建配置
├── scripts/               # 构建脚本
├── docs/                  # 文档
├── tests/                 # 测试文件
└── dist/                  # 构建输出
```

## 🎨 组件库

### 基础组件

- **Button**: 按钮组件，支持多种样式和状态
- **Input**: 输入框组件，支持多种类型和验证
- **Modal**: 模态框组件，支持多种动画效果
- **Loading**: 加载组件，支持多种加载动画

### 布局组件

- **Container**: 容器组件，支持响应式布局
- **Row/Col**: 网格系统，支持 12 列布局
- **Grid**: CSS Grid 布局组件
- **Space**: 间距组件，统一元素间距
- **Divider**: 分割线组件

### 动画组件

- **Transition**: 过渡动画组件
- **Loading**: 加载动画组件

### 图标系统

- **Icon**: 统一图标组件，支持多种图标库

## 🛠 工具函数

### 日期工具

```typescript
import { formatDate, parseDate, getRelativeTime } from '@/utils/date';

formatDate(new Date(), 'YYYY-MM-DD'); // 2024-01-01
getRelativeTime(new Date()); // 刚刚
```

### 字符串工具

```typescript
import { capitalize, truncate, slugify } from '@/utils/string';

capitalize('hello world'); // Hello world
truncate('long text...', 10); // long text...
```

### 数组工具

```typescript
import { unique, groupBy, chunk } from '@/utils/array';

unique([1, 2, 2, 3]); // [1, 2, 3]
groupBy(users, 'role'); // { admin: [...], user: [...] }
```

## 📊 状态管理

使用 Zustand 进行状态管理：

```typescript
import { useUserStore } from '@/stores/userStore';

function Profile() {
  const { user, updateUser, logout } = useUserStore();
  
  return (
    <div>
      <h1>{user?.name}</h1>
      <button onClick={logout}>退出登录</button>
    </div>
  );
}
```

## 🔧 配置

### 环境配置

在 `src/config/env.ts` 中配置不同环境的参数：

```typescript
export const config = {
  development: {
    apiBaseUrl: 'https://dev-api.example.com',
    debug: true,
  },
  production: {
    apiBaseUrl: 'https://api.example.com',
    debug: false,
  },
};
```

### 主题配置

在 `src/styles/variables.less` 中配置设计令牌：

```less
@color-primary: #007AFF;
@color-success: #34C759;
@font-size-base: 14px;
@spacing-md: 16px;
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

## 📝 代码规范

项目使用 ESLint 和 Prettier 进行代码规范检查：

```bash
# 检查代码规范
npm run lint

# 自动修复代码规范问题
npm run lint:fix

# 类型检查
npm run type-check
```

## 🔍 调试

### 开发环境调试

在开发环境中，可以通过 `window.__DEBUG__` 访问调试工具：

```javascript
// 查看日志
window.__DEBUG__.logger.info('Debug message');

// 查看性能数据
window.__DEBUG__.performance.getStats();

// 查看错误信息
window.__DEBUG__.errors.getErrors();
```

### 性能监控

```typescript
import { markStart, markEnd } from '@/utils/performance';

// 标记性能开始
markStart('component-render');

// 组件渲染逻辑...

// 标记性能结束
markEnd('component-render');
```

## 📈 构建优化

### 包大小分析

```bash
# 分析包大小
npm run build:analyze

# 构建优化分析
npm run build:optimize
```

### 性能优化建议

1. **代码分割**: 使用动态导入进行代码分割
2. **资源优化**: 压缩图片和其他静态资源
3. **缓存策略**: 合理设置缓存策略
4. **懒加载**: 实现图片和组件懒加载

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

### 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建或工具相关
```

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证。

## 🙏 致谢

感谢以下开源项目：

- [Taro](https://taro.aotu.io/) - 多端统一开发框架
- [Zustand](https://github.com/pmndrs/zustand) - 轻量级状态管理
- [TypeScript](https://www.typescriptlang.org/) - 类型安全的 JavaScript

## 📞 联系我们

- 邮箱: <EMAIL>
- 官网: https://example.com
- 文档: https://docs.example.com

---

Made with ❤️ by NoeMo Team
