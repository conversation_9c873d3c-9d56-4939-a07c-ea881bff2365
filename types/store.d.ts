/**
 * 状态管理相关类型定义
 */

/** 用户状态 */
interface UserState {
  userInfo: UserInfo | null;
  token: string | null;
  refreshToken: string | null;
  isLogin: boolean;
  permissions: string[];
  roles: string[];
  
  // Actions
  setUserInfo: (userInfo: UserInfo | null) => void;
  setToken: (token: string | null) => void;
  setRefreshToken: (refreshToken: string | null) => void;
  setPermissions: (permissions: string[]) => void;
  setRoles: (roles: string[]) => void;
  login: (userInfo: UserInfo, token: string, refreshToken?: string) => void;
  logout: () => void;
  updateProfile: (updates: Partial<UserInfo>) => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
}

/** 应用状态 */
interface AppState {
  loading: boolean;
  theme: Theme;
  platform: Platform;
  language: string;
  version: string;
  networkStatus: 'online' | 'offline';
  orientation: 'portrait' | 'landscape';
  safeArea: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  
  // Actions
  setLoading: (loading: boolean) => void;
  setTheme: (theme: Theme) => void;
  setPlatform: (platform: Platform) => void;
  setLanguage: (language: string) => void;
  setNetworkStatus: (status: 'online' | 'offline') => void;
  setOrientation: (orientation: 'portrait' | 'landscape') => void;
  setSafeArea: (safeArea: Partial<AppState['safeArea']>) => void;
  toggleTheme: () => void;
}

/** 路由状态 */
interface RouterState {
  currentRoute: string;
  routeParams: Record<string, any>;
  routeHistory: string[];
  tabBarVisible: boolean;
  
  // Actions
  setCurrentRoute: (route: string, params?: Record<string, any>) => void;
  pushRoute: (route: string) => void;
  popRoute: () => void;
  setTabBarVisible: (visible: boolean) => void;
  clearHistory: () => void;
}

/** 缓存状态 */
interface CacheState {
  apiCache: Map<string, {
    data: any;
    timestamp: number;
    ttl: number;
  }>;
  imageCache: Map<string, string>;
  
  // Actions
  setApiCache: (key: string, data: any, ttl?: number) => void;
  getApiCache: (key: string) => any;
  clearApiCache: (key?: string) => void;
  setImageCache: (key: string, url: string) => void;
  getImageCache: (key: string) => string | undefined;
  clearImageCache: () => void;
  clearExpiredCache: () => void;
}

/** 通知状态 */
interface NotificationState {
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message?: string;
    duration?: number;
    timestamp: number;
    read: boolean;
  }>;
  unreadCount: number;
  
  // Actions
  addNotification: (notification: Omit<NotificationState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
}

/** 设置状态 */
interface SettingsState {
  // 通用设置
  autoLogin: boolean;
  rememberPassword: boolean;
  biometricAuth: boolean;
  
  // 通知设置
  pushNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  
  // 隐私设置
  locationAccess: boolean;
  cameraAccess: boolean;
  microphoneAccess: boolean;
  
  // 显示设置
  fontSize: 'small' | 'medium' | 'large';
  autoNightMode: boolean;
  
  // 缓存设置
  imageCacheSize: number;
  dataCacheSize: number;
  autoClearCache: boolean;
  
  // Actions
  updateSetting: <K extends keyof Omit<SettingsState, 'updateSetting' | 'resetSettings'>>(
    key: K,
    value: SettingsState[K]
  ) => void;
  resetSettings: () => void;
}

/** Store 中间件类型 */
interface StoreMiddleware<T> {
  (config: T): T;
}

/** 持久化配置 */
interface PersistConfig {
  name: string;
  storage?: {
    getItem: (key: string) => any;
    setItem: (key: string, value: any) => void;
    removeItem: (key: string) => void;
  };
  partialize?: (state: any) => any;
  version?: number;
  migrate?: (persistedState: any, version: number) => any;
}
