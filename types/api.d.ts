/**
 * API 相关类型定义
 */

/** 请求配置 */
interface RequestConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retry?: number;
  cache?: boolean;
  loading?: boolean;
}

/** 请求拦截器配置 */
interface RequestInterceptor {
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
  onRequestError?: (error: any) => any;
  onResponse?: (response: any) => any | Promise<any>;
  onResponseError?: (error: any) => any;
}

/** 上传文件配置 */
interface UploadConfig {
  url: string;
  filePath: string;
  name: string;
  formData?: Record<string, any>;
  headers?: Record<string, string>;
  onProgress?: (progress: number) => void;
}

/** 下载文件配置 */
interface DownloadConfig {
  url: string;
  headers?: Record<string, string>;
  onProgress?: (progress: number) => void;
}

/** 缓存配置 */
interface CacheConfig {
  key: string;
  ttl?: number; // 缓存时间，单位秒
  storage?: 'memory' | 'local' | 'session';
}

/** API 错误类型 */
interface ApiError extends Error {
  code: number;
  status?: number;
  response?: any;
  config?: RequestConfig;
}

/** 重试配置 */
interface RetryConfig {
  times: number;
  delay?: number;
  condition?: (error: ApiError) => boolean;
}

/** 并发控制配置 */
interface ConcurrencyConfig {
  max: number;
  queue?: boolean;
}

/** 登录相关 API */
namespace LoginAPI {
  interface LoginParams {
    phone?: string;
    email?: string;
    password?: string;
    code?: string; // 验证码
    type: 'password' | 'sms' | 'wechat' | 'apple';
  }

  interface LoginResponse {
    token: string;
    refreshToken: string;
    userInfo: UserInfo;
    expiresIn: number;
  }

  interface RefreshTokenParams {
    refreshToken: string;
  }

  interface SendSmsParams {
    phone: string;
    type: 'login' | 'register' | 'reset';
  }
}

/** 用户相关 API */
namespace UserAPI {
  interface UpdateProfileParams {
    nickname?: string;
    avatar?: string;
    gender?: 0 | 1 | 2;
    birthday?: string;
    city?: string;
    province?: string;
  }

  interface ChangePasswordParams {
    oldPassword: string;
    newPassword: string;
  }

  interface BindPhoneParams {
    phone: string;
    code: string;
  }
}

/** 文件相关 API */
namespace FileAPI {
  interface UploadParams {
    file: File | string; // File 对象或文件路径
    type: 'avatar' | 'image' | 'video' | 'document';
    compress?: boolean;
    quality?: number;
  }

  interface UploadResponse {
    url: string;
    filename: string;
    size: number;
    type: string;
    hash: string;
    width?: number;
    height?: number;
    duration?: number; // 视频时长
  }

  interface GetUploadTokenResponse {
    token: string;
    uploadUrl: string;
    expiresIn: number;
  }
}

/** 通用列表 API */
namespace ListAPI {
  interface ListParams extends PaginationParams {
    keyword?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    filters?: Record<string, any>;
  }

  interface ListResponse<T> extends PaginationResponse<T> {
    filters?: Record<string, any>;
    sorts?: Record<string, 'asc' | 'desc'>;
  }
}

/** 地理位置相关 API */
namespace LocationAPI {
  interface LocationInfo {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    province?: string;
    district?: string;
  }

  interface GetLocationResponse extends LocationInfo {
    accuracy: number;
    altitude?: number;
    speed?: number;
  }

  interface SearchLocationParams {
    keyword: string;
    city?: string;
    page?: number;
    pageSize?: number;
  }

  interface SearchLocationResponse {
    list: Array<{
      name: string;
      address: string;
      location: LocationInfo;
      distance?: number;
    }>;
    total: number;
  }
}

/** 支付相关 API */
namespace PaymentAPI {
  interface CreateOrderParams {
    amount: number;
    currency?: string;
    description: string;
    orderNo?: string;
    notifyUrl?: string;
    returnUrl?: string;
    extra?: Record<string, any>;
  }

  interface CreateOrderResponse {
    orderId: string;
    orderNo: string;
    paymentData: any; // 支付平台返回的支付数据
    qrCode?: string; // 二维码支付链接
  }

  interface PaymentResult {
    success: boolean;
    orderId: string;
    orderNo: string;
    transactionId?: string;
    message?: string;
  }
}

/** WebSocket 相关类型 */
namespace WebSocketAPI {
  interface WebSocketConfig {
    url: string;
    protocols?: string[];
    reconnect?: boolean;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    heartbeat?: boolean;
    heartbeatInterval?: number;
  }

  interface WebSocketMessage<T = any> {
    type: string;
    data: T;
    timestamp: number;
    id?: string;
  }

  interface WebSocketEventHandlers {
    onOpen?: (event: any) => void;
    onMessage?: (message: WebSocketMessage) => void;
    onError?: (error: any) => void;
    onClose?: (event: any) => void;
    onReconnect?: (attempt: number) => void;
  }
}
