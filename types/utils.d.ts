/**
 * 工具函数相关类型定义
 */

/** 日期格式化选项 */
interface DateFormatOptions {
  format?: string;
  locale?: string;
  timezone?: string;
}

/** 日期工具类型 */
namespace DateUtils {
  interface RelativeTimeOptions {
    locale?: string;
    numeric?: 'always' | 'auto';
    style?: 'long' | 'short' | 'narrow';
  }

  interface DateRange {
    start: Date;
    end: Date;
  }

  interface CalendarDate {
    year: number;
    month: number;
    day: number;
    weekday: number;
    isToday: boolean;
    isWeekend: boolean;
    isHoliday?: boolean;
  }
}

/** 字符串工具类型 */
namespace StringUtils {
  interface TruncateOptions {
    length: number;
    suffix?: string;
    separator?: string;
  }

  interface HighlightOptions {
    keyword: string;
    className?: string;
    caseSensitive?: boolean;
    wholeWord?: boolean;
  }

  interface SlugOptions {
    replacement?: string;
    remove?: RegExp;
    lower?: boolean;
    strict?: boolean;
    locale?: string;
  }
}

/** 数组工具类型 */
namespace ArrayUtils {
  interface GroupByResult<T, K extends keyof T> {
    [key: string]: T[];
  }

  interface SortOptions<T> {
    key?: keyof T;
    order?: 'asc' | 'desc';
    numeric?: boolean;
    locale?: string;
  }

  interface ChunkOptions {
    size: number;
    fill?: any;
  }

  interface UniqueOptions<T> {
    key?: keyof T;
    compareFn?: (a: T, b: T) => boolean;
  }
}

/** 对象工具类型 */
namespace ObjectUtils {
  interface DeepMergeOptions {
    arrayMerge?: 'replace' | 'concat' | 'merge';
    customMerge?: (key: string, target: any, source: any) => any;
  }

  interface PickByTypeOptions<T, U> {
    strict?: boolean;
  }

  interface FlattenOptions {
    delimiter?: string;
    maxDepth?: number;
    safe?: boolean;
  }
}

/** 数学工具类型 */
namespace MathUtils {
  interface RoundOptions {
    precision?: number;
    method?: 'round' | 'floor' | 'ceil';
  }

  interface RandomOptions {
    min?: number;
    max?: number;
    integer?: boolean;
  }

  interface StatisticsResult {
    sum: number;
    average: number;
    median: number;
    mode: number[];
    min: number;
    max: number;
    variance: number;
    standardDeviation: number;
  }
}

/** 验证工具类型 */
namespace ValidationUtils {
  interface ValidationRule {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    custom?: (value: any) => boolean | string;
    message?: string;
  }

  interface ValidationResult {
    valid: boolean;
    errors: string[];
  }

  interface EmailValidationOptions {
    allowDisplayName?: boolean;
    requireDisplayName?: boolean;
    allowUtf8LocalPart?: boolean;
    requireTld?: boolean;
  }

  interface PasswordValidationOptions {
    minLength?: number;
    maxLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSymbols?: boolean;
    forbiddenPatterns?: RegExp[];
  }
}

/** 文件工具类型 */
namespace FileUtils {
  interface FileInfo {
    name: string;
    size: number;
    type: string;
    extension: string;
    lastModified: number;
  }

  interface ImageInfo extends FileInfo {
    width: number;
    height: number;
    aspectRatio: number;
  }

  interface CompressOptions {
    quality?: number;
    maxWidth?: number;
    maxHeight?: number;
    format?: 'jpeg' | 'png' | 'webp';
  }

  interface UploadProgress {
    loaded: number;
    total: number;
    percentage: number;
  }
}

/** URL 工具类型 */
namespace UrlUtils {
  interface ParsedUrl {
    protocol: string;
    host: string;
    hostname: string;
    port: string;
    pathname: string;
    search: string;
    hash: string;
    params: Record<string, string>;
  }

  interface BuildUrlOptions {
    baseUrl?: string;
    path?: string;
    params?: Record<string, any>;
    hash?: string;
  }
}

/** 存储工具类型 */
namespace StorageUtils {
  interface StorageOptions {
    prefix?: string;
    serialize?: (value: any) => string;
    deserialize?: (value: string) => any;
    ttl?: number;
  }

  interface StorageItem<T = any> {
    value: T;
    timestamp: number;
    ttl?: number;
  }

  interface StorageAdapter {
    getItem: (key: string) => string | null;
    setItem: (key: string, value: string) => void;
    removeItem: (key: string) => void;
    clear: () => void;
    key: (index: number) => string | null;
    length: number;
  }
}

/** 设备工具类型 */
namespace DeviceUtils {
  interface DeviceInfo {
    platform: Platform;
    os: string;
    osVersion: string;
    brand: string;
    model: string;
    screenWidth: number;
    screenHeight: number;
    pixelRatio: number;
    statusBarHeight: number;
    safeAreaInsets: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
  }

  interface NetworkInfo {
    type: 'wifi' | 'cellular' | 'none' | 'unknown';
    isConnected: boolean;
    isInternetReachable: boolean;
    details?: {
      strength?: number;
      ssid?: string;
      bssid?: string;
      frequency?: number;
      ipAddress?: string;
      subnet?: string;
      gateway?: string;
    };
  }

  interface BatteryInfo {
    level: number;
    isCharging: boolean;
    chargingTime?: number;
    dischargingTime?: number;
  }
}

/** 性能工具类型 */
namespace PerformanceUtils {
  interface PerformanceMark {
    name: string;
    startTime: number;
    duration?: number;
  }

  interface MemoryInfo {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  }

  interface FPSInfo {
    current: number;
    average: number;
    min: number;
    max: number;
  }
}

/** 错误处理工具类型 */
namespace ErrorUtils {
  interface ErrorContext {
    userId?: string;
    sessionId?: string;
    timestamp: number;
    url?: string;
    userAgent?: string;
    platform: Platform;
    version: string;
    extra?: Record<string, any>;
  }

  interface ErrorReport {
    error: Error;
    context: ErrorContext;
    stackTrace?: string;
    breadcrumbs?: Array<{
      timestamp: number;
      message: string;
      category: string;
      level: 'info' | 'warning' | 'error';
    }>;
  }
}

/** 调试工具类型 */
namespace DebugUtils {
  interface LogLevel {
    level: 'debug' | 'info' | 'warn' | 'error';
    color?: string;
    enabled?: boolean;
  }

  interface LogEntry {
    level: LogLevel['level'];
    message: string;
    timestamp: number;
    data?: any;
    stack?: string;
  }

  interface ConsoleOptions {
    prefix?: string;
    timestamp?: boolean;
    colors?: boolean;
    maxEntries?: number;
  }
}
