/**
 * 组件相关类型定义
 */

import { ReactNode, CSSProperties } from 'react';

/** 基础组件 Props */
interface BaseComponentProps {
  className?: string;
  style?: CSSProperties;
  children?: ReactNode;
  testID?: string;
}

/** 尺寸类型 */
type Size = 'small' | 'medium' | 'large';

/** 颜色类型 */
type Color = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';

/** 按钮组件 Props */
interface ButtonProps extends BaseComponentProps {
  type?: 'primary' | 'secondary' | 'outline' | 'text' | 'link';
  size?: Size;
  color?: Color;
  disabled?: boolean;
  loading?: boolean;
  block?: boolean;
  round?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  onClick?: () => void;
  onLongPress?: () => void;
}

/** 输入框组件 Props */
interface InputProps extends BaseComponentProps {
  type?: 'text' | 'password' | 'number' | 'email' | 'tel' | 'url';
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  maxLength?: number;
  clearable?: boolean;
  showPassword?: boolean;
  prefix?: ReactNode;
  suffix?: ReactNode;
  error?: boolean;
  errorMessage?: string;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClear?: () => void;
}

/** 文本域组件 Props */
interface TextareaProps extends Omit<InputProps, 'type' | 'prefix' | 'suffix'> {
  rows?: number;
  autoHeight?: boolean;
  showCount?: boolean;
}

/** 选择器组件 Props */
interface PickerProps extends BaseComponentProps {
  value?: any;
  options: Array<{
    label: string;
    value: any;
    disabled?: boolean;
  }>;
  placeholder?: string;
  disabled?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  onChange?: (value: any) => void;
  onSearch?: (keyword: string) => void;
}

/** 日期选择器组件 Props */
interface DatePickerProps extends BaseComponentProps {
  value?: Date | string;
  format?: string;
  placeholder?: string;
  disabled?: boolean;
  mode?: 'date' | 'time' | 'datetime' | 'year' | 'month';
  minDate?: Date | string;
  maxDate?: Date | string;
  onChange?: (value: Date | string) => void;
}

/** 开关组件 Props */
interface SwitchProps extends BaseComponentProps {
  checked?: boolean;
  defaultChecked?: boolean;
  disabled?: boolean;
  size?: Size;
  color?: Color;
  checkedText?: string;
  uncheckedText?: string;
  onChange?: (checked: boolean) => void;
}

/** 复选框组件 Props */
interface CheckboxProps extends BaseComponentProps {
  checked?: boolean;
  defaultChecked?: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
  value?: any;
  label?: ReactNode;
  onChange?: (checked: boolean, value?: any) => void;
}

/** 单选框组件 Props */
interface RadioProps extends BaseComponentProps {
  checked?: boolean;
  defaultChecked?: boolean;
  disabled?: boolean;
  value?: any;
  label?: ReactNode;
  onChange?: (checked: boolean, value?: any) => void;
}

/** 单选框组 Props */
interface RadioGroupProps extends BaseComponentProps {
  value?: any;
  defaultValue?: any;
  disabled?: boolean;
  direction?: 'horizontal' | 'vertical';
  options?: Array<{
    label: ReactNode;
    value: any;
    disabled?: boolean;
  }>;
  onChange?: (value: any) => void;
}

/** 图片组件 Props */
interface ImageProps extends BaseComponentProps {
  src: string;
  alt?: string;
  width?: number | string;
  height?: number | string;
  fit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  lazy?: boolean;
  placeholder?: ReactNode;
  fallback?: ReactNode;
  preview?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  onClick?: () => void;
}

/** 头像组件 Props */
interface AvatarProps extends BaseComponentProps {
  src?: string;
  alt?: string;
  size?: Size | number;
  shape?: 'circle' | 'square';
  icon?: ReactNode;
  text?: string;
  badge?: ReactNode;
  onClick?: () => void;
}

/** 标签组件 Props */
interface TagProps extends BaseComponentProps {
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  size?: Size;
  round?: boolean;
  closable?: boolean;
  icon?: ReactNode;
  onClose?: () => void;
  onClick?: () => void;
}

/** 徽章组件 Props */
interface BadgeProps extends BaseComponentProps {
  count?: number;
  max?: number;
  dot?: boolean;
  showZero?: boolean;
  color?: string;
  text?: string;
  offset?: [number, number];
}

/** 进度条组件 Props */
interface ProgressProps extends BaseComponentProps {
  percent: number;
  type?: 'line' | 'circle';
  size?: Size;
  color?: Color | string;
  showText?: boolean;
  textFormat?: (percent: number) => string;
  strokeWidth?: number;
}

/** 加载组件 Props */
interface LoadingProps extends BaseComponentProps {
  spinning?: boolean;
  size?: Size;
  color?: Color | string;
  text?: string;
  overlay?: boolean;
}

/** 空状态组件 Props */
interface EmptyProps extends BaseComponentProps {
  image?: ReactNode;
  title?: ReactNode;
  description?: ReactNode;
  action?: ReactNode;
}

/** 列表项组件 Props */
interface ListItemProps extends BaseComponentProps {
  title?: ReactNode;
  description?: ReactNode;
  extra?: ReactNode;
  prefix?: ReactNode;
  suffix?: ReactNode;
  clickable?: boolean;
  disabled?: boolean;
  divider?: boolean;
  onClick?: () => void;
}

/** 卡片组件 Props */
interface CardProps extends BaseComponentProps {
  title?: ReactNode;
  extra?: ReactNode;
  cover?: ReactNode;
  actions?: ReactNode[];
  bordered?: boolean;
  shadow?: boolean;
  hoverable?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

/** 模态框组件 Props */
interface ModalProps extends BaseComponentProps {
  visible?: boolean;
  title?: ReactNode;
  content?: ReactNode;
  footer?: ReactNode;
  closable?: boolean;
  maskClosable?: boolean;
  width?: number | string;
  height?: number | string;
  zIndex?: number;
  onClose?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
}

/** 抽屉组件 Props */
interface DrawerProps extends BaseComponentProps {
  visible?: boolean;
  title?: ReactNode;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  width?: number | string;
  height?: number | string;
  closable?: boolean;
  maskClosable?: boolean;
  onClose?: () => void;
}

/** 轮播图组件 Props */
interface SwiperProps extends BaseComponentProps {
  autoplay?: boolean;
  interval?: number;
  loop?: boolean;
  showDots?: boolean;
  showArrows?: boolean;
  vertical?: boolean;
  onChange?: (index: number) => void;
}

/** 标签页组件 Props */
interface TabsProps extends BaseComponentProps {
  activeKey?: string;
  defaultActiveKey?: string;
  type?: 'line' | 'card' | 'button';
  position?: 'top' | 'bottom' | 'left' | 'right';
  scrollable?: boolean;
  items: Array<{
    key: string;
    label: ReactNode;
    content: ReactNode;
    disabled?: boolean;
  }>;
  onChange?: (key: string) => void;
}

/** 步骤条组件 Props */
interface StepsProps extends BaseComponentProps {
  current?: number;
  direction?: 'horizontal' | 'vertical';
  size?: Size;
  status?: 'wait' | 'process' | 'finish' | 'error';
  items: Array<{
    title: ReactNode;
    description?: ReactNode;
    icon?: ReactNode;
    status?: 'wait' | 'process' | 'finish' | 'error';
  }>;
  onChange?: (current: number) => void;
}
