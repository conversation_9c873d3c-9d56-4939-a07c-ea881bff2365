/// <reference types="@tarojs/taro" />

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

declare namespace NodeJS {
  interface ProcessEnv {
    /** NODE 内置环境变量, 会影响到最终构建生成产物 */
    NODE_ENV: 'development' | 'production',
    /** 当前构建的平台 */
    TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'qq' | 'jd' | 'harmony' | 'jdrn' | 'xhs'
    /**
     * 当前构建的小程序 appid
     * @description 若不同环境有不同的小程序，可通过在 env 文件中配置环境变量`TARO_APP_ID`来方便快速切换 appid， 而不必手动去修改 dist/project.config.json 文件
     * @see https://taro-docs.jd.com/docs/next/env-mode-config#特殊环境变量-taro_app_id
     */
    TARO_APP_ID: string
  }
}

// 全局类型定义
declare global {
  /** 平台类型 */
  type Platform = 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'qq' | 'jd' | 'harmony' | 'xhs';

  /** 环境类型 */
  type Environment = 'development' | 'production' | 'test';

  /** 主题类型 */
  type Theme = 'light' | 'dark';

  /** 通用响应结构 */
  interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
    timestamp?: number;
    requestId?: string;
  }

  /** 分页请求参数 */
  interface PaginationParams {
    page: number;
    pageSize: number;
    total?: number;
  }

  /** 分页响应数据 */
  interface PaginationResponse<T = any> {
    list: T[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  }

  /** 用户信息类型 */
  interface UserInfo {
    id: string;
    nickname: string;
    avatar: string;
    phone?: string;
    email?: string;
    gender?: 0 | 1 | 2; // 0: 未知, 1: 男, 2: 女
    birthday?: string;
    city?: string;
    province?: string;
    country?: string;
    createTime?: string;
    updateTime?: string;
  }

  /** 文件上传响应 */
  interface UploadResponse {
    url: string;
    filename: string;
    size: number;
    type: string;
    hash?: string;
  }

  /** 错误信息类型 */
  interface ErrorInfo {
    code: string | number;
    message: string;
    stack?: string;
    timestamp: number;
    platform: Platform;
    version: string;
    userId?: string;
    extra?: Record<string, any>;
  }

  /** 性能监控数据 */
  interface PerformanceData {
    type: 'page' | 'api' | 'component';
    name: string;
    duration: number;
    timestamp: number;
    platform: Platform;
    extra?: Record<string, any>;
  }
}


