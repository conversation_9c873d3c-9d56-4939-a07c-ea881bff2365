/**
 * 环境配置管理
 * 统一管理不同环境下的配置信息
 */

/**
 * 环境类型
 */
export type Environment = 'development' | 'test' | 'production';

/**
 * 平台类型
 */
export type Platform = 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'qq' | 'jd' | 'harmony' | 'xhs';

/**
 * 基础配置接口
 */
interface BaseConfig {
  // 应用信息
  appName: string;
  appVersion: string;
  appDescription: string;
  
  // API 配置
  apiBaseUrl: string;
  apiTimeout: number;
  apiRetryTimes: number;
  
  // 上传配置
  uploadBaseUrl: string;
  uploadMaxSize: number;
  uploadAllowedTypes: string[];
  
  // 存储配置
  storagePrefix: string;
  storageTTL: number;
  
  // 日志配置
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  logEnabled: boolean;
  logMaxEntries: number;
  
  // 性能监控
  performanceEnabled: boolean;
  performanceMaxEntries: number;
  
  // 错误监控
  errorReportEnabled: boolean;
  errorReportUrl?: string;
  
  // 第三方服务
  services: {
    // 地图服务
    map: {
      provider: 'tencent' | 'baidu' | 'amap';
      key: string;
    };
    
    // 统计服务
    analytics: {
      enabled: boolean;
      appId?: string;
    };
    
    // 推送服务
    push: {
      enabled: boolean;
      appId?: string;
      appKey?: string;
    };
    
    // 支付服务
    payment: {
      wechat: {
        appId: string;
        merchantId?: string;
      };
      alipay: {
        appId: string;
      };
    };
  };
  
  // 小程序配置
  miniProgram: {
    weapp: {
      appId: string;
    };
    alipay: {
      appId: string;
    };
    tt: {
      appId: string;
    };
    swan: {
      appId: string;
    };
    qq: {
      appId: string;
    };
    jd: {
      appId: string;
    };
    xhs: {
      appId: string;
    };
  };
  
  // 功能开关
  features: {
    enableDebugPanel: boolean;
    enablePerformanceMonitor: boolean;
    enableErrorBoundary: boolean;
    enableOfflineMode: boolean;
    enableDarkMode: boolean;
    enableBiometric: boolean;
  };
}

/**
 * 开发环境配置
 */
const developmentConfig: BaseConfig = {
  appName: 'NoeMo App',
  appVersion: '1.0.0',
  appDescription: 'NoeMo 多端应用',
  
  apiBaseUrl: 'https://api-dev.noemo.com',
  apiTimeout: 10000,
  apiRetryTimes: 3,
  
  uploadBaseUrl: 'https://upload-dev.noemo.com',
  uploadMaxSize: 10 * 1024 * 1024, // 10MB
  uploadAllowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4'],
  
  storagePrefix: 'noemo_dev',
  storageTTL: 7 * 24 * 60 * 60, // 7天
  
  logLevel: 'debug',
  logEnabled: true,
  logMaxEntries: 1000,
  
  performanceEnabled: true,
  performanceMaxEntries: 500,
  
  errorReportEnabled: true,
  errorReportUrl: 'https://error-dev.noemo.com/report',
  
  services: {
    map: {
      provider: 'tencent',
      key: 'dev_map_key',
    },
    analytics: {
      enabled: true,
      appId: 'dev_analytics_id',
    },
    push: {
      enabled: false,
    },
    payment: {
      wechat: {
        appId: 'dev_wechat_app_id',
      },
      alipay: {
        appId: 'dev_alipay_app_id',
      },
    },
  },
  
  miniProgram: {
    weapp: {
      appId: 'dev_weapp_id',
    },
    alipay: {
      appId: 'dev_alipay_id',
    },
    tt: {
      appId: 'dev_tt_id',
    },
    swan: {
      appId: 'dev_swan_id',
    },
    qq: {
      appId: 'dev_qq_id',
    },
    jd: {
      appId: 'dev_jd_id',
    },
    xhs: {
      appId: 'dev_xhs_id',
    },
  },
  
  features: {
    enableDebugPanel: true,
    enablePerformanceMonitor: true,
    enableErrorBoundary: true,
    enableOfflineMode: false,
    enableDarkMode: true,
    enableBiometric: false,
  },
};

/**
 * 测试环境配置
 */
const testConfig: BaseConfig = {
  ...developmentConfig,
  
  apiBaseUrl: 'https://api-test.noemo.com',
  uploadBaseUrl: 'https://upload-test.noemo.com',
  errorReportUrl: 'https://error-test.noemo.com/report',
  
  storagePrefix: 'noemo_test',
  logLevel: 'info',
  
  services: {
    ...developmentConfig.services,
    map: {
      provider: 'tencent',
      key: 'test_map_key',
    },
    analytics: {
      enabled: true,
      appId: 'test_analytics_id',
    },
  },
  
  miniProgram: {
    weapp: {
      appId: 'test_weapp_id',
    },
    alipay: {
      appId: 'test_alipay_id',
    },
    tt: {
      appId: 'test_tt_id',
    },
    swan: {
      appId: 'test_swan_id',
    },
    qq: {
      appId: 'test_qq_id',
    },
    jd: {
      appId: 'test_jd_id',
    },
    xhs: {
      appId: 'test_xhs_id',
    },
  },
  
  features: {
    ...developmentConfig.features,
    enableDebugPanel: false,
  },
};

/**
 * 生产环境配置
 */
const productionConfig: BaseConfig = {
  ...developmentConfig,
  
  apiBaseUrl: 'https://api.noemo.com',
  uploadBaseUrl: 'https://upload.noemo.com',
  errorReportUrl: 'https://error.noemo.com/report',
  
  storagePrefix: 'noemo',
  logLevel: 'warn',
  logMaxEntries: 100,
  
  performanceMaxEntries: 100,
  
  services: {
    ...developmentConfig.services,
    map: {
      provider: 'tencent',
      key: 'prod_map_key',
    },
    analytics: {
      enabled: true,
      appId: 'prod_analytics_id',
    },
    push: {
      enabled: true,
      appId: 'prod_push_app_id',
      appKey: 'prod_push_app_key',
    },
  },
  
  miniProgram: {
    weapp: {
      appId: 'prod_weapp_id',
    },
    alipay: {
      appId: 'prod_alipay_id',
    },
    tt: {
      appId: 'prod_tt_id',
    },
    swan: {
      appId: 'prod_swan_id',
    },
    qq: {
      appId: 'prod_qq_id',
    },
    jd: {
      appId: 'prod_jd_id',
    },
    xhs: {
      appId: 'prod_xhs_id',
    },
  },
  
  features: {
    enableDebugPanel: false,
    enablePerformanceMonitor: false,
    enableErrorBoundary: true,
    enableOfflineMode: true,
    enableDarkMode: true,
    enableBiometric: true,
  },
};

/**
 * 环境配置映射
 */
const configs: Record<Environment, BaseConfig> = {
  development: developmentConfig,
  test: testConfig,
  production: productionConfig,
};

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): Environment {
  const nodeEnv = process.env.NODE_ENV as Environment;
  
  // 如果有自定义环境变量，优先使用
  if (process.env.APP_ENV) {
    return process.env.APP_ENV as Environment;
  }
  
  // 根据 NODE_ENV 判断
  if (nodeEnv === 'production') {
    return 'production';
  } else if (nodeEnv === 'test') {
    return 'test';
  } else {
    return 'development';
  }
}

/**
 * 获取当前平台
 */
export function getCurrentPlatform(): Platform {
  return (process.env.TARO_ENV as Platform) || 'h5';
}

/**
 * 获取配置
 */
export function getConfig(): BaseConfig {
  const env = getCurrentEnvironment();
  return configs[env];
}

/**
 * 获取 API 基础 URL
 */
export function getApiBaseUrl(): string {
  return getConfig().apiBaseUrl;
}

/**
 * 获取上传基础 URL
 */
export function getUploadBaseUrl(): string {
  return getConfig().uploadBaseUrl;
}

/**
 * 获取当前平台的小程序配置
 */
export function getMiniProgramConfig() {
  const config = getConfig();
  const platform = getCurrentPlatform();
  
  switch (platform) {
    case 'weapp':
      return config.miniProgram.weapp;
    case 'alipay':
      return config.miniProgram.alipay;
    case 'tt':
      return config.miniProgram.tt;
    case 'swan':
      return config.miniProgram.swan;
    case 'qq':
      return config.miniProgram.qq;
    case 'jd':
      return config.miniProgram.jd;
    case 'xhs':
      return config.miniProgram.xhs;
    default:
      return null;
  }
}

/**
 * 检查功能是否启用
 */
export function isFeatureEnabled(feature: keyof BaseConfig['features']): boolean {
  return getConfig().features[feature];
}

/**
 * 获取服务配置
 */
export function getServiceConfig<T extends keyof BaseConfig['services']>(
  service: T
): BaseConfig['services'][T] {
  return getConfig().services[service];
}

/**
 * 是否为开发环境
 */
export function isDevelopment(): boolean {
  return getCurrentEnvironment() === 'development';
}

/**
 * 是否为测试环境
 */
export function isTest(): boolean {
  return getCurrentEnvironment() === 'test';
}

/**
 * 是否为生产环境
 */
export function isProduction(): boolean {
  return getCurrentEnvironment() === 'production';
}

/**
 * 获取环境信息
 */
export function getEnvironmentInfo() {
  return {
    environment: getCurrentEnvironment(),
    platform: getCurrentPlatform(),
    nodeEnv: process.env.NODE_ENV,
    taroEnv: process.env.TARO_ENV,
    appVersion: getConfig().appVersion,
    isDevelopment: isDevelopment(),
    isTest: isTest(),
    isProduction: isProduction(),
  };
}

// 导出配置对象
export const config = getConfig();
export default config;
