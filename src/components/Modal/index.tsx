/**
 * 通用模态框组件
 * 支持多种样式、动画、位置的模态框组件
 */

import React from 'react';
import { View, Text } from '@tarojs/components';
import { ITouchEvent } from '@tarojs/components/types/common';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import Button from '../Button';
import './index.scss';

/**
 * 模态框尺寸
 */
export type ModalSize = 'small' | 'medium' | 'large' | 'full';

/**
 * 模态框位置
 */
export type ModalPosition = 'center' | 'top' | 'bottom' | 'left' | 'right';

/**
 * 动画类型
 */
export type AnimationType = 'fade' | 'slide' | 'zoom' | 'flip';

/**
 * 模态框属性接口
 */
export interface ModalProps {
  /**
   * 是否显示
   */
  visible?: boolean;
  
  /**
   * 模态框尺寸
   */
  size?: ModalSize;
  
  /**
   * 模态框位置
   */
  position?: ModalPosition;
  
  /**
   * 动画类型
   */
  animation?: AnimationType;
  
  /**
   * 标题
   */
  title?: React.ReactNode;
  
  /**
   * 内容
   */
  children?: React.ReactNode;
  
  /**
   * 底部内容
   */
  footer?: React.ReactNode;
  
  /**
   * 是否显示关闭按钮
   */
  closable?: boolean;
  
  /**
   * 是否显示遮罩层
   */
  mask?: boolean;
  
  /**
   * 点击遮罩层是否关闭
   */
  maskClosable?: boolean;
  
  /**
   * 是否显示确认按钮
   */
  showConfirm?: boolean;
  
  /**
   * 是否显示取消按钮
   */
  showCancel?: boolean;
  
  /**
   * 确认按钮文本
   */
  confirmText?: string;
  
  /**
   * 取消按钮文本
   */
  cancelText?: string;
  
  /**
   * 确认按钮类型
   */
  confirmType?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  
  /**
   * 确认按钮是否加载中
   */
  confirmLoading?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 层级
   */
  zIndex?: number;
  
  /**
   * 动画持续时间（毫秒）
   */
  duration?: number;
  
  /**
   * 是否销毁内容
   */
  destroyOnClose?: boolean;
  
  /**
   * 关闭事件
   */
  onClose?: () => void;
  
  /**
   * 确认事件
   */
  onConfirm?: () => void | Promise<void>;
  
  /**
   * 取消事件
   */
  onCancel?: () => void;
  
  /**
   * 打开后回调
   */
  onAfterOpen?: () => void;
  
  /**
   * 关闭后回调
   */
  onAfterClose?: () => void;
  
  /**
   * 遮罩层点击事件
   */
  onMaskClick?: (event: ITouchEvent) => void;
}

/**
 * 模态框组件
 */
const Modal: React.FC<ModalProps> = ({
  visible = false,
  size = 'medium',
  position = 'center',
  animation = 'fade',
  title,
  children,
  footer,
  closable = true,
  mask = true,
  maskClosable = true,
  showConfirm = false,
  showCancel = false,
  confirmText = '确认',
  cancelText = '取消',
  confirmType = 'primary',
  confirmLoading = false,
  className,
  style,
  zIndex = 1000,
  duration = 300,
  destroyOnClose = false,
  onClose,
  onConfirm,
  onCancel,
  onAfterOpen,
  onAfterClose,
  onMaskClick,
}) => {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = React.useState(visible);
  const [isAnimating, setIsAnimating] = React.useState(false);
  const [shouldRender, setShouldRender] = React.useState(visible);

  // 处理显示状态变化
  React.useEffect(() => {
    if (visible) {
      setShouldRender(true);
      setIsAnimating(true);
      
      // 延迟显示动画
      const timer = setTimeout(() => {
        setIsVisible(true);
        onAfterOpen?.();
      }, 10);
      
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
      setIsAnimating(true);
      
      // 动画结束后隐藏
      const timer = setTimeout(() => {
        setIsAnimating(false);
        if (destroyOnClose) {
          setShouldRender(false);
        }
        onAfterClose?.();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [visible, duration, destroyOnClose, onAfterOpen, onAfterClose]);

  // 处理关闭
  const handleClose = React.useCallback(() => {
    onClose?.();
  }, [onClose]);

  // 处理确认
  const handleConfirm = React.useCallback(async () => {
    if (onConfirm) {
      try {
        await onConfirm();
      } catch (error) {
        console.error('Modal confirm error:', error);
      }
    }
  }, [onConfirm]);

  // 处理取消
  const handleCancel = React.useCallback(() => {
    onCancel?.();
    handleClose();
  }, [onCancel, handleClose]);

  // 处理遮罩层点击
  const handleMaskClick = React.useCallback((event: ITouchEvent) => {
    onMaskClick?.(event);
    
    if (maskClosable) {
      handleClose();
    }
  }, [maskClosable, onMaskClick, handleClose]);

  // 阻止内容区域点击事件冒泡
  const handleContentClick = React.useCallback((event: ITouchEvent) => {
    event.stopPropagation();
  }, []);

  // 如果不需要渲染，直接返回 null
  if (!shouldRender && !isAnimating) {
    return null;
  }

  // 构建类名
  const modalClass = classNames(
    'modal',
    `modal--${size}`,
    `modal--${position}`,
    `modal--${animation}`,
    {
      'modal--visible': isVisible,
      'modal--dark': theme === 'dark',
    },
    className
  );

  const maskClass = classNames('modal__mask', {
    'modal__mask--visible': isVisible,
  });

  const contentClass = classNames('modal__content', {
    'modal__content--visible': isVisible,
  });

  // 渲染头部
  const renderHeader = () => {
    if (!title && !closable) return null;
    
    return (
      <View className="modal__header">
        {title && (
          <View className="modal__title">
            {typeof title === 'string' ? <Text>{title}</Text> : title}
          </View>
        )}
        {closable && (
          <View className="modal__close" onClick={handleClose}>
            ×
          </View>
        )}
      </View>
    );
  };

  // 渲染底部
  const renderFooter = () => {
    if (footer !== undefined) {
      return footer && (
        <View className="modal__footer">
          {footer}
        </View>
      );
    }
    
    if (!showConfirm && !showCancel) return null;
    
    return (
      <View className="modal__footer">
        <View className="modal__actions">
          {showCancel && (
            <Button
              type="secondary"
              onClick={handleCancel}
              className="modal__action-button"
            >
              {cancelText}
            </Button>
          )}
          {showConfirm && (
            <Button
              type={confirmType}
              loading={confirmLoading}
              onClick={handleConfirm}
              className="modal__action-button"
            >
              {confirmText}
            </Button>
          )}
        </View>
      </View>
    );
  };

  return (
    <View
      className={modalClass}
      style={{ zIndex, ...style }}
    >
      {mask && (
        <View
          className={maskClass}
          onClick={handleMaskClick}
        />
      )}
      
      <View
        className={contentClass}
        onClick={handleContentClick}
      >
        {renderHeader()}
        
        <View className="modal__body">
          {children}
        </View>
        
        {renderFooter()}
      </View>
    </View>
  );
};

export default Modal;
