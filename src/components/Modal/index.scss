/**
 * 模态框组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;

  &--visible {
    pointer-events: auto;
  }

  // 遮罩层
  &__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &--visible {
      opacity: 1;
    }
  }

  // 内容区域
  &__content {
    position: relative;
    display: flex;
    flex-direction: column;
    background-color: $color-white;
    border-radius: $border-radius-lg;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s ease-in-out;

    &--visible {
      transform: scale(1);
      opacity: 1;
    }
  }

  // 头部
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid $color-border;
    flex-shrink: 0;
  }

  &__title {
    flex: 1;
    font-size: $font-size-lg;
    font-weight: 600;
    color: $color-text-primary;
  }

  &__close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-left: 16px;
    font-size: 20px;
    color: $color-text-secondary;
    cursor: pointer;
    border-radius: $border-radius-sm;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: $color-gray-100;
      color: $color-text-primary;
    }
  }

  // 主体
  &__body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    color: $color-text-primary;
    line-height: 1.6;
  }

  // 底部
  &__footer {
    padding: 16px 20px;
    border-top: 1px solid $color-border;
    flex-shrink: 0;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  &__action-button {
    min-width: 80px;
  }

  // 尺寸变体
  &--small {
    .modal__content {
      width: 90%;
      max-width: 400px;
    }
  }

  &--medium {
    .modal__content {
      width: 90%;
      max-width: 600px;
    }
  }

  &--large {
    .modal__content {
      width: 90%;
      max-width: 800px;
    }
  }

  &--full {
    .modal__content {
      width: 95%;
      height: 95%;
      max-width: none;
      max-height: none;
    }
  }

  // 位置变体
  &--top {
    align-items: flex-start;
    padding-top: 10vh;

    .modal__content {
      transform: translateY(-50px);

      &--visible {
        transform: translateY(0);
      }
    }
  }

  &--bottom {
    align-items: flex-end;

    .modal__content {
      border-radius: $border-radius-lg $border-radius-lg 0 0;
      transform: translateY(100%);

      &--visible {
        transform: translateY(0);
      }
    }
  }

  &--left {
    justify-content: flex-start;

    .modal__content {
      height: 100%;
      border-radius: 0 $border-radius-lg $border-radius-lg 0;
      transform: translateX(-100%);

      &--visible {
        transform: translateX(0);
      }
    }
  }

  &--right {
    justify-content: flex-end;

    .modal__content {
      height: 100%;
      border-radius: $border-radius-lg 0 0 $border-radius-lg;
      transform: translateX(100%);

      &--visible {
        transform: translateX(0);
      }
    }
  }

  // 动画变体
  &--slide {
    &.modal--center {
      .modal__content {
        transform: translateY(50px);

        &--visible {
          transform: translateY(0);
        }
      }
    }
  }

  &--zoom {
    .modal__content {
      transform: scale(0.5);

      &--visible {
        transform: scale(1);
      }
    }
  }

  &--flip {
    .modal__content {
      transform: rotateY(90deg);

      &--visible {
        transform: rotateY(0deg);
      }
    }
  }

  // 暗色主题
  &--dark {
    .modal__mask {
      background-color: rgba(0, 0, 0, 0.7);
    }

    .modal__content {
      background-color: $color-gray-800;
      color: $color-white;
    }

    .modal__header {
      border-bottom-color: $color-gray-700;
    }

    .modal__title {
      color: $color-white;
    }

    .modal__close {
      color: $color-gray-400;

      &:hover {
        background-color: $color-gray-700;
        color: $color-white;
      }
    }

    .modal__body {
      color: $color-white;
    }

    .modal__footer {
      border-top-color: $color-gray-700;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modal {
    &--small,
    &--medium,
    &--large {
      .modal__content {
        width: 95%;
        margin: 0 2.5%;
      }
    }

    &--full {
      .modal__content {
        width: 100%;
        height: 100%;
        border-radius: 0;
      }
    }

    &__header {
      padding: 12px 16px;
    }

    &__body {
      padding: 16px;
    }

    &__footer {
      padding: 12px 16px;
    }

    &__actions {
      flex-direction: column-reverse;
      gap: 8px;

      .modal__action-button {
        width: 100%;
      }
    }
  }
}
