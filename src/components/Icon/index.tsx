/**
 * 图标组件
 * 统一的图标系统，支持多种图标库和自定义图标
 */

import React from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './index.scss';

/**
 * 图标尺寸
 */
export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | number;

/**
 * 图标类型
 */
export type IconType = 'outline' | 'filled' | 'duotone' | 'light' | 'bold';

/**
 * 图标属性
 */
export interface IconProps {
  /**
   * 图标名称
   */
  name: string;
  
  /**
   * 图标尺寸
   */
  size?: IconSize;
  
  /**
   * 图标类型
   */
  type?: IconType;
  
  /**
   * 图标颜色
   */
  color?: string;
  
  /**
   * 是否旋转
   */
  spin?: boolean;
  
  /**
   * 旋转角度
   */
  rotate?: number;
  
  /**
   * 是否翻转
   */
  flip?: 'horizontal' | 'vertical' | 'both';
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 点击事件
   */
  onClick?: (event: any) => void;
  
  /**
   * 自定义 SVG 内容
   */
  children?: React.ReactNode;
  
  /**
   * 图标前缀（用于不同图标库）
   */
  prefix?: string;
  
  /**
   * 是否为徽章图标
   */
  badge?: boolean;
  
  /**
   * 徽章内容
   */
  badgeContent?: string | number;
  
  /**
   * 徽章颜色
   */
  badgeColor?: string;
}

/**
 * 内置图标映射
 */
const ICON_MAP: Record<string, string> = {
  // 基础图标
  'home': '🏠',
  'user': '👤',
  'settings': '⚙️',
  'search': '🔍',
  'heart': '❤️',
  'star': '⭐',
  'plus': '➕',
  'minus': '➖',
  'close': '✖️',
  'check': '✅',
  'arrow-up': '⬆️',
  'arrow-down': '⬇️',
  'arrow-left': '⬅️',
  'arrow-right': '➡️',
  'chevron-up': '🔼',
  'chevron-down': '🔽',
  'chevron-left': '◀️',
  'chevron-right': '▶️',
  'menu': '☰',
  'more': '⋯',
  'edit': '✏️',
  'delete': '🗑️',
  'share': '📤',
  'download': '⬇️',
  'upload': '⬆️',
  'refresh': '🔄',
  'loading': '⏳',
  'warning': '⚠️',
  'error': '❌',
  'info': 'ℹ️',
  'success': '✅',
  'lock': '🔒',
  'unlock': '🔓',
  'eye': '👁️',
  'eye-off': '🙈',
  'calendar': '📅',
  'clock': '🕐',
  'mail': '📧',
  'phone': '📞',
  'location': '📍',
  'camera': '📷',
  'image': '🖼️',
  'file': '📄',
  'folder': '📁',
  'link': '🔗',
  'copy': '📋',
  'cut': '✂️',
  'paste': '📋',
  'save': '💾',
  'print': '🖨️',
  'wifi': '📶',
  'bluetooth': '📶',
  'battery': '🔋',
  'volume': '🔊',
  'mute': '🔇',
  'play': '▶️',
  'pause': '⏸️',
  'stop': '⏹️',
  'skip-back': '⏮️',
  'skip-forward': '⏭️',
  'repeat': '🔁',
  'shuffle': '🔀',
};

/**
 * 获取图标尺寸值
 */
const getIconSize = (size: IconSize): number => {
  if (typeof size === 'number') {
    return size;
  }
  
  const sizeMap = {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    '2xl': 40,
  };
  
  return sizeMap[size] || 20;
};

/**
 * 图标组件
 */
const Icon: React.FC<IconProps> = ({
  name,
  size = 'md',
  type = 'outline',
  color,
  spin = false,
  rotate,
  flip,
  className,
  style,
  onClick,
  children,
  prefix = 'icon',
  badge = false,
  badgeContent,
  badgeColor,
}) => {
  const { theme } = useTheme();
  const iconSize = getIconSize(size);

  // 构建类名
  const iconClass = classNames(
    'icon',
    `icon--${type}`,
    `icon--${size}`,
    {
      'icon--spin': spin,
      'icon--clickable': !!onClick,
      'icon--with-badge': badge,
      'icon--dark': theme === 'dark',
      [`icon--flip-${flip}`]: flip,
    },
    className
  );

  // 构建样式
  const iconStyle: React.CSSProperties = {
    fontSize: `${iconSize}px`,
    width: `${iconSize}px`,
    height: `${iconSize}px`,
    ...style,
  };

  if (color) {
    iconStyle.color = color;
  }

  if (rotate) {
    iconStyle.transform = `rotate(${rotate}deg)`;
  }

  // 渲染图标内容
  const renderIconContent = () => {
    // 如果有自定义子元素，直接使用
    if (children) {
      return children;
    }

    // 检查是否为内置图标
    if (ICON_MAP[name]) {
      return (
        <Text className="icon__emoji">
          {ICON_MAP[name]}
        </Text>
      );
    }

    // 检查是否为 SVG 图标（以 svg: 开头）
    if (name.startsWith('svg:')) {
      const svgName = name.replace('svg:', '');
      return (
        <View className="icon__svg">
          {/* 这里可以根据实际需求加载 SVG 图标 */}
          <Text className="icon__text">{svgName}</Text>
        </View>
      );
    }

    // 检查是否为字体图标（以 font: 开头）
    if (name.startsWith('font:')) {
      const fontIcon = name.replace('font:', '');
      return (
        <Text className={`icon__font ${prefix}-${fontIcon}`} />
      );
    }

    // 检查是否为图片图标（以 img: 开头）
    if (name.startsWith('img:')) {
      const imgSrc = name.replace('img:', '');
      return (
        <View 
          className="icon__image"
          style={{
            backgroundImage: `url(${imgSrc})`,
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
          }}
        />
      );
    }

    // 默认显示图标名称
    return (
      <Text className="icon__text">
        {name}
      </Text>
    );
  };

  // 渲染徽章
  const renderBadge = () => {
    if (!badge || !badgeContent) return null;
    
    const badgeStyle: React.CSSProperties = {};
    if (badgeColor) {
      badgeStyle.backgroundColor = badgeColor;
    }
    
    return (
      <View className="icon__badge" style={badgeStyle}>
        <Text className="icon__badge-text">
          {badgeContent}
        </Text>
      </View>
    );
  };

  return (
    <View 
      className={iconClass} 
      style={iconStyle}
      onClick={onClick}
    >
      <View className="icon__content">
        {renderIconContent()}
      </View>
      {renderBadge()}
    </View>
  );
};

export default Icon;
