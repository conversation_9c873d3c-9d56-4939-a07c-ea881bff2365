/**
 * 图标库
 * 定义所有可用的图标
 */

/**
 * 图标分类
 */
export interface IconCategory {
  name: string;
  label: string;
  icons: string[];
}

/**
 * 图标信息
 */
export interface IconInfo {
  name: string;
  label: string;
  category: string;
  tags: string[];
  unicode?: string;
  svg?: string;
}

/**
 * 基础图标
 */
export const BASIC_ICONS: IconInfo[] = [
  { name: 'home', label: '首页', category: 'basic', tags: ['主页', '房子'] },
  { name: 'user', label: '用户', category: 'basic', tags: ['人员', '个人'] },
  { name: 'settings', label: '设置', category: 'basic', tags: ['配置', '齿轮'] },
  { name: 'search', label: '搜索', category: 'basic', tags: ['查找', '放大镜'] },
  { name: 'heart', label: '喜欢', category: 'basic', tags: ['爱心', '收藏'] },
  { name: 'star', label: '星星', category: 'basic', tags: ['收藏', '评分'] },
  { name: 'plus', label: '添加', category: 'basic', tags: ['新增', '加号'] },
  { name: 'minus', label: '减少', category: 'basic', tags: ['删除', '减号'] },
  { name: 'close', label: '关闭', category: 'basic', tags: ['取消', '叉号'] },
  { name: 'check', label: '确认', category: 'basic', tags: ['勾选', '对号'] },
];

/**
 * 箭头图标
 */
export const ARROW_ICONS: IconInfo[] = [
  { name: 'arrow-up', label: '向上箭头', category: 'arrow', tags: ['上', '箭头'] },
  { name: 'arrow-down', label: '向下箭头', category: 'arrow', tags: ['下', '箭头'] },
  { name: 'arrow-left', label: '向左箭头', category: 'arrow', tags: ['左', '箭头'] },
  { name: 'arrow-right', label: '向右箭头', category: 'arrow', tags: ['右', '箭头'] },
  { name: 'chevron-up', label: '向上', category: 'arrow', tags: ['上', '尖括号'] },
  { name: 'chevron-down', label: '向下', category: 'arrow', tags: ['下', '尖括号'] },
  { name: 'chevron-left', label: '向左', category: 'arrow', tags: ['左', '尖括号'] },
  { name: 'chevron-right', label: '向右', category: 'arrow', tags: ['右', '尖括号'] },
];

/**
 * 操作图标
 */
export const ACTION_ICONS: IconInfo[] = [
  { name: 'edit', label: '编辑', category: 'action', tags: ['修改', '铅笔'] },
  { name: 'delete', label: '删除', category: 'action', tags: ['移除', '垃圾桶'] },
  { name: 'share', label: '分享', category: 'action', tags: ['共享', '发送'] },
  { name: 'download', label: '下载', category: 'action', tags: ['保存', '下载'] },
  { name: 'upload', label: '上传', category: 'action', tags: ['上传', '发送'] },
  { name: 'refresh', label: '刷新', category: 'action', tags: ['重新加载', '更新'] },
  { name: 'copy', label: '复制', category: 'action', tags: ['拷贝', '复制'] },
  { name: 'cut', label: '剪切', category: 'action', tags: ['剪切', '剪刀'] },
  { name: 'paste', label: '粘贴', category: 'action', tags: ['粘贴', '剪贴板'] },
  { name: 'save', label: '保存', category: 'action', tags: ['存储', '磁盘'] },
  { name: 'print', label: '打印', category: 'action', tags: ['打印机', '输出'] },
];

/**
 * 状态图标
 */
export const STATUS_ICONS: IconInfo[] = [
  { name: 'loading', label: '加载中', category: 'status', tags: ['等待', '进度'] },
  { name: 'warning', label: '警告', category: 'status', tags: ['注意', '感叹号'] },
  { name: 'error', label: '错误', category: 'status', tags: ['失败', '叉号'] },
  { name: 'info', label: '信息', category: 'status', tags: ['提示', '信息'] },
  { name: 'success', label: '成功', category: 'status', tags: ['完成', '对号'] },
];

/**
 * 安全图标
 */
export const SECURITY_ICONS: IconInfo[] = [
  { name: 'lock', label: '锁定', category: 'security', tags: ['加锁', '安全'] },
  { name: 'unlock', label: '解锁', category: 'security', tags: ['开锁', '解除'] },
  { name: 'eye', label: '显示', category: 'security', tags: ['查看', '眼睛'] },
  { name: 'eye-off', label: '隐藏', category: 'security', tags: ['不看', '隐藏'] },
];

/**
 * 时间图标
 */
export const TIME_ICONS: IconInfo[] = [
  { name: 'calendar', label: '日历', category: 'time', tags: ['日期', '时间'] },
  { name: 'clock', label: '时钟', category: 'time', tags: ['时间', '钟表'] },
];

/**
 * 通信图标
 */
export const COMMUNICATION_ICONS: IconInfo[] = [
  { name: 'mail', label: '邮件', category: 'communication', tags: ['邮箱', '消息'] },
  { name: 'phone', label: '电话', category: 'communication', tags: ['通话', '联系'] },
  { name: 'location', label: '位置', category: 'communication', tags: ['地址', '定位'] },
];

/**
 * 媒体图标
 */
export const MEDIA_ICONS: IconInfo[] = [
  { name: 'camera', label: '相机', category: 'media', tags: ['拍照', '摄像'] },
  { name: 'image', label: '图片', category: 'media', tags: ['照片', '图像'] },
  { name: 'play', label: '播放', category: 'media', tags: ['开始', '播放'] },
  { name: 'pause', label: '暂停', category: 'media', tags: ['停止', '暂停'] },
  { name: 'stop', label: '停止', category: 'media', tags: ['结束', '停止'] },
  { name: 'skip-back', label: '上一个', category: 'media', tags: ['前一个', '后退'] },
  { name: 'skip-forward', label: '下一个', category: 'media', tags: ['下一个', '前进'] },
  { name: 'repeat', label: '重复', category: 'media', tags: ['循环', '重播'] },
  { name: 'shuffle', label: '随机', category: 'media', tags: ['打乱', '随机播放'] },
  { name: 'volume', label: '音量', category: 'media', tags: ['声音', '音响'] },
  { name: 'mute', label: '静音', category: 'media', tags: ['无声', '关闭声音'] },
];

/**
 * 文件图标
 */
export const FILE_ICONS: IconInfo[] = [
  { name: 'file', label: '文件', category: 'file', tags: ['文档', '档案'] },
  { name: 'folder', label: '文件夹', category: 'file', tags: ['目录', '文件夹'] },
  { name: 'link', label: '链接', category: 'file', tags: ['连接', '网址'] },
];

/**
 * 设备图标
 */
export const DEVICE_ICONS: IconInfo[] = [
  { name: 'wifi', label: 'WiFi', category: 'device', tags: ['无线', '网络'] },
  { name: 'bluetooth', label: '蓝牙', category: 'device', tags: ['无线连接', '蓝牙'] },
  { name: 'battery', label: '电池', category: 'device', tags: ['电量', '电源'] },
];

/**
 * 导航图标
 */
export const NAVIGATION_ICONS: IconInfo[] = [
  { name: 'menu', label: '菜单', category: 'navigation', tags: ['导航', '列表'] },
  { name: 'more', label: '更多', category: 'navigation', tags: ['省略号', '更多选项'] },
];

/**
 * 所有图标
 */
export const ALL_ICONS: IconInfo[] = [
  ...BASIC_ICONS,
  ...ARROW_ICONS,
  ...ACTION_ICONS,
  ...STATUS_ICONS,
  ...SECURITY_ICONS,
  ...TIME_ICONS,
  ...COMMUNICATION_ICONS,
  ...MEDIA_ICONS,
  ...FILE_ICONS,
  ...DEVICE_ICONS,
  ...NAVIGATION_ICONS,
];

/**
 * 图标分类
 */
export const ICON_CATEGORIES: IconCategory[] = [
  {
    name: 'basic',
    label: '基础图标',
    icons: BASIC_ICONS.map(icon => icon.name),
  },
  {
    name: 'arrow',
    label: '箭头图标',
    icons: ARROW_ICONS.map(icon => icon.name),
  },
  {
    name: 'action',
    label: '操作图标',
    icons: ACTION_ICONS.map(icon => icon.name),
  },
  {
    name: 'status',
    label: '状态图标',
    icons: STATUS_ICONS.map(icon => icon.name),
  },
  {
    name: 'security',
    label: '安全图标',
    icons: SECURITY_ICONS.map(icon => icon.name),
  },
  {
    name: 'time',
    label: '时间图标',
    icons: TIME_ICONS.map(icon => icon.name),
  },
  {
    name: 'communication',
    label: '通信图标',
    icons: COMMUNICATION_ICONS.map(icon => icon.name),
  },
  {
    name: 'media',
    label: '媒体图标',
    icons: MEDIA_ICONS.map(icon => icon.name),
  },
  {
    name: 'file',
    label: '文件图标',
    icons: FILE_ICONS.map(icon => icon.name),
  },
  {
    name: 'device',
    label: '设备图标',
    icons: DEVICE_ICONS.map(icon => icon.name),
  },
  {
    name: 'navigation',
    label: '导航图标',
    icons: NAVIGATION_ICONS.map(icon => icon.name),
  },
];

/**
 * 图标工具函数
 */
export const iconUtils = {
  /**
   * 根据名称获取图标信息
   */
  getIconInfo: (name: string): IconInfo | undefined => {
    return ALL_ICONS.find(icon => icon.name === name);
  },

  /**
   * 根据分类获取图标
   */
  getIconsByCategory: (category: string): IconInfo[] => {
    return ALL_ICONS.filter(icon => icon.category === category);
  },

  /**
   * 搜索图标
   */
  searchIcons: (query: string): IconInfo[] => {
    const lowerQuery = query.toLowerCase();
    return ALL_ICONS.filter(icon => 
      icon.name.toLowerCase().includes(lowerQuery) ||
      icon.label.toLowerCase().includes(lowerQuery) ||
      icon.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  },

  /**
   * 获取所有图标名称
   */
  getAllIconNames: (): string[] => {
    return ALL_ICONS.map(icon => icon.name);
  },

  /**
   * 检查图标是否存在
   */
  hasIcon: (name: string): boolean => {
    return ALL_ICONS.some(icon => icon.name === name);
  },

  /**
   * 获取随机图标
   */
  getRandomIcon: (): IconInfo => {
    const randomIndex = Math.floor(Math.random() * ALL_ICONS.length);
    return ALL_ICONS[randomIndex];
  },

  /**
   * 获取热门图标
   */
  getPopularIcons: (): IconInfo[] => {
    const popularNames = [
      'home', 'user', 'settings', 'search', 'heart', 'star',
      'plus', 'close', 'check', 'edit', 'delete', 'share'
    ];
    return ALL_ICONS.filter(icon => popularNames.includes(icon.name));
  },
};
