/**
 * 图标组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  color: currentColor;
  @include transition(all);

  // 可点击状态
  &--clickable {
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
      transform: scale(1.1);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }

  // 旋转动画
  &--spin {
    animation: icon-spin 1s linear infinite;
  }

  // 翻转效果
  &--flip-horizontal {
    transform: scaleX(-1);
  }

  &--flip-vertical {
    transform: scaleY(-1);
  }

  &--flip-both {
    transform: scale(-1);
  }

  // 尺寸变体
  &--xs {
    font-size: 12px;
    width: 12px;
    height: 12px;
  }

  &--sm {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  &--md {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  &--lg {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  &--xl {
    font-size: 32px;
    width: 32px;
    height: 32px;
  }

  &--2xl {
    font-size: 40px;
    width: 40px;
    height: 40px;
  }

  // 图标类型
  &--outline {
    font-weight: 400;
  }

  &--filled {
    font-weight: 600;
  }

  &--duotone {
    font-weight: 500;
    opacity: 0.8;
  }

  &--light {
    font-weight: 300;
    opacity: 0.7;
  }

  &--bold {
    font-weight: 700;
  }

  // 图标内容
  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  // 表情符号图标
  &__emoji {
    font-size: inherit;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // SVG 图标
  &__svg {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    svg {
      width: 100%;
      height: 100%;
      fill: currentColor;
    }
  }

  // 字体图标
  &__font {
    font-size: inherit;
    line-height: 1;
    font-style: normal;
    font-weight: inherit;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    text-align: center;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  // 图片图标
  &__image {
    width: 100%;
    height: 100%;
    display: block;
  }

  // 文本图标
  &__text {
    font-size: 0.6em;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
    text-align: center;
    color: currentColor;
  }

  // 徽章
  &__badge {
    position: absolute;
    top: -2px;
    right: -2px;
    min-width: 16px;
    height: 16px;
    padding: 0 4px;
    background-color: $color-danger;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }

  &__badge-text {
    font-size: 10px;
    font-weight: 600;
    color: $color-white;
    line-height: 1;
    white-space: nowrap;
  }

  // 带徽章的图标
  &--with-badge {
    .icon__badge {
      display: flex;
    }
  }

  // 暗色主题
  &--dark {
    .icon__badge {
      background-color: $color-danger-light;
    }
  }
}

// 旋转动画
@keyframes icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 图标组合样式
.icon-group {
  display: flex;
  align-items: center;
  gap: $spacing-2;

  .icon {
    flex-shrink: 0;
  }
}

// 图标按钮样式
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-2;
  border: none;
  background: transparent;
  border-radius: $border-radius-sm;
  cursor: pointer;
  @include transition(all);

  &:hover {
    background-color: rgba($color-primary, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }

  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .icon {
    pointer-events: none;
  }
}

// 图标列表样式
.icon-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-3;

  &--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
  }

  &__item {
    display: flex;
    align-items: center;
    gap: $spacing-3;
    padding: $spacing-2;
    border-radius: $border-radius-sm;
    @include transition(background-color);

    &:hover {
      background-color: $color-bg-secondary;
    }

    .icon {
      flex-shrink: 0;
    }
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-weight: 500;
    color: $color-text-primary;
    margin-bottom: $spacing-1;
  }

  &__description {
    font-size: $font-size-sm;
    color: $color-text-secondary;
    line-height: 1.4;
  }
}

// 响应式图标
@include respond-to(sm) {
  .icon--responsive {
    &.icon--xs { font-size: 14px; width: 14px; height: 14px; }
    &.icon--sm { font-size: 18px; width: 18px; height: 18px; }
    &.icon--md { font-size: 22px; width: 22px; height: 22px; }
    &.icon--lg { font-size: 28px; width: 28px; height: 28px; }
    &.icon--xl { font-size: 36px; width: 36px; height: 36px; }
    &.icon--2xl { font-size: 44px; width: 44px; height: 44px; }
  }
}

@include respond-to(md) {
  .icon--responsive {
    &.icon--xs { font-size: 16px; width: 16px; height: 16px; }
    &.icon--sm { font-size: 20px; width: 20px; height: 20px; }
    &.icon--md { font-size: 24px; width: 24px; height: 24px; }
    &.icon--lg { font-size: 32px; width: 32px; height: 32px; }
    &.icon--xl { font-size: 40px; width: 40px; height: 40px; }
    &.icon--2xl { font-size: 48px; width: 48px; height: 48px; }
  }
}
