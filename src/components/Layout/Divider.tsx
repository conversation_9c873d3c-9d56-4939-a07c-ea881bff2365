/**
 * 分割线组件
 * 用于分隔内容区域
 */

import React from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Divider.scss';

/**
 * 分割线类型
 */
export type DividerType = 'solid' | 'dashed' | 'dotted';

/**
 * 分割线方向
 */
export type DividerOrientation = 'horizontal' | 'vertical';

/**
 * 文本位置
 */
export type DividerTextPosition = 'left' | 'center' | 'right';

/**
 * 分割线属性
 */
export interface DividerProps {
  /**
   * 分割线类型
   */
  type?: DividerType;
  
  /**
   * 分割线方向
   */
  orientation?: DividerOrientation;
  
  /**
   * 分割线颜色
   */
  color?: string;
  
  /**
   * 分割线粗细
   */
  thickness?: number;
  
  /**
   * 边距
   */
  margin?: number | [number, number];
  
  /**
   * 文本内容
   */
  children?: React.ReactNode;
  
  /**
   * 文本位置
   */
  textPosition?: DividerTextPosition;
  
  /**
   * 是否为朴素样式
   */
  plain?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
}

/**
 * 分割线组件
 */
const Divider: React.FC<DividerProps> = ({
  type = 'solid',
  orientation = 'horizontal',
  color,
  thickness = 1,
  margin,
  children,
  textPosition = 'center',
  plain = false,
  className,
  style,
}) => {
  const { theme } = useTheme();

  // 构建类名
  const dividerClass = classNames(
    'divider',
    `divider--${orientation}`,
    `divider--${type}`,
    {
      'divider--with-text': !!children,
      [`divider--text-${textPosition}`]: !!children,
      'divider--plain': plain,
      'divider--dark': theme === 'dark',
    },
    className
  );

  // 构建样式
  const getDividerStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {};

    // 颜色
    if (color) {
      baseStyle.borderColor = color;
    }

    // 粗细
    if (orientation === 'horizontal') {
      baseStyle.borderTopWidth = `${thickness}px`;
    } else {
      baseStyle.borderLeftWidth = `${thickness}px`;
    }

    // 边距
    if (margin !== undefined) {
      if (typeof margin === 'number') {
        if (orientation === 'horizontal') {
          baseStyle.marginTop = `${margin}px`;
          baseStyle.marginBottom = `${margin}px`;
        } else {
          baseStyle.marginLeft = `${margin}px`;
          baseStyle.marginRight = `${margin}px`;
        }
      } else {
        const [vertical, horizontal] = margin;
        if (orientation === 'horizontal') {
          baseStyle.marginTop = `${vertical}px`;
          baseStyle.marginBottom = `${vertical}px`;
        } else {
          baseStyle.marginLeft = `${horizontal}px`;
          baseStyle.marginRight = `${horizontal}px`;
        }
      }
    }

    return {
      ...baseStyle,
      ...style,
    };
  };

  // 渲染水平分割线
  const renderHorizontalDivider = () => {
    if (!children) {
      return <View className={dividerClass} style={getDividerStyle()} />;
    }

    return (
      <View className={dividerClass} style={getDividerStyle()}>
        <View className="divider__line divider__line--left" />
        <View className="divider__text">
          {typeof children === 'string' ? (
            <Text className="divider__text-content">{children}</Text>
          ) : (
            children
          )}
        </View>
        <View className="divider__line divider__line--right" />
      </View>
    );
  };

  // 渲染垂直分割线
  const renderVerticalDivider = () => {
    if (!children) {
      return <View className={dividerClass} style={getDividerStyle()} />;
    }

    return (
      <View className={dividerClass} style={getDividerStyle()}>
        <View className="divider__line divider__line--top" />
        <View className="divider__text">
          {typeof children === 'string' ? (
            <Text className="divider__text-content">{children}</Text>
          ) : (
            children
          )}
        </View>
        <View className="divider__line divider__line--bottom" />
      </View>
    );
  };

  return orientation === 'horizontal' 
    ? renderHorizontalDivider() 
    : renderVerticalDivider();
};

export default Divider;
