/**
 * 间距组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.space {
  display: flex;
  box-sizing: border-box;

  // 水平方向
  &--horizontal {
    flex-direction: row;
  }

  // 垂直方向
  &--vertical {
    flex-direction: column;
  }

  // 对齐方式
  &--align-start {
    align-items: flex-start;
  }

  &--align-end {
    align-items: flex-end;
  }

  &--align-center {
    align-items: center;
  }

  &--align-baseline {
    align-items: baseline;
  }

  &--align-stretch {
    align-items: stretch;
  }

  // 换行
  &--wrap {
    flex-wrap: wrap;
  }

  // 子元素
  &__item {
    flex-shrink: 0;
  }

  // 分割线
  &__split {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // 水平方向的分割线
  &--horizontal &__split {
    height: 100%;
    align-items: center;
  }

  // 垂直方向的分割线
  &--vertical &__split {
    width: 100%;
    justify-content: center;
  }
}
