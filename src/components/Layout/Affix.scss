/**
 * 固定定位组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.affix {
  position: relative;
  box-sizing: border-box;
  @include transition(all);

  // 固定状态
  &--fixed {
    position: fixed;
    z-index: $z-index-fixed;
  }

  // 占位符
  &__placeholder {
    display: none;
  }

  // 当元素固定时，显示占位符
  &--fixed + &__placeholder {
    display: block;
  }

  // 暗色主题
  &--dark {
    // 可以在这里添加暗色主题特定的样式
  }
}
