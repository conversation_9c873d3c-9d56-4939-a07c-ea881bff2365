/**
 * 间距组件
 * 用于在元素之间添加一致的间距
 */

import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Space.scss';

/**
 * 间距尺寸
 */
export type SpaceSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;

/**
 * 间距方向
 */
export type SpaceDirection = 'horizontal' | 'vertical';

/**
 * 对齐方式
 */
export type SpaceAlign = 'start' | 'end' | 'center' | 'baseline' | 'stretch';

/**
 * 间距组件属性
 */
export interface SpaceProps {
  /**
   * 间距大小
   */
  size?: SpaceSize | [SpaceSize, SpaceSize];
  
  /**
   * 间距方向
   */
  direction?: SpaceDirection;
  
  /**
   * 对齐方式
   */
  align?: SpaceAlign;
  
  /**
   * 是否换行
   */
  wrap?: boolean;
  
  /**
   * 分割线
   */
  split?: React.ReactNode;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
}

/**
 * 获取间距值
 */
const getSpaceSize = (size: SpaceSize): number => {
  if (typeof size === 'number') {
    return size;
  }
  
  const sizeMap = {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  };
  
  return sizeMap[size] || 16;
};

/**
 * 间距组件
 */
const Space: React.FC<SpaceProps> = ({
  size = 'md',
  direction = 'horizontal',
  align = 'start',
  wrap = false,
  split,
  className,
  style,
  children,
}) => {
  const { theme } = useTheme();

  // 处理间距大小
  const [horizontalSize, verticalSize] = React.useMemo(() => {
    if (Array.isArray(size)) {
      return [getSpaceSize(size[0]), getSpaceSize(size[1])];
    }
    
    const spaceSize = getSpaceSize(size);
    return direction === 'horizontal' 
      ? [spaceSize, 0] 
      : [0, spaceSize];
  }, [size, direction]);

  // 过滤有效子元素
  const childrenArray = React.Children.toArray(children).filter(child => {
    return React.isValidElement(child) || (typeof child === 'string' && child.trim());
  });

  // 构建类名
  const spaceClass = classNames(
    'space',
    `space--${direction}`,
    `space--align-${align}`,
    {
      'space--wrap': wrap,
      'space--dark': theme === 'dark',
    },
    className
  );

  // 构建样式
  const spaceStyle: React.CSSProperties = {
    gap: direction === 'horizontal' 
      ? `${verticalSize}px ${horizontalSize}px`
      : `${verticalSize}px ${horizontalSize}px`,
    ...style,
  };

  // 如果没有子元素，返回 null
  if (childrenArray.length === 0) {
    return null;
  }

  // 如果有分割线，需要在子元素之间插入分割线
  const renderChildren = () => {
    if (!split) {
      return childrenArray;
    }

    const result: React.ReactNode[] = [];
    
    childrenArray.forEach((child, index) => {
      result.push(
        <View key={`child-${index}`} className="space__item">
          {child}
        </View>
      );
      
      // 在非最后一个元素后添加分割线
      if (index < childrenArray.length - 1) {
        result.push(
          <View key={`split-${index}`} className="space__split">
            {split}
          </View>
        );
      }
    });
    
    return result;
  };

  return (
    <View className={spaceClass} style={spaceStyle}>
      {renderChildren()}
    </View>
  );
};

export default Space;
