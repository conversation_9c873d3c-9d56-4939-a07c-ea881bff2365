/**
 * 容器组件
 * 提供响应式的内容容器，支持不同的最大宽度和内边距
 */

import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Container.scss';

/**
 * 容器尺寸
 */
export type ContainerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';

/**
 * 容器属性接口
 */
export interface ContainerProps {
  /**
   * 容器最大宽度
   */
  size?: ContainerSize;
  
  /**
   * 是否流式布局（无最大宽度限制）
   */
  fluid?: boolean;
  
  /**
   * 是否居中对齐
   */
  center?: boolean;
  
  /**
   * 内边距
   */
  padding?: boolean | 'none' | 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
  
  /**
   * 是否启用安全区域适配
   */
  safeArea?: boolean | 'top' | 'bottom' | 'left' | 'right' | 'horizontal' | 'vertical';
  
  /**
   * 背景色
   */
  background?: 'transparent' | 'primary' | 'secondary' | 'tertiary';
  
  /**
   * 是否启用阴影
   */
  shadow?: boolean | 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * 圆角
   */
  rounded?: boolean | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /**
   * 边框
   */
  border?: boolean | 'top' | 'bottom' | 'left' | 'right' | 'horizontal' | 'vertical';
}

/**
 * 容器组件
 */
const Container: React.FC<ContainerProps> = ({
  size = 'lg',
  fluid = false,
  center = true,
  padding = 'md',
  className,
  style,
  children,
  safeArea = false,
  background = 'transparent',
  shadow = false,
  rounded = false,
  border = false,
}) => {
  const { theme } = useTheme();

  // 构建类名
  const containerClass = classNames(
    'container',
    {
      // 尺寸
      [`container--${size}`]: !fluid && size,
      'container--fluid': fluid,
      
      // 对齐
      'container--center': center,
      
      // 内边距
      'container--no-padding': padding === 'none' || padding === false,
      [`container--padding-${padding}`]: padding && padding !== true && padding !== 'none',
      'container--padding': padding === true,
      
      // 安全区域
      'container--safe-area': safeArea === true,
      [`container--safe-area-${safeArea}`]: typeof safeArea === 'string',
      
      // 背景
      [`container--bg-${background}`]: background !== 'transparent',
      
      // 阴影
      'container--shadow': shadow === true,
      [`container--shadow-${shadow}`]: typeof shadow === 'string',
      
      // 圆角
      'container--rounded': rounded === true,
      [`container--rounded-${rounded}`]: typeof rounded === 'string',
      
      // 边框
      'container--border': border === true,
      [`container--border-${border}`]: typeof border === 'string',
      
      // 主题
      'container--dark': theme === 'dark',
    },
    className
  );

  return (
    <View className={containerClass} style={style}>
      {children}
    </View>
  );
};

export default Container;
