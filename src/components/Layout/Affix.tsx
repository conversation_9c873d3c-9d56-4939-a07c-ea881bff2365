/**
 * 固定定位组件
 * 将元素固定在视口的指定位置
 */

import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Affix.scss';

/**
 * 固定定位属性
 */
export interface AffixProps {
  /**
   * 距离顶部的偏移量
   */
  top?: number;
  
  /**
   * 距离底部的偏移量
   */
  bottom?: number;
  
  /**
   * 距离左侧的偏移量
   */
  left?: number;
  
  /**
   * 距离右侧的偏移量
   */
  right?: number;
  
  /**
   * z-index 层级
   */
  zIndex?: number;
  
  /**
   * 触发固定的滚动偏移量
   */
  offsetTop?: number;
  
  /**
   * 触发固定的底部滚动偏移量
   */
  offsetBottom?: number;
  
  /**
   * 目标容器（默认为 window）
   */
  target?: () => HTMLElement | Window;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
  
  /**
   * 固定状态改变回调
   */
  onChange?: (affixed: boolean) => void;
}

/**
 * 固定定位组件
 */
const Affix: React.FC<AffixProps> = ({
  top,
  bottom,
  left,
  right,
  zIndex = 1030,
  offsetTop = 0,
  offsetBottom,
  target,
  className,
  style,
  children,
  onChange,
}) => {
  const { theme } = useTheme();
  const [affixed, setAffixed] = React.useState(false);
  const [placeholderStyle, setPlaceholderStyle] = React.useState<React.CSSProperties>({});
  const elementRef = React.useRef<HTMLDivElement>(null);
  const placeholderRef = React.useRef<HTMLDivElement>(null);

  // 获取目标容器
  const getTarget = React.useCallback(() => {
    return target ? target() : window;
  }, [target]);

  // 计算固定状态
  const updateAffixStatus = React.useCallback(() => {
    if (!elementRef.current || !placeholderRef.current) return;

    const targetElement = getTarget();
    const element = elementRef.current;
    const placeholder = placeholderRef.current;
    
    const elementRect = element.getBoundingClientRect();
    const placeholderRect = placeholder.getBoundingClientRect();
    
    let shouldAffix = false;
    
    if (offsetTop !== undefined) {
      shouldAffix = placeholderRect.top <= offsetTop;
    } else if (offsetBottom !== undefined) {
      const targetHeight = targetElement === window 
        ? window.innerHeight 
        : (targetElement as HTMLElement).clientHeight;
      shouldAffix = placeholderRect.bottom >= targetHeight - offsetBottom;
    }

    if (shouldAffix !== affixed) {
      setAffixed(shouldAffix);
      onChange?.(shouldAffix);

      if (shouldAffix) {
        // 设置占位符样式
        setPlaceholderStyle({
          width: `${elementRect.width}px`,
          height: `${elementRect.height}px`,
        });
      } else {
        setPlaceholderStyle({});
      }
    }
  }, [affixed, offsetTop, offsetBottom, getTarget, onChange]);

  // 监听滚动事件
  React.useEffect(() => {
    const targetElement = getTarget();
    
    const handleScroll = () => {
      updateAffixStatus();
    };

    const handleResize = () => {
      updateAffixStatus();
    };

    targetElement.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
    
    // 初始检查
    updateAffixStatus();

    return () => {
      targetElement.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [updateAffixStatus, getTarget]);

  // 构建类名
  const affixClass = classNames(
    'affix',
    {
      'affix--fixed': affixed,
      'affix--dark': theme === 'dark',
    },
    className
  );

  // 构建固定样式
  const getAffixStyle = (): React.CSSProperties => {
    if (!affixed) {
      return style || {};
    }

    const fixedStyle: React.CSSProperties = {
      position: 'fixed',
      zIndex,
    };

    if (top !== undefined) {
      fixedStyle.top = `${top}px`;
    } else if (offsetTop !== undefined) {
      fixedStyle.top = `${offsetTop}px`;
    }

    if (bottom !== undefined) {
      fixedStyle.bottom = `${bottom}px`;
    } else if (offsetBottom !== undefined) {
      fixedStyle.bottom = `${offsetBottom}px`;
    }

    if (left !== undefined) {
      fixedStyle.left = `${left}px`;
    }

    if (right !== undefined) {
      fixedStyle.right = `${right}px`;
    }

    return {
      ...fixedStyle,
      ...style,
    };
  };

  return (
    <>
      {/* 占位符 */}
      <View 
        ref={placeholderRef}
        className="affix__placeholder"
        style={placeholderStyle}
      />
      
      {/* 固定元素 */}
      <View 
        ref={elementRef}
        className={affixClass}
        style={getAffixStyle()}
      >
        {children}
      </View>
    </>
  );
};

export default Affix;
