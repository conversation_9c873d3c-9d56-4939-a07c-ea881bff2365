/**
 * 网格系统组件
 * 提供灵活的网格布局系统，支持响应式设计
 */

import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Grid.scss';

/**
 * 断点类型
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * 列数类型
 */
export type ColSpan = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'auto' | 'full';

/**
 * 响应式列配置
 */
export interface ResponsiveColConfig {
  xs?: ColSpan;
  sm?: ColSpan;
  md?: ColSpan;
  lg?: ColSpan;
  xl?: ColSpan;
  '2xl'?: ColSpan;
}

/**
 * 行组件属性
 */
export interface RowProps {
  /**
   * 列间距
   */
  gutter?: number | [number, number] | {
    xs?: number | [number, number];
    sm?: number | [number, number];
    md?: number | [number, number];
    lg?: number | [number, number];
    xl?: number | [number, number];
    '2xl'?: number | [number, number];
  };

  /**
   * 水平对齐方式
   */
  justify?: 'start' | 'end' | 'center' | 'space-around' | 'space-between' | 'space-evenly';

  /**
   * 垂直对齐方式
   */
  align?: 'top' | 'middle' | 'bottom' | 'stretch';

  /**
   * 是否换行
   */
  wrap?: boolean;

  /**
   * 自定义类名
   */
  className?: string;

  /**
   * 自定义样式
   */
  style?: React.CSSProperties;

  /**
   * 子元素
   */
  children?: React.ReactNode;
}

/**
 * 列组件属性
 */
export interface ColProps {
  /**
   * 列宽度
   */
  span?: ColSpan;

  /**
   * 响应式列配置
   */
  responsive?: ResponsiveColConfig;

  /**
   * 列偏移
   */
  offset?: number;

  /**
   * 响应式偏移配置
   */
  responsiveOffset?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };

  /**
   * 列顺序
   */
  order?: number;

  /**
   * 响应式顺序配置
   */
  responsiveOrder?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };

  /**
   * 自定义类名
   */
  className?: string;

  /**
   * 自定义样式
   */
  style?: React.CSSProperties;

  /**
   * 子元素
   */
  children?: React.ReactNode;
}

/**
 * 行组件
 */
export const Row: React.FC<RowProps> = ({
  gutter = 0,
  justify = 'start',
  align = 'top',
  wrap = true,
  className,
  style,
  children,
}) => {
  const { theme } = useTheme();

  // 处理间距
  const getGutterStyle = () => {
    if (typeof gutter === 'number') {
      const halfGutter = gutter / 2;
      return {
        marginLeft: -halfGutter,
        marginRight: -halfGutter,
      };
    }

    if (Array.isArray(gutter)) {
      const [horizontal, vertical] = gutter;
      return {
        marginLeft: -horizontal / 2,
        marginRight: -horizontal / 2,
        marginTop: -vertical / 2,
        marginBottom: -vertical / 2,
      };
    }

    return {};
  };

  // 构建类名
  const rowClass = classNames(
    'row',
    {
      [`row--justify-${justify}`]: justify !== 'start',
      [`row--align-${align}`]: align !== 'top',
      'row--no-wrap': !wrap,
      'row--dark': theme === 'dark',
    },
    className
  );

  const rowStyle = {
    ...getGutterStyle(),
    ...style,
  };

  // 为子元素添加间距
  const childrenWithGutter = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === Col) {
      const gutterStyle: React.CSSProperties = {};

      if (typeof gutter === 'number') {
        gutterStyle.paddingLeft = gutter / 2;
        gutterStyle.paddingRight = gutter / 2;
      } else if (Array.isArray(gutter)) {
        const [horizontal, vertical] = gutter;
        gutterStyle.paddingLeft = horizontal / 2;
        gutterStyle.paddingRight = horizontal / 2;
        gutterStyle.paddingTop = vertical / 2;
        gutterStyle.paddingBottom = vertical / 2;
      }

      return React.cloneElement(child, {
        style: {
          ...gutterStyle,
          ...child.props.style,
        },
      });
    }

    return child;
  });

  return (
    <View className={rowClass} style={rowStyle}>
      {childrenWithGutter}
    </View>
  );
};

/**
 * 列组件
 */
export const Col: React.FC<ColProps> = ({
  span = 'auto',
  responsive,
  offset = 0,
  responsiveOffset,
  order,
  responsiveOrder,
  className,
  style,
  children,
}) => {
  const { theme } = useTheme();

  // 构建类名
  const colClass = classNames(
    'col',
    {
      // 基础列宽
      [`col--${span}`]: span !== 'auto',
      'col--auto': span === 'auto',

      // 偏移
      [`col--offset-${offset}`]: offset > 0,

      // 顺序
      [`col--order-${order}`]: order !== undefined,

      // 响应式列宽
      ...(responsive && Object.entries(responsive).reduce((acc, [breakpoint, colSpan]) => {
        acc[`col--${breakpoint}-${colSpan}`] = true;
        return acc;
      }, {} as Record<string, boolean>)),

      // 响应式偏移
      ...(responsiveOffset && Object.entries(responsiveOffset).reduce((acc, [breakpoint, offsetValue]) => {
        if (offsetValue > 0) {
          acc[`col--${breakpoint}-offset-${offsetValue}`] = true;
        }
        return acc;
      }, {} as Record<string, boolean>)),

      // 响应式顺序
      ...(responsiveOrder && Object.entries(responsiveOrder).reduce((acc, [breakpoint, orderValue]) => {
        if (orderValue !== undefined) {
          acc[`col--${breakpoint}-order-${orderValue}`] = true;
        }
        return acc;
      }, {} as Record<string, boolean>)),

      // 主题
      'col--dark': theme === 'dark',
    },
    className
  );

  return (
    <View className={colClass} style={style}>
      {children}
    </View>
  );
};

/**
 * 网格容器组件
 */
export interface GridProps {
  /**
   * 列数
   */
  cols?: number;

  /**
   * 响应式列数配置
   */
  responsiveCols?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };

  /**
   * 间距
   */
  gap?: number | [number, number];

  /**
   * 响应式间距配置
   */
  responsiveGap?: {
    xs?: number | [number, number];
    sm?: number | [number, number];
    md?: number | [number, number];
    lg?: number | [number, number];
    xl?: number | [number, number];
    '2xl'?: number | [number, number];
  };

  /**
   * 自定义类名
   */
  className?: string;

  /**
   * 自定义样式
   */
  style?: React.CSSProperties;

  /**
   * 子元素
   */
  children?: React.ReactNode;
}

/**
 * 网格容器组件
 */
export const Grid: React.FC<GridProps> = ({
  cols = 12,
  responsiveCols,
  gap = 0,
  responsiveGap,
  className,
  style,
  children,
}) => {
  const { theme } = useTheme();

  // 构建类名
  const gridClass = classNames(
    'grid',
    {
      [`grid--cols-${cols}`]: cols,

      // 响应式列数
      ...(responsiveCols && Object.entries(responsiveCols).reduce((acc, [breakpoint, colCount]) => {
        acc[`grid--${breakpoint}-cols-${colCount}`] = true;
        return acc;
      }, {} as Record<string, boolean>)),

      // 主题
      'grid--dark': theme === 'dark',
    },
    className
  );

  // 构建样式
  const getGapStyle = () => {
    if (typeof gap === 'number') {
      return { gap: `${gap}px` };
    }

    if (Array.isArray(gap)) {
      const [rowGap, colGap] = gap;
      return {
        rowGap: `${rowGap}px`,
        columnGap: `${colGap}px`,
      };
    }

    return {};
  };

  const gridStyle = {
    ...getGapStyle(),
    ...style,
  };

  return (
    <View className={gridClass} style={gridStyle}>
      {children}
    </View>
  );
};

// 单独导出
export { Row, Col, Grid };

// 默认导出
export default { Row, Col, Grid };
