/**
 * 粘性定位组件
 * 当元素滚动到指定位置时固定在屏幕上
 */

import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Sticky.scss';

/**
 * 粘性定位属性
 */
export interface StickyProps {
  /**
   * 距离顶部的偏移量
   */
  top?: number;
  
  /**
   * 距离底部的偏移量
   */
  bottom?: number;
  
  /**
   * 距离左侧的偏移量
   */
  left?: number;
  
  /**
   * 距离右侧的偏移量
   */
  right?: number;
  
  /**
   * z-index 层级
   */
  zIndex?: number;
  
  /**
   * 是否启用
   */
  enabled?: boolean;
  
  /**
   * 容器选择器（限制粘性范围）
   */
  container?: string;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
  
  /**
   * 粘性状态改变回调
   */
  onStickyChange?: (isSticky: boolean) => void;
}

/**
 * 粘性定位组件
 */
const Sticky: React.FC<StickyProps> = ({
  top,
  bottom,
  left,
  right,
  zIndex = 1020,
  enabled = true,
  container,
  className,
  style,
  children,
  onStickyChange,
}) => {
  const { theme } = useTheme();
  const [isSticky, setIsSticky] = React.useState(false);
  const elementRef = React.useRef<HTMLDivElement>(null);
  const observerRef = React.useRef<IntersectionObserver | null>(null);

  // 监听粘性状态变化
  React.useEffect(() => {
    if (!enabled || !elementRef.current) return;

    // 创建 Intersection Observer
    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        const newIsSticky = !entry.isIntersecting;
        
        if (newIsSticky !== isSticky) {
          setIsSticky(newIsSticky);
          onStickyChange?.(newIsSticky);
        }
      },
      {
        root: container ? document.querySelector(container) : null,
        rootMargin: `-${top || 0}px 0px -${bottom || 0}px 0px`,
        threshold: [0, 1],
      }
    );

    // 创建一个哨兵元素来检测粘性状态
    const sentinel = document.createElement('div');
    sentinel.style.position = 'absolute';
    sentinel.style.top = '0';
    sentinel.style.left = '0';
    sentinel.style.width = '1px';
    sentinel.style.height = '1px';
    sentinel.style.pointerEvents = 'none';
    sentinel.style.visibility = 'hidden';

    const parent = elementRef.current.parentElement;
    if (parent) {
      parent.insertBefore(sentinel, elementRef.current);
      observer.observe(sentinel);
    }

    observerRef.current = observer;

    return () => {
      observer.disconnect();
      if (sentinel.parentElement) {
        sentinel.parentElement.removeChild(sentinel);
      }
    };
  }, [enabled, top, bottom, container, isSticky, onStickyChange]);

  // 构建类名
  const stickyClass = classNames(
    'sticky',
    {
      'sticky--active': isSticky && enabled,
      'sticky--disabled': !enabled,
      'sticky--dark': theme === 'dark',
    },
    className
  );

  // 构建样式
  const getStickyStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      zIndex,
    };

    if (enabled) {
      baseStyle.position = 'sticky';
      
      if (top !== undefined) {
        baseStyle.top = `${top}px`;
      }
      
      if (bottom !== undefined) {
        baseStyle.bottom = `${bottom}px`;
      }
      
      if (left !== undefined) {
        baseStyle.left = `${left}px`;
      }
      
      if (right !== undefined) {
        baseStyle.right = `${right}px`;
      }
    }

    return {
      ...baseStyle,
      ...style,
    };
  };

  return (
    <View 
      ref={elementRef}
      className={stickyClass} 
      style={getStickyStyle()}
    >
      {children}
    </View>
  );
};

export default Sticky;
