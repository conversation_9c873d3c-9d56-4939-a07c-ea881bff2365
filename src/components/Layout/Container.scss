/**
 * 容器组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.container {
  width: 100%;
  box-sizing: border-box;

  // 居中对齐
  &--center {
    margin-left: auto;
    margin-right: auto;
  }

  // 流式布局
  &--fluid {
    max-width: none;
  }

  // 尺寸变体
  &--xs {
    max-width: 320px;
  }

  &--sm {
    max-width: 384px;
  }

  &--md {
    max-width: 448px;
  }

  &--lg {
    max-width: 512px;
  }

  &--xl {
    max-width: 576px;
  }

  &--2xl {
    max-width: 672px;
  }

  // 内边距变体
  &--padding {
    padding: $spacing-4;
  }

  &--padding-sm {
    padding: $spacing-2;
  }

  &--padding-md {
    padding: $spacing-4;
  }

  &--padding-lg {
    padding: $spacing-6;
  }

  &--padding-xl {
    padding: $spacing-8;
  }

  &--no-padding {
    padding: 0;
  }

  // 安全区域适配
  &--safe-area {
    @include safe-area-padding('all');
  }

  &--safe-area-top {
    @include safe-area-padding('top');
  }

  &--safe-area-bottom {
    @include safe-area-padding('bottom');
  }

  &--safe-area-left {
    @include safe-area-padding('left');
  }

  &--safe-area-right {
    @include safe-area-padding('right');
  }

  &--safe-area-horizontal {
    @include safe-area-padding('left');
    @include safe-area-padding('right');
  }

  &--safe-area-vertical {
    @include safe-area-padding('top');
    @include safe-area-padding('bottom');
  }

  // 背景变体
  &--bg-primary {
    background-color: var(--color-bg-primary);
  }

  &--bg-secondary {
    background-color: var(--color-bg-secondary);
  }

  &--bg-tertiary {
    background-color: var(--color-bg-tertiary);
  }

  // 阴影变体
  &--shadow {
    @include shadow('base');
  }

  &--shadow-sm {
    @include shadow('sm');
  }

  &--shadow-md {
    @include shadow('md');
  }

  &--shadow-lg {
    @include shadow('lg');
  }

  &--shadow-xl {
    @include shadow('xl');
  }

  // 圆角变体
  &--rounded {
    border-radius: $border-radius-base;
  }

  &--rounded-sm {
    border-radius: $border-radius-sm;
  }

  &--rounded-md {
    border-radius: $border-radius-md;
  }

  &--rounded-lg {
    border-radius: $border-radius-lg;
  }

  &--rounded-xl {
    border-radius: $border-radius-xl;
  }

  &--rounded-full {
    border-radius: $border-radius-full;
  }

  // 边框变体
  &--border {
    border: $border-width-base solid var(--color-border);
  }

  &--border-top {
    border-top: $border-width-base solid var(--color-border);
  }

  &--border-bottom {
    border-bottom: $border-width-base solid var(--color-border);
  }

  &--border-left {
    border-left: $border-width-base solid var(--color-border);
  }

  &--border-right {
    border-right: $border-width-base solid var(--color-border);
  }

  &--border-horizontal {
    border-left: $border-width-base solid var(--color-border);
    border-right: $border-width-base solid var(--color-border);
  }

  &--border-vertical {
    border-top: $border-width-base solid var(--color-border);
    border-bottom: $border-width-base solid var(--color-border);
  }

  // 响应式断点
  @include respond-to(sm) {
    &--xs {
      max-width: 540px;
    }

    &--sm {
      max-width: 540px;
    }

    &--md {
      max-width: 540px;
    }

    &--lg {
      max-width: 540px;
    }

    &--xl {
      max-width: 540px;
    }

    &--2xl {
      max-width: 540px;
    }

    &--padding-sm {
      padding: $spacing-3;
    }

    &--padding-md {
      padding: $spacing-6;
    }

    &--padding-lg {
      padding: $spacing-8;
    }

    &--padding-xl {
      padding: $spacing-10;
    }
  }

  @include respond-to(md) {
    &--md {
      max-width: 720px;
    }

    &--lg {
      max-width: 720px;
    }

    &--xl {
      max-width: 720px;
    }

    &--2xl {
      max-width: 720px;
    }
  }

  @include respond-to(lg) {
    &--lg {
      max-width: 960px;
    }

    &--xl {
      max-width: 960px;
    }

    &--2xl {
      max-width: 960px;
    }
  }

  @include respond-to(xl) {
    &--xl {
      max-width: 1140px;
    }

    &--2xl {
      max-width: 1140px;
    }
  }

  @include respond-to(2xl) {
    &--2xl {
      max-width: 1320px;
    }
  }

  // 暗色主题
  &--dark {
    &.container--bg-primary {
      background-color: $dark-color-bg-primary;
    }

    &.container--bg-secondary {
      background-color: $dark-color-bg-secondary;
    }

    &.container--bg-tertiary {
      background-color: $dark-color-bg-tertiary;
    }

    &.container--border {
      border-color: $dark-color-border;
    }

    &.container--border-top {
      border-top-color: $dark-color-border;
    }

    &.container--border-bottom {
      border-bottom-color: $dark-color-border;
    }

    &.container--border-left {
      border-left-color: $dark-color-border;
    }

    &.container--border-right {
      border-right-color: $dark-color-border;
    }

    &.container--border-horizontal {
      border-left-color: $dark-color-border;
      border-right-color: $dark-color-border;
    }

    &.container--border-vertical {
      border-top-color: $dark-color-border;
      border-bottom-color: $dark-color-border;
    }
  }
}
