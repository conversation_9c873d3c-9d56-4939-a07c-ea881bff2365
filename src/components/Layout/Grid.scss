/**
 * 网格系统样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

// ==================== 行组件 ====================

.row {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;

  // 水平对齐
  &--justify-start {
    justify-content: flex-start;
  }

  &--justify-end {
    justify-content: flex-end;
  }

  &--justify-center {
    justify-content: center;
  }

  &--justify-space-around {
    justify-content: space-around;
  }

  &--justify-space-between {
    justify-content: space-between;
  }

  &--justify-space-evenly {
    justify-content: space-evenly;
  }

  // 垂直对齐
  &--align-top {
    align-items: flex-start;
  }

  &--align-middle {
    align-items: center;
  }

  &--align-bottom {
    align-items: flex-end;
  }

  &--align-stretch {
    align-items: stretch;
  }

  // 换行控制
  &--no-wrap {
    flex-wrap: nowrap;
  }
}

// ==================== 列组件 ====================

.col {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  flex: 0 0 auto;

  // 自动宽度
  &--auto {
    flex: 1 1 0%;
    width: auto;
  }

  // 全宽
  &--full {
    flex: 0 0 100%;
    max-width: 100%;
  }

  // 固定列宽
  @for $i from 1 through 12 {
    &--#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }

  // 偏移
  @for $i from 1 through 11 {
    &--offset-#{$i} {
      margin-left: percentage($i / 12);
    }
  }

  // 顺序
  @for $i from 1 through 12 {
    &--order-#{$i} {
      order: $i;
    }
  }

  // 响应式列宽
  @include respond-to(xs) {
    &--xs-auto {
      flex: 1 1 0%;
      width: auto;
    }

    &--xs-full {
      flex: 0 0 100%;
      max-width: 100%;
    }

    @for $i from 1 through 12 {
      &--xs-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }

    @for $i from 1 through 11 {
      &--xs-offset-#{$i} {
        margin-left: percentage($i / 12);
      }
    }

    @for $i from 1 through 12 {
      &--xs-order-#{$i} {
        order: $i;
      }
    }
  }

  @include respond-to(sm) {
    &--sm-auto {
      flex: 1 1 0%;
      width: auto;
    }

    &--sm-full {
      flex: 0 0 100%;
      max-width: 100%;
    }

    @for $i from 1 through 12 {
      &--sm-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }

    @for $i from 1 through 11 {
      &--sm-offset-#{$i} {
        margin-left: percentage($i / 12);
      }
    }

    @for $i from 1 through 12 {
      &--sm-order-#{$i} {
        order: $i;
      }
    }
  }

  @include respond-to(md) {
    &--md-auto {
      flex: 1 1 0%;
      width: auto;
    }

    &--md-full {
      flex: 0 0 100%;
      max-width: 100%;
    }

    @for $i from 1 through 12 {
      &--md-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }

    @for $i from 1 through 11 {
      &--md-offset-#{$i} {
        margin-left: percentage($i / 12);
      }
    }

    @for $i from 1 through 12 {
      &--md-order-#{$i} {
        order: $i;
      }
    }
  }

  @include respond-to(lg) {
    &--lg-auto {
      flex: 1 1 0%;
      width: auto;
    }

    &--lg-full {
      flex: 0 0 100%;
      max-width: 100%;
    }

    @for $i from 1 through 12 {
      &--lg-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }

    @for $i from 1 through 11 {
      &--lg-offset-#{$i} {
        margin-left: percentage($i / 12);
      }
    }

    @for $i from 1 through 12 {
      &--lg-order-#{$i} {
        order: $i;
      }
    }
  }

  @include respond-to(xl) {
    &--xl-auto {
      flex: 1 1 0%;
      width: auto;
    }

    &--xl-full {
      flex: 0 0 100%;
      max-width: 100%;
    }

    @for $i from 1 through 12 {
      &--xl-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }

    @for $i from 1 through 11 {
      &--xl-offset-#{$i} {
        margin-left: percentage($i / 12);
      }
    }

    @for $i from 1 through 12 {
      &--xl-order-#{$i} {
        order: $i;
      }
    }
  }

  @include respond-to(2xl) {
    &--2xl-auto {
      flex: 1 1 0%;
      width: auto;
    }

    &--2xl-full {
      flex: 0 0 100%;
      max-width: 100%;
    }

    @for $i from 1 through 12 {
      &--2xl-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }

    @for $i from 1 through 11 {
      &--2xl-offset-#{$i} {
        margin-left: percentage($i / 12);
      }
    }

    @for $i from 1 through 12 {
      &--2xl-order-#{$i} {
        order: $i;
      }
    }
  }
}

// ==================== 网格容器 ====================

.grid {
  display: grid;
  box-sizing: border-box;

  // 基础列数
  @for $i from 1 through 12 {
    &--cols-#{$i} {
      grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
    }
  }

  // 响应式列数
  @include respond-to(xs) {
    @for $i from 1 through 12 {
      &--xs-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
      }
    }
  }

  @include respond-to(sm) {
    @for $i from 1 through 12 {
      &--sm-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
      }
    }
  }

  @include respond-to(md) {
    @for $i from 1 through 12 {
      &--md-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
      }
    }
  }

  @include respond-to(lg) {
    @for $i from 1 through 12 {
      &--lg-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
      }
    }
  }

  @include respond-to(xl) {
    @for $i from 1 through 12 {
      &--xl-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
      }
    }
  }

  @include respond-to(2xl) {
    @for $i from 1 through 12 {
      &--2xl-cols-#{$i} {
        grid-template-columns: repeat(#{$i}, minmax(0, 1fr));
      }
    }
  }
}
