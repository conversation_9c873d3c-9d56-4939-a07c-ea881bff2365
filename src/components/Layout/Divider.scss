/**
 * 分割线组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.divider {
  border: 0;
  box-sizing: border-box;

  // 水平分割线
  &--horizontal {
    width: 100%;
    border-top: 1px solid var(--color-border);
    margin: $spacing-4 0;

    &.divider--with-text {
      display: flex;
      align-items: center;
      margin: $spacing-6 0;
      border-top: none;
    }
  }

  // 垂直分割线
  &--vertical {
    height: 100%;
    border-left: 1px solid var(--color-border);
    margin: 0 $spacing-4;
    display: inline-block;

    &.divider--with-text {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
      margin: 0 $spacing-6;
      border-left: none;
    }
  }

  // 分割线类型
  &--solid {
    &.divider--horizontal {
      border-top-style: solid;
    }

    &.divider--vertical {
      border-left-style: solid;
    }

    .divider__line {
      border-style: solid;
    }
  }

  &--dashed {
    &.divider--horizontal {
      border-top-style: dashed;
    }

    &.divider--vertical {
      border-left-style: dashed;
    }

    .divider__line {
      border-style: dashed;
    }
  }

  &--dotted {
    &.divider--horizontal {
      border-top-style: dotted;
    }

    &.divider--vertical {
      border-left-style: dotted;
    }

    .divider__line {
      border-style: dotted;
    }
  }

  // 分割线
  &__line {
    border-color: var(--color-border);

    &--left,
    &--right {
      flex: 1;
      border-top: 1px solid;
    }

    &--top,
    &--bottom {
      flex: 1;
      border-left: 1px solid;
    }
  }

  // 文本
  &__text {
    padding: 0 $spacing-4;
    background-color: var(--color-bg-primary);
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;

    .divider--vertical & {
      padding: $spacing-4 0;
    }
  }

  &__text-content {
    font-size: $font-size-sm;
    color: var(--color-text-secondary);
    line-height: 1;
  }

  // 文本位置
  &--text-left {
    .divider__line--left {
      flex: 0 0 5%;
    }

    .divider__line--right {
      flex: 1;
    }
  }

  &--text-right {
    .divider__line--left {
      flex: 1;
    }

    .divider__line--right {
      flex: 0 0 5%;
    }
  }

  &--text-center {
    .divider__line--left,
    .divider__line--right {
      flex: 1;
    }
  }

  // 垂直分割线文本位置
  &--vertical.divider--text-left {
    .divider__line--top {
      flex: 0 0 5%;
    }

    .divider__line--bottom {
      flex: 1;
    }
  }

  &--vertical.divider--text-right {
    .divider__line--top {
      flex: 1;
    }

    .divider__line--bottom {
      flex: 0 0 5%;
    }
  }

  &--vertical.divider--text-center {
    .divider__line--top,
    .divider__line--bottom {
      flex: 1;
    }
  }

  // 朴素样式
  &--plain {
    .divider__text {
      background-color: transparent;
    }
  }

  // 暗色主题
  &--dark {
    &.divider--horizontal {
      border-top-color: $dark-color-border;
    }

    &.divider--vertical {
      border-left-color: $dark-color-border;
    }

    .divider__line {
      border-color: $dark-color-border;
    }

    .divider__text {
      background-color: $dark-color-bg-primary;
    }

    .divider__text-content {
      color: $dark-color-text-secondary;
    }

    &.divider--plain .divider__text {
      background-color: transparent;
    }
  }
}
