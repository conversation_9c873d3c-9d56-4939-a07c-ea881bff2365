/**
 * 布局组件入口文件
 * 统一导出所有布局相关组件
 */

// 容器组件
export { default as Container } from './Container';
export type { ContainerProps, ContainerSize } from './Container';

// 网格系统
export { Row, Col, Grid } from './Grid';
export type { 
  RowProps, 
  ColProps, 
  GridProps, 
  Breakpoint, 
  ColSpan, 
  ResponsiveColConfig 
} from './Grid';

// 间距组件
export { default as Space } from './Space';
export type { SpaceProps, SpaceSize, SpaceDirection } from './Space';

// 分割线组件
export { default as Divider } from './Divider';
export type { DividerProps, DividerType, DividerOrientation } from './Divider';

// 粘性定位组件
export { default as Sticky } from './Sticky';
export type { StickyProps } from './Sticky';

// 固定定位组件
export { default as Affix } from './Affix';
export type { AffixProps } from './Affix';

/**
 * 布局工具函数
 */
export const layoutUtils = {
  /**
   * 获取断点值
   */
  getBreakpointValue: (breakpoint: Breakpoint): number => {
    const breakpoints = {
      xs: 0,
      sm: 576,
      md: 768,
      lg: 992,
      xl: 1200,
      '2xl': 1400,
    };
    return breakpoints[breakpoint];
  },

  /**
   * 检查当前是否匹配断点
   */
  matchBreakpoint: (breakpoint: Breakpoint): boolean => {
    if (typeof window === 'undefined') return false;
    const breakpointValue = layoutUtils.getBreakpointValue(breakpoint);
    return window.innerWidth >= breakpointValue;
  },

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint: (): Breakpoint => {
    if (typeof window === 'undefined') return 'xs';
    
    const width = window.innerWidth;
    if (width >= 1400) return '2xl';
    if (width >= 1200) return 'xl';
    if (width >= 992) return 'lg';
    if (width >= 768) return 'md';
    if (width >= 576) return 'sm';
    return 'xs';
  },

  /**
   * 计算列宽度百分比
   */
  getColWidth: (span: ColSpan, total: number = 12): string => {
    if (span === 'auto') return 'auto';
    if (span === 'full') return '100%';
    return `${(Number(span) / total) * 100}%`;
  },

  /**
   * 生成响应式样式
   */
  generateResponsiveStyles: (
    config: Record<Breakpoint, any>,
    property: string
  ): Record<string, any> => {
    const styles: Record<string, any> = {};
    
    Object.entries(config).forEach(([breakpoint, value]) => {
      const bp = breakpoint as Breakpoint;
      const minWidth = layoutUtils.getBreakpointValue(bp);
      
      if (minWidth === 0) {
        styles[property] = value;
      } else {
        const mediaQuery = `@media (min-width: ${minWidth}px)`;
        if (!styles[mediaQuery]) {
          styles[mediaQuery] = {};
        }
        styles[mediaQuery][property] = value;
      }
    });
    
    return styles;
  },

  /**
   * 创建间距值
   */
  createSpacing: (value: number | string): string => {
    if (typeof value === 'string') return value;
    return `${value * 4}px`; // 基础单位为 4px
  },

  /**
   * 检查是否为移动端
   */
  isMobile: (): boolean => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth < 768;
  },

  /**
   * 检查是否为平板
   */
  isTablet: (): boolean => {
    if (typeof window === 'undefined') return false;
    const width = window.innerWidth;
    return width >= 768 && width < 1200;
  },

  /**
   * 检查是否为桌面端
   */
  isDesktop: (): boolean => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth >= 1200;
  },

  /**
   * 获取安全区域值
   */
  getSafeAreaInsets: () => {
    if (typeof window === 'undefined') {
      return { top: 0, right: 0, bottom: 0, left: 0 };
    }

    const style = getComputedStyle(document.documentElement);
    return {
      top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
      right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
      bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
      left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
    };
  },

  /**
   * 创建媒体查询钩子
   */
  useMediaQuery: (query: string): boolean => {
    if (typeof window === 'undefined') return false;
    
    try {
      const mediaQuery = window.matchMedia(query);
      return mediaQuery.matches;
    } catch {
      return false;
    }
  },

  /**
   * 创建断点钩子
   */
  useBreakpoint: (): {
    current: Breakpoint;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    matches: (breakpoint: Breakpoint) => boolean;
  } => {
    const current = layoutUtils.getCurrentBreakpoint();
    
    return {
      current,
      isMobile: layoutUtils.isMobile(),
      isTablet: layoutUtils.isTablet(),
      isDesktop: layoutUtils.isDesktop(),
      matches: (breakpoint: Breakpoint) => layoutUtils.matchBreakpoint(breakpoint),
    };
  },
};

/**
 * 布局常量
 */
export const LAYOUT_CONSTANTS = {
  // 断点值
  BREAKPOINTS: {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    '2xl': 1400,
  },
  
  // 容器最大宽度
  CONTAINER_MAX_WIDTHS: {
    sm: 540,
    md: 720,
    lg: 960,
    xl: 1140,
    '2xl': 1320,
  },
  
  // 网格列数
  GRID_COLUMNS: 12,
  
  // 基础间距单位
  SPACING_UNIT: 4,
  
  // 默认间距
  DEFAULT_GUTTER: 16,
  
  // Z-index 层级
  Z_INDEX: {
    sticky: 1020,
    affix: 1030,
    modal: 1050,
    tooltip: 1070,
    toast: 1080,
  },
} as const;
