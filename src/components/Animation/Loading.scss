/**
 * 加载动画组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.loading {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);

  // 布局方向
  &--vertical {
    flex-direction: column;
    
    .loading__text {
      margin-top: $spacing-2;
    }
    
    &.loading--text-top .loading__text {
      margin-top: 0;
      margin-bottom: $spacing-2;
    }
  }

  &--horizontal {
    flex-direction: row;
    
    .loading__text {
      margin-left: $spacing-2;
    }
    
    &.loading--text-left .loading__text {
      margin-left: 0;
      margin-right: $spacing-2;
    }
  }

  // 全屏显示
  &--fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-index-modal;
    background-color: rgba(255, 255, 255, 0.9);
  }

  // 遮罩
  &__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: -1;
  }

  // 内容容器
  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: inherit;
  }

  // 文本
  &__text {
    font-size: $font-size-sm;
    color: var(--color-text-secondary);
    white-space: nowrap;
  }

  // 尺寸变体
  &--xs {
    font-size: 12px;
    
    .loading__spinner,
    .loading__circle,
    .loading__ring,
    .loading__pulse,
    .loading__flip {
      width: 16px;
      height: 16px;
    }
  }

  &--sm {
    font-size: 14px;
    
    .loading__spinner,
    .loading__circle,
    .loading__ring,
    .loading__pulse,
    .loading__flip {
      width: 20px;
      height: 20px;
    }
  }

  &--md {
    font-size: 16px;
    
    .loading__spinner,
    .loading__circle,
    .loading__ring,
    .loading__pulse,
    .loading__flip {
      width: 24px;
      height: 24px;
    }
  }

  &--lg {
    font-size: 18px;
    
    .loading__spinner,
    .loading__circle,
    .loading__ring,
    .loading__pulse,
    .loading__flip {
      width: 32px;
      height: 32px;
    }
  }

  &--xl {
    font-size: 20px;
    
    .loading__spinner,
    .loading__circle,
    .loading__ring,
    .loading__pulse,
    .loading__flip {
      width: 40px;
      height: 40px;
    }
  }

  // ==================== 旋转器 ====================
  
  &__spinner {
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: loading-spin 1s linear infinite;
  }

  // ==================== 点动画 ====================
  
  &__dots {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &__dot {
    width: 6px;
    height: 6px;
    background-color: currentColor;
    border-radius: 50%;
    animation: loading-dots 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }

  // ==================== 脉冲动画 ====================
  
  &__pulse {
    width: 24px;
    height: 24px;
    background-color: currentColor;
    border-radius: 50%;
    animation: loading-pulse 1.5s ease-in-out infinite;
  }

  // ==================== 波浪动画 ====================
  
  &__wave {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  &__wave-bar {
    width: 3px;
    height: 20px;
    background-color: currentColor;
    animation: loading-wave 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -1.2s; }
    &:nth-child(2) { animation-delay: -1.1s; }
    &:nth-child(3) { animation-delay: -1.0s; }
    &:nth-child(4) { animation-delay: -0.9s; }
    &:nth-child(5) { animation-delay: -0.8s; }
  }

  // ==================== 条形动画 ====================
  
  &__bars {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &__bar {
    width: 4px;
    height: 20px;
    background-color: currentColor;
    animation: loading-bars 1s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -0.4s; }
    &:nth-child(2) { animation-delay: -0.2s; }
    &:nth-child(3) { animation-delay: 0s; }
  }

  // ==================== 圆形动画 ====================
  
  &__circle {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(currentColor, 0.2);
    border-left-color: currentColor;
    border-radius: 50%;
    animation: loading-spin 1s linear infinite;
  }

  // ==================== 环形动画 ====================
  
  &__ring {
    width: 24px;
    height: 24px;
    border: 3px solid transparent;
    border-top: 3px solid currentColor;
    border-bottom: 3px solid currentColor;
    border-radius: 50%;
    animation: loading-spin 2s linear infinite;
  }

  // ==================== 弹跳动画 ====================
  
  &__bounce {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  &__bounce-ball {
    width: 8px;
    height: 8px;
    background-color: currentColor;
    border-radius: 50%;
    animation: loading-bounce 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }

  // ==================== 淡入淡出动画 ====================
  
  &__fade {
    display: flex;
    align-items: center;
    gap: 2px;
  }

  &__fade-rect {
    width: 4px;
    height: 20px;
    background-color: currentColor;
    animation: loading-fade 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: -0.4s; }
    &:nth-child(2) { animation-delay: -0.3s; }
    &:nth-child(3) { animation-delay: -0.2s; }
    &:nth-child(4) { animation-delay: -0.1s; }
    &:nth-child(5) { animation-delay: 0s; }
  }

  // ==================== 翻转动画 ====================
  
  &__flip {
    width: 24px;
    height: 24px;
    background-color: currentColor;
    animation: loading-flip 2s ease-in-out infinite;
  }

  // 暗色主题
  &--dark {
    &.loading--fullscreen {
      background-color: rgba(0, 0, 0, 0.9);
    }

    .loading__mask {
      background-color: rgba(0, 0, 0, 0.8);
    }

    .loading__text {
      color: $dark-color-text-secondary;
    }
  }
}

// ==================== 动画关键帧 ====================

@keyframes loading-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-pulse {
  0%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes loading-wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

@keyframes loading-bars {
  0%, 40%, 100% {
    transform: scaleY(0.4);
    opacity: 0.5;
  }
  20% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes loading-fade {
  0%, 39%, 100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

@keyframes loading-flip {
  0%, 50%, 100% {
    transform: rotateY(0deg);
  }
  25% {
    transform: rotateY(180deg);
  }
  75% {
    transform: rotateY(180deg);
  }
}
