/**
 * 动画组件入口文件
 * 统一导出所有动画相关组件
 */

// 过渡动画组件
export { default as Transition } from './Transition';
export type { 
  TransitionProps, 
  TransitionType, 
  TransitionStatus 
} from './Transition';

// 加载动画组件
export { default as Loading } from './Loading';
export type { 
  LoadingProps, 
  LoadingType, 
  LoadingSize 
} from './Loading';

/**
 * 动画工具函数
 */
export const animationUtils = {
  /**
   * 创建缓动函数
   */
  createEasing: (type: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear' | 'bounce' | 'elastic'): string => {
    const easingMap = {
      ease: 'ease',
      'ease-in': 'ease-in',
      'ease-out': 'ease-out',
      'ease-in-out': 'ease-in-out',
      linear: 'linear',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    };
    return easingMap[type] || 'ease-in-out';
  },

  /**
   * 创建动画延迟
   */
  createDelay: (index: number, baseDelay: number = 100): number => {
    return index * baseDelay;
  },

  /**
   * 创建交错动画延迟
   */
  createStaggerDelay: (index: number, total: number, duration: number = 1000): number => {
    return (index / total) * duration;
  },

  /**
   * 检查是否支持动画
   */
  supportsAnimation: (): boolean => {
    if (typeof window === 'undefined') return false;
    
    const element = document.createElement('div');
    const properties = [
      'animation',
      'webkitAnimation',
      'mozAnimation',
      'oAnimation',
      'msAnimation',
    ];
    
    return properties.some(property => property in element.style);
  },

  /**
   * 检查是否支持过渡
   */
  supportsTransition: (): boolean => {
    if (typeof window === 'undefined') return false;
    
    const element = document.createElement('div');
    const properties = [
      'transition',
      'webkitTransition',
      'mozTransition',
      'oTransition',
      'msTransition',
    ];
    
    return properties.some(property => property in element.style);
  },

  /**
   * 检查用户是否偏好减少动画
   */
  prefersReducedMotion: (): boolean => {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * 获取动画持续时间
   */
  getAnimationDuration: (element: HTMLElement): number => {
    if (typeof window === 'undefined') return 0;
    
    const style = window.getComputedStyle(element);
    const duration = style.animationDuration || style.transitionDuration;
    
    if (!duration || duration === '0s') return 0;
    
    // 转换为毫秒
    return parseFloat(duration) * (duration.includes('ms') ? 1 : 1000);
  },

  /**
   * 等待动画完成
   */
  waitForAnimation: (element: HTMLElement): Promise<void> => {
    return new Promise((resolve) => {
      const duration = animationUtils.getAnimationDuration(element);
      
      if (duration === 0) {
        resolve();
        return;
      }
      
      const handleAnimationEnd = () => {
        element.removeEventListener('animationend', handleAnimationEnd);
        element.removeEventListener('transitionend', handleAnimationEnd);
        resolve();
      };
      
      element.addEventListener('animationend', handleAnimationEnd);
      element.addEventListener('transitionend', handleAnimationEnd);
      
      // 备用定时器
      setTimeout(resolve, duration + 50);
    });
  },

  /**
   * 创建关键帧动画
   */
  createKeyframes: (name: string, keyframes: Record<string, Record<string, string>>): string => {
    const keyframeRules = Object.entries(keyframes)
      .map(([percentage, styles]) => {
        const styleRules = Object.entries(styles)
          .map(([property, value]) => `${property}: ${value}`)
          .join('; ');
        return `${percentage} { ${styleRules} }`;
      })
      .join(' ');
    
    return `@keyframes ${name} { ${keyframeRules} }`;
  },

  /**
   * 应用动画到元素
   */
  applyAnimation: (
    element: HTMLElement,
    options: {
      name: string;
      duration?: number;
      delay?: number;
      easing?: string;
      fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
      iterationCount?: number | 'infinite';
    }
  ): void => {
    const {
      name,
      duration = 300,
      delay = 0,
      easing = 'ease-in-out',
      fillMode = 'both',
      iterationCount = 1,
    } = options;
    
    element.style.animationName = name;
    element.style.animationDuration = `${duration}ms`;
    element.style.animationDelay = `${delay}ms`;
    element.style.animationTimingFunction = easing;
    element.style.animationFillMode = fillMode;
    element.style.animationIterationCount = String(iterationCount);
  },

  /**
   * 移除动画
   */
  removeAnimation: (element: HTMLElement): void => {
    element.style.animation = '';
  },

  /**
   * 创建弹簧动画配置
   */
  createSpringConfig: (
    tension: number = 170,
    friction: number = 26,
    mass: number = 1
  ): string => {
    // 简化的弹簧动画近似
    const damping = friction / (2 * Math.sqrt(mass * tension));
    const frequency = Math.sqrt(tension / mass) / (2 * Math.PI);
    
    if (damping < 1) {
      // 欠阻尼
      const dampedFreq = frequency * Math.sqrt(1 - damping * damping);
      return `cubic-bezier(0.25, ${0.46 + damping * 0.3}, 0.45, ${0.94 - damping * 0.2})`;
    } else {
      // 过阻尼或临界阻尼
      return 'cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    }
  },
};

/**
 * 动画常量
 */
export const ANIMATION_CONSTANTS = {
  // 动画持续时间
  DURATION: {
    FAST: 150,
    BASE: 300,
    SLOW: 500,
    SLOWER: 800,
  },
  
  // 缓动函数
  EASING: {
    EASE: 'ease',
    EASE_IN: 'ease-in',
    EASE_OUT: 'ease-out',
    EASE_IN_OUT: 'ease-in-out',
    LINEAR: 'linear',
    BOUNCE: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    ELASTIC: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    BACK: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  // 动画延迟
  DELAY: {
    NONE: 0,
    SHORT: 100,
    MEDIUM: 200,
    LONG: 300,
  },
  
  // 交错动画
  STAGGER: {
    FAST: 50,
    BASE: 100,
    SLOW: 150,
  },
} as const;
