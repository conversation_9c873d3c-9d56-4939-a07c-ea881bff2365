/**
 * 过渡动画组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.transition {
  transition-property: all;
  
  // ==================== 淡入淡出 ====================
  
  &--fade {
    &-enter {
      opacity: 0;
    }
    
    &-enter-active {
      transition-property: opacity;
    }
    
    &-enter-to {
      opacity: 1;
    }
    
    &-exit {
      opacity: 1;
    }
    
    &-exit-active {
      transition-property: opacity;
    }
    
    &-exit-to {
      opacity: 0;
    }
  }
  
  // ==================== 滑动动画 ====================
  
  &--slide-up {
    &-enter {
      opacity: 0;
      transform: translateY(100%);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: translateY(0);
    }
    
    &-exit {
      opacity: 1;
      transform: translateY(0);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: translateY(-100%);
    }
  }
  
  &--slide-down {
    &-enter {
      opacity: 0;
      transform: translateY(-100%);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: translateY(0);
    }
    
    &-exit {
      opacity: 1;
      transform: translateY(0);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: translateY(100%);
    }
  }
  
  &--slide-left {
    &-enter {
      opacity: 0;
      transform: translateX(100%);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: translateX(0);
    }
    
    &-exit {
      opacity: 1;
      transform: translateX(0);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: translateX(-100%);
    }
  }
  
  &--slide-right {
    &-enter {
      opacity: 0;
      transform: translateX(-100%);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: translateX(0);
    }
    
    &-exit {
      opacity: 1;
      transform: translateX(0);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: translateX(100%);
    }
  }
  
  // ==================== 缩放动画 ====================
  
  &--zoom {
    &-enter {
      opacity: 0;
      transform: scale(0);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: scale(1);
    }
    
    &-exit {
      opacity: 1;
      transform: scale(1);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: scale(0);
    }
  }
  
  &--zoom-up {
    &-enter {
      opacity: 0;
      transform: scale(0) translateY(100%);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    
    &-exit {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: scale(0) translateY(-100%);
    }
  }
  
  &--zoom-down {
    &-enter {
      opacity: 0;
      transform: scale(0) translateY(-100%);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    
    &-exit {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: scale(0) translateY(100%);
    }
  }
  
  // ==================== 翻转动画 ====================
  
  &--flip-x {
    &-enter {
      opacity: 0;
      transform: perspective(400px) rotateX(90deg);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: perspective(400px) rotateX(0deg);
    }
    
    &-exit {
      opacity: 1;
      transform: perspective(400px) rotateX(0deg);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: perspective(400px) rotateX(-90deg);
    }
  }
  
  &--flip-y {
    &-enter {
      opacity: 0;
      transform: perspective(400px) rotateY(90deg);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
    }
    
    &-enter-to {
      opacity: 1;
      transform: perspective(400px) rotateY(0deg);
    }
    
    &-exit {
      opacity: 1;
      transform: perspective(400px) rotateY(0deg);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
    }
    
    &-exit-to {
      opacity: 0;
      transform: perspective(400px) rotateY(-90deg);
    }
  }
  
  // ==================== 弹跳动画 ====================
  
  &--bounce {
    &-enter {
      opacity: 0;
      transform: scale(0.3);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
      transition-timing-function: $ease-bounce;
    }
    
    &-enter-to {
      opacity: 1;
      transform: scale(1);
    }
    
    &-exit {
      opacity: 1;
      transform: scale(1);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
      transition-timing-function: $ease-bounce;
    }
    
    &-exit-to {
      opacity: 0;
      transform: scale(0.3);
    }
  }
  
  // ==================== 弹性动画 ====================
  
  &--elastic {
    &-enter {
      opacity: 0;
      transform: scale(0);
    }
    
    &-enter-active {
      transition-property: opacity, transform;
      transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
    
    &-enter-to {
      opacity: 1;
      transform: scale(1);
    }
    
    &-exit {
      opacity: 1;
      transform: scale(1);
    }
    
    &-exit-active {
      transition-property: opacity, transform;
      transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }
    
    &-exit-to {
      opacity: 0;
      transform: scale(0);
    }
  }
}
