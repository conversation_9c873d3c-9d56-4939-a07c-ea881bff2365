/**
 * 加载动画组件
 * 提供多种加载动画效果
 */

import React from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Loading.scss';

/**
 * 加载动画类型
 */
export type LoadingType = 
  | 'spinner' 
  | 'dots' 
  | 'pulse' 
  | 'wave' 
  | 'bars' 
  | 'circle' 
  | 'ring'
  | 'bounce'
  | 'fade'
  | 'flip';

/**
 * 加载动画尺寸
 */
export type LoadingSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

/**
 * 加载动画属性
 */
export interface LoadingProps {
  /**
   * 是否显示加载动画
   */
  loading?: boolean;
  
  /**
   * 动画类型
   */
  type?: LoadingType;
  
  /**
   * 动画尺寸
   */
  size?: LoadingSize;
  
  /**
   * 动画颜色
   */
  color?: string;
  
  /**
   * 加载文本
   */
  text?: string;
  
  /**
   * 文本位置
   */
  textPosition?: 'top' | 'bottom' | 'left' | 'right';
  
  /**
   * 是否垂直布局
   */
  vertical?: boolean;
  
  /**
   * 是否全屏显示
   */
  fullscreen?: boolean;
  
  /**
   * 背景遮罩
   */
  mask?: boolean;
  
  /**
   * 遮罩颜色
   */
  maskColor?: string;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子元素（当 loading 为 false 时显示）
   */
  children?: React.ReactNode;
}

/**
 * 加载动画组件
 */
const Loading: React.FC<LoadingProps> = ({
  loading = true,
  type = 'spinner',
  size = 'md',
  color,
  text,
  textPosition = 'bottom',
  vertical = false,
  fullscreen = false,
  mask = false,
  maskColor,
  className,
  style,
  children,
}) => {
  const { theme } = useTheme();

  // 如果不是加载状态且有子元素，显示子元素
  if (!loading && children) {
    return <>{children}</>;
  }

  // 如果不是加载状态且没有子元素，不显示任何内容
  if (!loading) {
    return null;
  }

  // 构建类名
  const loadingClass = classNames(
    'loading',
    `loading--${type}`,
    `loading--${size}`,
    {
      'loading--vertical': vertical || textPosition === 'top' || textPosition === 'bottom',
      'loading--horizontal': !vertical && (textPosition === 'left' || textPosition === 'right'),
      'loading--fullscreen': fullscreen,
      'loading--with-mask': mask,
      'loading--dark': theme === 'dark',
    },
    className
  );

  // 构建样式
  const loadingStyle: React.CSSProperties = {
    ...style,
  };

  if (color) {
    loadingStyle.color = color;
  }

  // 渲染加载动画
  const renderSpinner = () => {
    switch (type) {
      case 'spinner':
        return <View className="loading__spinner" />;
        
      case 'dots':
        return (
          <View className="loading__dots">
            <View className="loading__dot" />
            <View className="loading__dot" />
            <View className="loading__dot" />
          </View>
        );
        
      case 'pulse':
        return <View className="loading__pulse" />;
        
      case 'wave':
        return (
          <View className="loading__wave">
            <View className="loading__wave-bar" />
            <View className="loading__wave-bar" />
            <View className="loading__wave-bar" />
            <View className="loading__wave-bar" />
            <View className="loading__wave-bar" />
          </View>
        );
        
      case 'bars':
        return (
          <View className="loading__bars">
            <View className="loading__bar" />
            <View className="loading__bar" />
            <View className="loading__bar" />
          </View>
        );
        
      case 'circle':
        return <View className="loading__circle" />;
        
      case 'ring':
        return <View className="loading__ring" />;
        
      case 'bounce':
        return (
          <View className="loading__bounce">
            <View className="loading__bounce-ball" />
            <View className="loading__bounce-ball" />
            <View className="loading__bounce-ball" />
          </View>
        );
        
      case 'fade':
        return (
          <View className="loading__fade">
            <View className="loading__fade-rect" />
            <View className="loading__fade-rect" />
            <View className="loading__fade-rect" />
            <View className="loading__fade-rect" />
            <View className="loading__fade-rect" />
          </View>
        );
        
      case 'flip':
        return <View className="loading__flip" />;
        
      default:
        return <View className="loading__spinner" />;
    }
  };

  // 渲染文本
  const renderText = () => {
    if (!text) return null;
    
    return (
      <Text className="loading__text">
        {text}
      </Text>
    );
  };

  // 渲染内容
  const renderContent = () => {
    const spinner = renderSpinner();
    const textElement = renderText();
    
    if (!textElement) {
      return spinner;
    }
    
    switch (textPosition) {
      case 'top':
        return (
          <>
            {textElement}
            {spinner}
          </>
        );
      case 'left':
        return (
          <>
            {textElement}
            {spinner}
          </>
        );
      case 'right':
        return (
          <>
            {spinner}
            {textElement}
          </>
        );
      case 'bottom':
      default:
        return (
          <>
            {spinner}
            {textElement}
          </>
        );
    }
  };

  // 渲染遮罩
  const renderMask = () => {
    if (!mask) return null;
    
    const maskStyle: React.CSSProperties = {};
    if (maskColor) {
      maskStyle.backgroundColor = maskColor;
    }
    
    return <View className="loading__mask" style={maskStyle} />;
  };

  return (
    <View className={loadingClass} style={loadingStyle}>
      {renderMask()}
      <View className="loading__content">
        {renderContent()}
      </View>
    </View>
  );
};

export default Loading;
