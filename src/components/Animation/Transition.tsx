/**
 * 过渡动画组件
 * 为元素的进入和离开提供平滑的过渡效果
 */

import React from 'react';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './Transition.scss';

/**
 * 过渡类型
 */
export type TransitionType = 
  | 'fade' 
  | 'slide-up' 
  | 'slide-down' 
  | 'slide-left' 
  | 'slide-right'
  | 'zoom' 
  | 'zoom-up' 
  | 'zoom-down'
  | 'flip-x' 
  | 'flip-y'
  | 'bounce'
  | 'elastic'
  | 'custom';

/**
 * 过渡状态
 */
export type TransitionStatus = 'entering' | 'entered' | 'exiting' | 'exited';

/**
 * 过渡组件属性
 */
export interface TransitionProps {
  /**
   * 是否显示
   */
  visible?: boolean;
  
  /**
   * 过渡类型
   */
  type?: TransitionType;
  
  /**
   * 进入动画持续时间（毫秒）
   */
  enterDuration?: number;
  
  /**
   * 离开动画持续时间（毫秒）
   */
  exitDuration?: number;
  
  /**
   * 动画延迟（毫秒）
   */
  delay?: number;
  
  /**
   * 缓动函数
   */
  easing?: string;
  
  /**
   * 是否在离开后销毁元素
   */
  destroyOnExit?: boolean;
  
  /**
   * 是否在首次挂载时显示动画
   */
  appear?: boolean;
  
  /**
   * 自定义进入类名
   */
  enterClass?: string;
  
  /**
   * 自定义进入激活类名
   */
  enterActiveClass?: string;
  
  /**
   * 自定义进入完成类名
   */
  enterToClass?: string;
  
  /**
   * 自定义离开类名
   */
  exitClass?: string;
  
  /**
   * 自定义离开激活类名
   */
  exitActiveClass?: string;
  
  /**
   * 自定义离开完成类名
   */
  exitToClass?: string;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
  
  /**
   * 进入前回调
   */
  onBeforeEnter?: () => void;
  
  /**
   * 进入中回调
   */
  onEnter?: () => void;
  
  /**
   * 进入后回调
   */
  onAfterEnter?: () => void;
  
  /**
   * 离开前回调
   */
  onBeforeExit?: () => void;
  
  /**
   * 离开中回调
   */
  onExit?: () => void;
  
  /**
   * 离开后回调
   */
  onAfterExit?: () => void;
}

/**
 * 过渡动画组件
 */
const Transition: React.FC<TransitionProps> = ({
  visible = false,
  type = 'fade',
  enterDuration = 300,
  exitDuration = 300,
  delay = 0,
  easing = 'ease-in-out',
  destroyOnExit = false,
  appear = false,
  enterClass,
  enterActiveClass,
  enterToClass,
  exitClass,
  exitActiveClass,
  exitToClass,
  className,
  style,
  children,
  onBeforeEnter,
  onEnter,
  onAfterEnter,
  onBeforeExit,
  onExit,
  onAfterExit,
}) => {
  const { theme } = useTheme();
  const [status, setStatus] = React.useState<TransitionStatus>(
    visible ? 'entered' : 'exited'
  );
  const [shouldRender, setShouldRender] = React.useState(visible || !destroyOnExit);
  const elementRef = React.useRef<HTMLDivElement>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  // 清理定时器
  const clearTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  };

  // 执行进入动画
  const performEnter = React.useCallback(() => {
    if (!shouldRender) {
      setShouldRender(true);
    }

    clearTimer();
    
    // 进入前
    setStatus('entering');
    onBeforeEnter?.();
    
    // 延迟执行动画
    timeoutRef.current = setTimeout(() => {
      onEnter?.();
      
      // 动画完成后
      timeoutRef.current = setTimeout(() => {
        setStatus('entered');
        onAfterEnter?.();
      }, enterDuration);
    }, delay);
  }, [shouldRender, delay, enterDuration, onBeforeEnter, onEnter, onAfterEnter]);

  // 执行离开动画
  const performExit = React.useCallback(() => {
    clearTimer();
    
    // 离开前
    setStatus('exiting');
    onBeforeExit?.();
    
    // 延迟执行动画
    timeoutRef.current = setTimeout(() => {
      onExit?.();
      
      // 动画完成后
      timeoutRef.current = setTimeout(() => {
        setStatus('exited');
        onAfterExit?.();
        
        if (destroyOnExit) {
          setShouldRender(false);
        }
      }, exitDuration);
    }, delay);
  }, [delay, exitDuration, destroyOnExit, onBeforeExit, onExit, onAfterExit]);

  // 监听 visible 变化
  React.useEffect(() => {
    if (visible) {
      performEnter();
    } else {
      performExit();
    }
  }, [visible, performEnter, performExit]);

  // 首次挂载动画
  React.useEffect(() => {
    if (appear && visible) {
      performEnter();
    }
  }, [appear, visible, performEnter]);

  // 清理定时器
  React.useEffect(() => {
    return clearTimer;
  }, []);

  // 如果不需要渲染，返回 null
  if (!shouldRender) {
    return null;
  }

  // 获取动画类名
  const getAnimationClasses = () => {
    const baseClass = `transition--${type}`;
    
    switch (status) {
      case 'entering':
        return [
          baseClass,
          enterClass || `${baseClass}-enter`,
          enterActiveClass || `${baseClass}-enter-active`,
        ];
      case 'entered':
        return [
          baseClass,
          enterToClass || `${baseClass}-enter-to`,
        ];
      case 'exiting':
        return [
          baseClass,
          exitClass || `${baseClass}-exit`,
          exitActiveClass || `${baseClass}-exit-active`,
        ];
      case 'exited':
        return [
          baseClass,
          exitToClass || `${baseClass}-exit-to`,
        ];
      default:
        return [baseClass];
    }
  };

  // 构建类名
  const transitionClass = classNames(
    'transition',
    ...getAnimationClasses(),
    {
      'transition--dark': theme === 'dark',
    },
    className
  );

  // 构建样式
  const transitionStyle: React.CSSProperties = {
    transitionDuration: `${status === 'entering' ? enterDuration : exitDuration}ms`,
    transitionTimingFunction: easing,
    transitionDelay: `${delay}ms`,
    ...style,
  };

  return (
    <View 
      ref={elementRef}
      className={transitionClass} 
      style={transitionStyle}
    >
      {children}
    </View>
  );
};

export default Transition;
