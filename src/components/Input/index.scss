/**
 * 输入框组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.input {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;

  // 标签
  &__label {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: $font-size-sm;
    color: $color-text-secondary;
  }

  &__label-text {
    flex: 1;
  }

  &__label-required {
    color: $color-danger;
    margin-left: 2px;
  }

  // 输入框包装器
  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: $color-white;
    border: 1px solid $color-border;
    border-radius: $border-radius-base;
    transition: all 0.2s ease-in-out;
    overflow: hidden;

    &:hover {
      border-color: $color-primary-light;
    }
  }

  // 输入框控件
  &__control {
    flex: 1;
    padding: 0;
    border: none;
    background: transparent;
    font-size: $font-size-base;
    color: $color-text-primary;
    outline: none;
    resize: none;

    &::placeholder {
      color: $color-text-placeholder;
    }

    &--textarea {
      min-height: 3em;
      line-height: 1.5;
    }
  }

  // 前缀和后缀
  &__prefix,
  &__suffix {
    display: flex;
    align-items: center;
    color: $color-text-secondary;
  }

  &__prefix {
    padding-left: 12px;
  }

  &__suffix {
    padding-right: 12px;
  }

  // 图标
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    transition: color 0.2s ease-in-out;

    &:hover {
      color: $color-primary;
    }

    &--prefix {
      margin-right: 8px;
    }

    &--suffix {
      margin-left: 8px;
    }

    &--clear,
    &--password {
      margin-left: 4px;
      font-size: 14px;
      color: $color-text-secondary;

      &:hover {
        color: $color-text-primary;
      }
    }
  }

  // 帮助文本
  &__help {
    margin-top: 4px;
    font-size: $font-size-xs;
  }

  &__help-text {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }
  }

  // 尺寸变体
  &--small {
    .input__wrapper {
      min-height: 32px;
    }

    .input__control {
      padding: 6px 12px;
      font-size: $font-size-sm;
    }

    .input__prefix {
      padding-left: 8px;
    }

    .input__suffix {
      padding-right: 8px;
    }
  }

  &--medium {
    .input__wrapper {
      min-height: 40px;
    }

    .input__control {
      padding: 8px 12px;
      font-size: $font-size-base;
    }
  }

  &--large {
    .input__wrapper {
      min-height: 48px;
    }

    .input__control {
      padding: 12px 16px;
      font-size: $font-size-lg;
    }

    .input__prefix {
      padding-left: 16px;
    }

    .input__suffix {
      padding-right: 16px;
    }
  }

  // 状态变体
  &--focused {
    .input__wrapper {
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }
  }

  &--error {
    .input__wrapper {
      border-color: $color-danger;

      &:hover {
        border-color: $color-danger;
      }
    }

    &.input--focused {
      .input__wrapper {
        border-color: $color-danger;
        box-shadow: 0 0 0 2px rgba($color-danger, 0.2);
      }
    }
  }

  &--success {
    .input__wrapper {
      border-color: $color-success;

      &:hover {
        border-color: $color-success;
      }
    }

    &.input--focused {
      .input__wrapper {
        border-color: $color-success;
        box-shadow: 0 0 0 2px rgba($color-success, 0.2);
      }
    }
  }

  &--warning {
    .input__wrapper {
      border-color: $color-warning;

      &:hover {
        border-color: $color-warning;
      }
    }

    &.input--focused {
      .input__wrapper {
        border-color: $color-warning;
        box-shadow: 0 0 0 2px rgba($color-warning, 0.2);
      }
    }
  }

  // 禁用状态
  &--disabled {
    .input__wrapper {
      background-color: $color-gray-100;
      border-color: $color-border;
      cursor: not-allowed;

      &:hover {
        border-color: $color-border;
      }
    }

    .input__control {
      color: $color-text-disabled;
      cursor: not-allowed;

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    .input__icon {
      color: $color-text-disabled;
      cursor: not-allowed;

      &:hover {
        color: $color-text-disabled;
      }
    }
  }

  // 只读状态
  &--readonly {
    .input__wrapper {
      background-color: $color-gray-50;
      cursor: default;
    }

    .input__control {
      cursor: default;
    }
  }

  // 文本域
  &--textarea {
    .input__wrapper {
      align-items: flex-start;
    }

    .input__control {
      min-height: 3em;
      line-height: 1.5;
      resize: vertical;
    }

    .input__prefix,
    .input__suffix {
      align-items: flex-start;
      padding-top: 8px;
    }

    &.input--small {
      .input__prefix,
      .input__suffix {
        padding-top: 6px;
      }
    }

    &.input--large {
      .input__prefix,
      .input__suffix {
        padding-top: 12px;
      }
    }
  }

  // 暗色主题
  &--dark {
    .input__label {
      color: $color-gray-300;
    }

    .input__wrapper {
      background-color: $color-gray-800;
      border-color: $color-gray-700;

      &:hover {
        border-color: $color-primary-light;
      }
    }

    .input__control {
      color: $color-white;

      &::placeholder {
        color: $color-gray-400;
      }
    }

    .input__prefix,
    .input__suffix {
      color: $color-gray-400;
    }

    .input__help-text {
      color: $color-gray-400;

      &--error {
        color: $color-danger-light;
      }
    }

    &.input--disabled {
      .input__wrapper {
        background-color: $color-gray-900;
        border-color: $color-gray-800;
      }

      .input__control {
        color: $color-gray-600;

        &::placeholder {
          color: $color-gray-600;
        }
      }
    }

    &.input--readonly {
      .input__wrapper {
        background-color: $color-gray-900;
      }
    }
  }
}
