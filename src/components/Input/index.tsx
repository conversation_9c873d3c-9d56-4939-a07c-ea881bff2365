/**
 * 通用输入框组件
 * 支持多种类型、验证、格式化的输入框组件
 */

import React from 'react';
import { View, Input as TaroInput, Text, Textarea } from '@tarojs/components';
import { BaseEventOrig, InputProps as TaroInputProps } from '@tarojs/components/types/Input';
import { TextareaProps as TaroTextareaProps } from '@tarojs/components/types/Textarea';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './index.scss';

/**
 * 输入框类型
 */
export type InputType = 'text' | 'number' | 'password' | 'email' | 'tel' | 'url' | 'search' | 'textarea';

/**
 * 输入框尺寸
 */
export type InputSize = 'small' | 'medium' | 'large';

/**
 * 输入框状态
 */
export type InputStatus = 'default' | 'success' | 'warning' | 'error';

/**
 * 验证规则
 */
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: string) => boolean | string;
  message?: string;
}

/**
 * 输入框属性接口
 */
export interface InputProps {
  /**
   * 输入框类型
   */
  type?: InputType;
  
  /**
   * 输入框尺寸
   */
  size?: InputSize;
  
  /**
   * 输入框状态
   */
  status?: InputStatus;
  
  /**
   * 输入框值
   */
  value?: string;
  
  /**
   * 默认值
   */
  defaultValue?: string;
  
  /**
   * 占位符
   */
  placeholder?: string;
  
  /**
   * 是否禁用
   */
  disabled?: boolean;
  
  /**
   * 是否只读
   */
  readonly?: boolean;
  
  /**
   * 是否必填
   */
  required?: boolean;
  
  /**
   * 是否显示清除按钮
   */
  clearable?: boolean;
  
  /**
   * 是否显示密码切换按钮
   */
  showPassword?: boolean;
  
  /**
   * 最大长度
   */
  maxLength?: number;
  
  /**
   * 最小长度
   */
  minLength?: number;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 标签
   */
  label?: string;
  
  /**
   * 帮助文本
   */
  helpText?: string;
  
  /**
   * 错误信息
   */
  errorMessage?: string;
  
  /**
   * 前缀图标
   */
  prefixIcon?: React.ReactNode;
  
  /**
   * 后缀图标
   */
  suffixIcon?: React.ReactNode;
  
  /**
   * 前缀内容
   */
  prefix?: React.ReactNode;
  
  /**
   * 后缀内容
   */
  suffix?: React.ReactNode;
  
  /**
   * 验证规则
   */
  rules?: ValidationRule[];
  
  /**
   * 格式化函数
   */
  formatter?: (value: string) => string;
  
  /**
   * 解析函数
   */
  parser?: (value: string) => string;
  
  /**
   * 防抖时间（毫秒）
   */
  debounce?: number;
  
  /**
   * 是否自动获取焦点
   */
  autoFocus?: boolean;
  
  /**
   * 输入框行数（仅 textarea 类型）
   */
  rows?: number;
  
  /**
   * 是否自动调整高度（仅 textarea 类型）
   */
  autoHeight?: boolean;
  
  /**
   * 值变化事件
   */
  onChange?: (value: string, event?: BaseEventOrig<TaroInputProps.inputEventDetail>) => void;
  
  /**
   * 输入事件
   */
  onInput?: (value: string, event?: BaseEventOrig<TaroInputProps.inputEventDetail>) => void;
  
  /**
   * 获取焦点事件
   */
  onFocus?: (event?: BaseEventOrig<TaroInputProps.inputForceEventDetail>) => void;
  
  /**
   * 失去焦点事件
   */
  onBlur?: (event?: BaseEventOrig<TaroInputProps.inputValueEventDetail>) => void;
  
  /**
   * 确认事件
   */
  onConfirm?: (value: string, event?: BaseEventOrig<TaroInputProps.inputValueEventDetail>) => void;
  
  /**
   * 清除事件
   */
  onClear?: () => void;
  
  /**
   * 验证事件
   */
  onValidate?: (isValid: boolean, message?: string) => void;
}

/**
 * 防抖函数
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 输入框组件
 */
const Input: React.FC<InputProps> = ({
  type = 'text',
  size = 'medium',
  status = 'default',
  value,
  defaultValue = '',
  placeholder,
  disabled = false,
  readonly = false,
  required = false,
  clearable = false,
  showPassword = false,
  maxLength,
  minLength,
  className,
  style,
  label,
  helpText,
  errorMessage,
  prefixIcon,
  suffixIcon,
  prefix,
  suffix,
  rules = [],
  formatter,
  parser,
  debounce: debounceTime,
  autoFocus = false,
  rows = 3,
  autoHeight = false,
  onChange,
  onInput,
  onFocus,
  onBlur,
  onConfirm,
  onClear,
  onValidate,
}) => {
  const { theme } = useTheme();
  const [internalValue, setInternalValue] = React.useState(value || defaultValue);
  const [focused, setFocused] = React.useState(false);
  const [showPasswordText, setShowPasswordText] = React.useState(false);
  const [validationMessage, setValidationMessage] = React.useState('');

  // 是否受控组件
  const isControlled = value !== undefined;
  const currentValue = isControlled ? value : internalValue;

  // 防抖处理
  const debouncedOnChange = React.useMemo(() => {
    if (!onChange || !debounceTime) return onChange;
    return debounce(onChange, debounceTime);
  }, [onChange, debounceTime]);

  // 验证输入值
  const validateValue = React.useCallback((val: string) => {
    if (!rules.length) return { isValid: true, message: '' };

    for (const rule of rules) {
      // 必填验证
      if (rule.required && !val.trim()) {
        const message = rule.message || '此字段为必填项';
        return { isValid: false, message };
      }

      // 长度验证
      if (rule.min !== undefined && val.length < rule.min) {
        const message = rule.message || `最少输入${rule.min}个字符`;
        return { isValid: false, message };
      }

      if (rule.max !== undefined && val.length > rule.max) {
        const message = rule.message || `最多输入${rule.max}个字符`;
        return { isValid: false, message };
      }

      // 正则验证
      if (rule.pattern && !rule.pattern.test(val)) {
        const message = rule.message || '输入格式不正确';
        return { isValid: false, message };
      }

      // 自定义验证
      if (rule.validator) {
        const result = rule.validator(val);
        if (result !== true) {
          const message = typeof result === 'string' ? result : (rule.message || '验证失败');
          return { isValid: false, message };
        }
      }
    }

    return { isValid: true, message: '' };
  }, [rules]);

  // 处理值变化
  const handleChange = (val: string, event?: any) => {
    let newValue = val;

    // 解析处理
    if (parser) {
      newValue = parser(newValue);
    }

    // 格式化处理
    if (formatter) {
      newValue = formatter(newValue);
    }

    // 长度限制
    if (maxLength && newValue.length > maxLength) {
      newValue = newValue.slice(0, maxLength);
    }

    // 更新内部状态
    if (!isControlled) {
      setInternalValue(newValue);
    }

    // 验证
    const validation = validateValue(newValue);
    setValidationMessage(validation.message);
    onValidate?.(validation.isValid, validation.message);

    // 触发变化事件
    if (debouncedOnChange) {
      debouncedOnChange(newValue, event);
    } else {
      onChange?.(newValue, event);
    }
  };

  // 处理输入事件
  const handleInput = (event: BaseEventOrig<TaroInputProps.inputEventDetail>) => {
    const val = event.detail.value;
    onInput?.(val, event);
    
    if (!debounceTime) {
      handleChange(val, event);
    }
  };

  // 处理获取焦点
  const handleFocus = (event: BaseEventOrig<TaroInputProps.inputForceEventDetail>) => {
    setFocused(true);
    onFocus?.(event);
  };

  // 处理失去焦点
  const handleBlur = (event: BaseEventOrig<TaroInputProps.inputValueEventDetail>) => {
    setFocused(false);
    onBlur?.(event);
    
    // 失去焦点时进行验证
    const validation = validateValue(currentValue);
    setValidationMessage(validation.message);
    onValidate?.(validation.isValid, validation.message);
  };

  // 处理确认
  const handleConfirm = (event: BaseEventOrig<TaroInputProps.inputValueEventDetail>) => {
    onConfirm?.(event.detail.value, event);
  };

  // 处理清除
  const handleClear = () => {
    const newValue = '';
    
    if (!isControlled) {
      setInternalValue(newValue);
    }
    
    setValidationMessage('');
    onChange?.(newValue);
    onClear?.();
  };

  // 切换密码显示
  const togglePasswordVisibility = () => {
    setShowPasswordText(!showPasswordText);
  };

  // 获取当前状态
  const getCurrentStatus = () => {
    if (errorMessage || validationMessage) return 'error';
    return status;
  };

  // 构建类名
  const inputClass = classNames(
    'input',
    `input--${size}`,
    `input--${getCurrentStatus()}`,
    {
      'input--disabled': disabled,
      'input--readonly': readonly,
      'input--focused': focused,
      'input--dark': theme === 'dark',
      'input--textarea': type === 'textarea',
    },
    className
  );

  // 获取输入框类型
  const getInputType = () => {
    if (type === 'password' && showPasswordText) return 'text';
    if (type === 'textarea') return 'text';
    return type;
  };

  // 渲染前缀
  const renderPrefix = () => {
    if (!prefixIcon && !prefix) return null;
    
    return (
      <View className="input__prefix">
        {prefixIcon && (
          <View className="input__icon input__icon--prefix">
            {prefixIcon}
          </View>
        )}
        {prefix}
      </View>
    );
  };

  // 渲染后缀
  const renderSuffix = () => {
    const hasContent = suffixIcon || suffix || clearable || (type === 'password' && showPassword);
    if (!hasContent) return null;
    
    return (
      <View className="input__suffix">
        {suffix}
        {clearable && currentValue && !disabled && !readonly && (
          <View className="input__icon input__icon--clear" onClick={handleClear}>
            ×
          </View>
        )}
        {type === 'password' && showPassword && (
          <View className="input__icon input__icon--password" onClick={togglePasswordVisibility}>
            {showPasswordText ? '👁️' : '👁️‍🗨️'}
          </View>
        )}
        {suffixIcon && (
          <View className="input__icon input__icon--suffix">
            {suffixIcon}
          </View>
        )}
      </View>
    );
  };

  // 渲染输入框
  const renderInput = () => {
    const commonProps = {
      value: currentValue,
      placeholder,
      disabled,
      maxlength: maxLength,
      focus: autoFocus,
      onInput: handleInput,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onConfirm: handleConfirm,
    };

    if (type === 'textarea') {
      return (
        <Textarea
          {...commonProps}
          autoHeight={autoHeight}
          className="input__control input__control--textarea"
          style={{ minHeight: `${rows * 1.5}em` }}
        />
      );
    }

    return (
      <TaroInput
        {...commonProps}
        type={getInputType() as any}
        className="input__control"
      />
    );
  };

  return (
    <View className={inputClass} style={style}>
      {label && (
        <View className="input__label">
          <Text className="input__label-text">{label}</Text>
          {required && <Text className="input__label-required">*</Text>}
        </View>
      )}
      
      <View className="input__wrapper">
        {renderPrefix()}
        {renderInput()}
        {renderSuffix()}
      </View>
      
      {(helpText || errorMessage || validationMessage) && (
        <View className="input__help">
          <Text className={classNames('input__help-text', {
            'input__help-text--error': errorMessage || validationMessage,
          })}>
            {errorMessage || validationMessage || helpText}
          </Text>
        </View>
      )}
    </View>
  );
};

export default Input;
