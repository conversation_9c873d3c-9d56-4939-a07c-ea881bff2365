/**
 * 通用按钮组件
 * 支持多种样式、尺寸、状态的按钮组件
 */

import React from 'react';
import { View, Text } from '@tarojs/components';
import { ITouchEvent } from '@tarojs/components/types/common';
import classNames from 'classnames';
import { useTheme } from '../../stores/appStore';
import './index.scss';

/**
 * 按钮类型
 */
export type ButtonType = 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'link';

/**
 * 按钮尺寸
 */
export type ButtonSize = 'mini' | 'small' | 'medium' | 'large';

/**
 * 按钮形状
 */
export type ButtonShape = 'square' | 'round' | 'circle';

/**
 * 按钮属性接口
 */
export interface ButtonProps {
  /**
   * 按钮类型
   */
  type?: ButtonType;
  
  /**
   * 按钮尺寸
   */
  size?: ButtonSize;
  
  /**
   * 按钮形状
   */
  shape?: ButtonShape;
  
  /**
   * 是否禁用
   */
  disabled?: boolean;
  
  /**
   * 是否加载中
   */
  loading?: boolean;
  
  /**
   * 是否为块级元素
   */
  block?: boolean;
  
  /**
   * 是否为幽灵按钮（透明背景）
   */
  ghost?: boolean;
  
  /**
   * 是否为朴素按钮（无边框）
   */
  plain?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  
  /**
   * 按钮文本
   */
  children?: React.ReactNode;
  
  /**
   * 左侧图标
   */
  icon?: React.ReactNode;
  
  /**
   * 右侧图标
   */
  suffixIcon?: React.ReactNode;
  
  /**
   * 点击事件
   */
  onClick?: (event: ITouchEvent) => void;
  
  /**
   * 长按事件
   */
  onLongPress?: (event: ITouchEvent) => void;
  
  /**
   * 触摸开始事件
   */
  onTouchStart?: (event: ITouchEvent) => void;
  
  /**
   * 触摸结束事件
   */
  onTouchEnd?: (event: ITouchEvent) => void;
  
  /**
   * 防抖时间（毫秒）
   */
  debounce?: number;
  
  /**
   * 节流时间（毫秒）
   */
  throttle?: number;
  
  /**
   * 是否开启触觉反馈
   */
  hapticFeedback?: boolean;
  
  /**
   * 自定义加载图标
   */
  loadingIcon?: React.ReactNode;
  
  /**
   * 加载文本
   */
  loadingText?: string;
}

/**
 * 防抖函数
 */
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let lastCallTime = 0;
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCallTime >= wait) {
      lastCallTime = now;
      func(...args);
    }
  };
}

/**
 * 按钮组件
 */
const Button: React.FC<ButtonProps> = ({
  type = 'primary',
  size = 'medium',
  shape = 'square',
  disabled = false,
  loading = false,
  block = false,
  ghost = false,
  plain = false,
  className,
  style,
  children,
  icon,
  suffixIcon,
  onClick,
  onLongPress,
  onTouchStart,
  onTouchEnd,
  debounce: debounceTime,
  throttle: throttleTime,
  hapticFeedback = false,
  loadingIcon,
  loadingText,
}) => {
  const { theme } = useTheme();
  const [isPressed, setIsPressed] = React.useState(false);

  // 处理点击事件
  const handleClick = React.useMemo(() => {
    if (!onClick) return undefined;

    let handler = onClick;

    // 防抖处理
    if (debounceTime) {
      handler = debounce(handler, debounceTime);
    }

    // 节流处理
    if (throttleTime) {
      handler = throttle(handler, throttleTime);
    }

    return (event: ITouchEvent) => {
      if (disabled || loading) return;

      // 触觉反馈
      if (hapticFeedback && process.env.TARO_ENV !== 'h5') {
        try {
          // @ts-ignore
          Taro.vibrateShort();
        } catch (error) {
          console.warn('Haptic feedback failed:', error);
        }
      }

      handler(event);
    };
  }, [onClick, disabled, loading, debounceTime, throttleTime, hapticFeedback]);

  // 处理触摸开始
  const handleTouchStart = (event: ITouchEvent) => {
    if (disabled || loading) return;
    setIsPressed(true);
    onTouchStart?.(event);
  };

  // 处理触摸结束
  const handleTouchEnd = (event: ITouchEvent) => {
    setIsPressed(false);
    onTouchEnd?.(event);
  };

  // 处理长按
  const handleLongPress = (event: ITouchEvent) => {
    if (disabled || loading) return;
    onLongPress?.(event);
  };

  // 构建类名
  const buttonClass = classNames(
    'button',
    `button--${type}`,
    `button--${size}`,
    `button--${shape}`,
    {
      'button--disabled': disabled,
      'button--loading': loading,
      'button--block': block,
      'button--ghost': ghost,
      'button--plain': plain,
      'button--pressed': isPressed,
      'button--dark': theme === 'dark',
    },
    className
  );

  // 渲染加载图标
  const renderLoadingIcon = () => {
    if (loadingIcon) {
      return loadingIcon;
    }
    
    return (
      <View className="button__loading-icon">
        <View className="button__spinner" />
      </View>
    );
  };

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <>
          {renderLoadingIcon()}
          {loadingText && (
            <Text className="button__text">{loadingText}</Text>
          )}
        </>
      );
    }

    return (
      <>
        {icon && (
          <View className="button__icon button__icon--prefix">
            {icon}
          </View>
        )}
        {children && (
          <Text className="button__text">{children}</Text>
        )}
        {suffixIcon && (
          <View className="button__icon button__icon--suffix">
            {suffixIcon}
          </View>
        )}
      </>
    );
  };

  return (
    <View
      className={buttonClass}
      style={style}
      onClick={handleClick}
      onLongPress={handleLongPress}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      {renderContent()}
    </View>
  );
};

export default Button;
