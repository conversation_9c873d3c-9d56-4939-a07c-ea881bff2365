/**
 * Button 组件单元测试
 */

import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import Button from '../index';
import type { ButtonProps } from '../index';

describe('Button', () => {
  // 基础渲染测试
  describe('Rendering', () => {
    it('renders correctly with default props', () => {
      render(<Button>Click me</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('Click me');
      expect(button).toHaveClass('button', 'button--default', 'button--md');
    });

    it('renders with custom className', () => {
      render(<Button className="custom-button">Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-button');
    });

    it('renders with custom style', () => {
      const customStyle = { backgroundColor: 'red' };
      render(<Button style={customStyle}>Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveStyle('background-color: red');
    });
  });

  // 类型变体测试
  describe('Type variants', () => {
    const types: ButtonProps['type'][] = ['default', 'primary', 'secondary', 'danger', 'ghost'];

    types.forEach(type => {
      it(`renders ${type} type correctly`, () => {
        render(<Button type={type}>Button</Button>);
        
        const button = screen.getByRole('button');
        expect(button).toHaveClass(`button--${type}`);
      });
    });
  });

  // 尺寸变体测试
  describe('Size variants', () => {
    const sizes: ButtonProps['size'][] = ['sm', 'md', 'lg'];

    sizes.forEach(size => {
      it(`renders ${size} size correctly`, () => {
        render(<Button size={size}>Button</Button>);
        
        const button = screen.getByRole('button');
        expect(button).toHaveClass(`button--${size}`);
      });
    });
  });

  // 状态测试
  describe('States', () => {
    it('renders loading state correctly', () => {
      render(<Button loading>Loading Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--loading');
      expect(button.querySelector('.button__loading')).toBeInTheDocument();
    });

    it('renders disabled state correctly', () => {
      render(<Button disabled>Disabled Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--disabled');
      expect(button).toBeDisabled();
    });

    it('renders block button correctly', () => {
      render(<Button block>Block Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--block');
    });

    it('renders round button correctly', () => {
      render(<Button round>Round Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--round');
    });
  });

  // 事件处理测试
  describe('Event handling', () => {
    it('handles click events', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Click me</Button>);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not trigger click when disabled', () => {
      const handleClick = jest.fn();
      render(<Button disabled onClick={handleClick}>Disabled Button</Button>);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('does not trigger click when loading', () => {
      const handleClick = jest.fn();
      render(<Button loading onClick={handleClick}>Loading Button</Button>);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('passes event object to click handler', () => {
      const handleClick = jest.fn();
      render(<Button onClick={handleClick}>Click me</Button>);
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledWith(expect.any(Object));
    });
  });

  // 可访问性测试
  describe('Accessibility', () => {
    it('has correct role', () => {
      render(<Button>Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('supports aria-label', () => {
      render(<Button aria-label="Custom label">Button</Button>);
      
      const button = screen.getByLabelText('Custom label');
      expect(button).toBeInTheDocument();
    });

    it('supports aria-disabled when disabled', () => {
      render(<Button disabled>Disabled Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-disabled', 'true');
    });

    it('is focusable when not disabled', () => {
      render(<Button>Focusable Button</Button>);
      
      const button = screen.getByRole('button');
      button.focus();
      expect(button).toHaveFocus();
    });

    it('is not focusable when disabled', () => {
      render(<Button disabled>Disabled Button</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('tabindex', '-1');
    });
  });

  // 组合状态测试
  describe('Combined states', () => {
    it('renders primary loading button correctly', () => {
      render(<Button type="primary" loading>Primary Loading</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--primary', 'button--loading');
    });

    it('renders large disabled button correctly', () => {
      render(<Button size="lg" disabled>Large Disabled</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--lg', 'button--disabled');
    });

    it('renders danger block button correctly', () => {
      render(<Button type="danger" block>Danger Block</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('button--danger', 'button--block');
    });
  });

  // 边界情况测试
  describe('Edge cases', () => {
    it('renders without children', () => {
      render(<Button />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toBeEmptyDOMElement();
    });

    it('renders with null children', () => {
      render(<Button>{null}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('renders with undefined children', () => {
      render(<Button>{undefined}</Button>);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('renders with complex children', () => {
      render(
        <Button>
          <span>Icon</span>
          <span>Text</span>
        </Button>
      );
      
      const button = screen.getByRole('button');
      expect(button).toContainHTML('<span>Icon</span><span>Text</span>');
    });
  });

  // 性能测试
  describe('Performance', () => {
    it('does not re-render unnecessarily', () => {
      const renderSpy = jest.fn();
      
      function TestButton(props: ButtonProps) {
        renderSpy();
        return <Button {...props}>Test Button</Button>;
      }
      
      const { rerender } = render(<TestButton>Initial</TestButton>);
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // 相同 props 不应该重新渲染
      rerender(<TestButton>Initial</TestButton>);
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // 不同 props 应该重新渲染
      rerender(<TestButton type="primary">Initial</TestButton>);
      expect(renderSpy).toHaveBeenCalledTimes(2);
    });
  });

  // 快照测试
  describe('Snapshots', () => {
    it('matches snapshot for default button', () => {
      const { container } = render(<Button>Default Button</Button>);
      expect(container.firstChild).toMatchSnapshot();
    });

    it('matches snapshot for primary loading button', () => {
      const { container } = render(
        <Button type="primary" loading>Primary Loading</Button>
      );
      expect(container.firstChild).toMatchSnapshot();
    });

    it('matches snapshot for large disabled button', () => {
      const { container } = render(
        <Button size="lg" disabled>Large Disabled</Button>
      );
      expect(container.firstChild).toMatchSnapshot();
    });
  });
});
