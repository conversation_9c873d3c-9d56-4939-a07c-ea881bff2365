/**
 * 按钮组件样式
 */

@import '../../styles/variables.scss';
@import '../../styles/mixins.scss';

.button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
  outline: none;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  // 加载状态
  &--loading {
    cursor: not-allowed;
    pointer-events: none;
  }

  // 块级按钮
  &--block {
    display: flex;
    width: 100%;
  }

  // 按下状态
  &--pressed {
    transform: scale(0.95);
  }

  // 按钮类型样式
  &--primary {
    background-color: $color-primary;
    border-color: $color-primary;
    color: $color-white;

    &:hover {
      background-color: $color-primary-light;
      border-color: $color-primary-light;
    }

    &.button--ghost {
      background-color: transparent;
      color: $color-primary;

      &:hover {
        background-color: rgba($color-primary, 0.1);
      }
    }

    &.button--plain {
      background-color: transparent;
      border-color: transparent;
      color: $color-primary;

      &:hover {
        background-color: rgba($color-primary, 0.1);
      }
    }
  }

  &--secondary {
    background-color: $color-gray-100;
    border-color: $color-gray-300;
    color: $color-text-primary;

    &:hover {
      background-color: $color-gray-200;
      border-color: $color-gray-400;
    }

    &.button--ghost {
      background-color: transparent;
      color: $color-text-primary;

      &:hover {
        background-color: rgba($color-gray-500, 0.1);
      }
    }

    &.button--plain {
      background-color: transparent;
      border-color: transparent;
      color: $color-text-primary;

      &:hover {
        background-color: rgba($color-gray-500, 0.1);
      }
    }
  }

  &--success {
    background-color: $color-success;
    border-color: $color-success;
    color: $color-white;

    &:hover {
      background-color: $color-success-light;
      border-color: $color-success-light;
    }

    &.button--ghost {
      background-color: transparent;
      color: $color-success;

      &:hover {
        background-color: rgba($color-success, 0.1);
      }
    }

    &.button--plain {
      background-color: transparent;
      border-color: transparent;
      color: $color-success;

      &:hover {
        background-color: rgba($color-success, 0.1);
      }
    }
  }

  &--warning {
    background-color: $color-warning;
    border-color: $color-warning;
    color: $color-white;

    &:hover {
      background-color: $color-warning-light;
      border-color: $color-warning-light;
    }

    &.button--ghost {
      background-color: transparent;
      color: $color-warning;

      &:hover {
        background-color: rgba($color-warning, 0.1);
      }
    }

    &.button--plain {
      background-color: transparent;
      border-color: transparent;
      color: $color-warning;

      &:hover {
        background-color: rgba($color-warning, 0.1);
      }
    }
  }

  &--danger {
    background-color: $color-danger;
    border-color: $color-danger;
    color: $color-white;

    &:hover {
      background-color: $color-danger-light;
      border-color: $color-danger-light;
    }

    &.button--ghost {
      background-color: transparent;
      color: $color-danger;

      &:hover {
        background-color: rgba($color-danger, 0.1);
      }
    }

    &.button--plain {
      background-color: transparent;
      border-color: transparent;
      color: $color-danger;

      &:hover {
        background-color: rgba($color-danger, 0.1);
      }
    }
  }

  &--info {
    background-color: $color-info;
    border-color: $color-info;
    color: $color-white;

    &:hover {
      background-color: $color-info-light;
      border-color: $color-info-light;
    }

    &.button--ghost {
      background-color: transparent;
      color: $color-info;

      &:hover {
        background-color: rgba($color-info, 0.1);
      }
    }

    &.button--plain {
      background-color: transparent;
      border-color: transparent;
      color: $color-info;

      &:hover {
        background-color: rgba($color-info, 0.1);
      }
    }
  }

  &--text {
    background-color: transparent;
    border-color: transparent;
    color: $color-primary;

    &:hover {
      background-color: rgba($color-primary, 0.1);
    }
  }

  &--link {
    background-color: transparent;
    border-color: transparent;
    color: $color-primary;
    text-decoration: underline;

    &:hover {
      color: $color-primary-light;
    }
  }

  // 按钮尺寸
  &--mini {
    padding: 4px 8px;
    font-size: $font-size-sm;
    border-radius: $border-radius-sm;
    min-height: 24px;
  }

  &--small {
    padding: 6px 12px;
    font-size: $font-size-sm;
    border-radius: $border-radius-sm;
    min-height: 32px;
  }

  &--medium {
    padding: 8px 16px;
    font-size: $font-size-base;
    border-radius: $border-radius-base;
    min-height: 40px;
  }

  &--large {
    padding: 12px 24px;
    font-size: $font-size-lg;
    border-radius: $border-radius-lg;
    min-height: 48px;
  }

  // 按钮形状
  &--round {
    border-radius: 999px;
  }

  &--circle {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;

    &.button--mini {
      width: 24px;
      height: 24px;
    }

    &.button--small {
      width: 32px;
      height: 32px;
    }

    &.button--large {
      width: 48px;
      height: 48px;
    }
  }

  // 暗色主题
  &--dark {
    &.button--secondary {
      background-color: $color-gray-800;
      border-color: $color-gray-700;
      color: $color-white;

      &:hover {
        background-color: $color-gray-700;
        border-color: $color-gray-600;
      }
    }
  }

  // 按钮内容
  &__text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;

    &--prefix {
      margin-right: 4px;
    }

    &--suffix {
      margin-left: 4px;
    }
  }

  // 加载状态
  &__loading-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: button-spin 1s linear infinite;
  }
}

@keyframes button-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
