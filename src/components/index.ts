/**
 * 组件库入口文件
 * 统一导出所有组件
 */

// 基础组件
export { default as Button } from './Button';
export type { ButtonProps, ButtonType, ButtonSize, ButtonShape } from './Button';

export { default as Input } from './Input';
export type { InputProps, InputType, InputSize, InputStatus, ValidationRule } from './Input';

export { default as Modal } from './Modal';
export type { ModalProps, ModalSize, ModalPosition, AnimationType } from './Modal';

// 布局组件
export {
  Container,
  Row,
  Col,
  Grid,
  Space,
  Divider,
  Sticky,
  Affix,
  layoutUtils,
  LAYOUT_CONSTANTS
} from './Layout';
export type {
  ContainerProps,
  ContainerSize,
  RowProps,
  ColProps,
  GridProps,
  Breakpoint,
  ColSpan,
  ResponsiveColConfig,
  SpaceProps,
  SpaceSize,
  SpaceDirection,
  DividerProps,
  DividerType,
  DividerOrientation,
  StickyProps,
  AffixProps
} from './Layout';

// 动画组件
export {
  Transition,
  Loading,
  animationUtils,
  ANIMATION_CONSTANTS
} from './Animation';
export type {
  TransitionProps,
  TransitionType,
  TransitionStatus,
  LoadingProps,
  LoadingType,
  LoadingSize
} from './Animation';

// 图标组件
export { default as Icon } from './Icon';
export type { IconProps, IconSize, IconType } from './Icon';
export {
  ALL_ICONS,
  ICON_CATEGORIES,
  iconUtils,
  BASIC_ICONS,
  ARROW_ICONS,
  ACTION_ICONS,
  STATUS_ICONS,
  SECURITY_ICONS,
  TIME_ICONS,
  COMMUNICATION_ICONS,
  MEDIA_ICONS,
  FILE_ICONS,
  DEVICE_ICONS,
  NAVIGATION_ICONS
} from './Icon/icons';
export type { IconInfo, IconCategory } from './Icon/icons';

// 导航组件
// export { default as Navbar } from './Navbar';
// export { default as Tabbar } from './Tabbar';
// export { default as Breadcrumb } from './Breadcrumb';

// 数据展示组件
// export { default as Card } from './Card';
// export { default as List } from './List';
// export { default as Table } from './Table';
// export { default as Tag } from './Tag';
// export { default as Badge } from './Badge';
// export { default as Avatar } from './Avatar';

// 反馈组件
// export { default as Toast } from './Toast';
// export { default as Loading } from './Loading';
// export { default as Empty } from './Empty';
// export { default as Result } from './Result';

// 表单组件
// export { default as Form } from './Form';
// export { default as Select } from './Select';
// export { default as Checkbox } from './Checkbox';
// export { default as Radio } from './Radio';
// export { default as Switch } from './Switch';
// export { default as Slider } from './Slider';
// export { default as DatePicker } from './DatePicker';
// export { default as Upload } from './Upload';

// 其他组件
// export { default as Icon } from './Icon';
// export { default as Image } from './Image';
// export { default as Divider } from './Divider';
// export { default as Progress } from './Progress';
// export { default as Steps } from './Steps';
// export { default as Collapse } from './Collapse';
// export { default as Drawer } from './Drawer';
// export { default as ActionSheet } from './ActionSheet';
// export { default as Popup } from './Popup';
// export { default as Picker } from './Picker';
// export { default as Calendar } from './Calendar';
// export { default as Swiper } from './Swiper';
// export { default as PullRefresh } from './PullRefresh';
// export { default as InfiniteScroll } from './InfiniteScroll';
// export { default as BackTop } from './BackTop';
// export { default as Sticky } from './Sticky';
// export { default as Affix } from './Affix';

// 高级组件
// export { default as VirtualList } from './VirtualList';
// export { default as LazyLoad } from './LazyLoad';
// export { default as Waterfall } from './Waterfall';
// export { default as Chart } from './Chart';
// export { default as Map } from './Map';
// export { default as Video } from './Video';
// export { default as Audio } from './Audio';
// export { default as QRCode } from './QRCode';
// export { default as Barcode } from './Barcode';

// 工具组件
// export { default as Portal } from './Portal';
// export { default as Transition } from './Transition';
// export { default as Observer } from './Observer';
// export { default as ErrorBoundary } from './ErrorBoundary';
// export { default as ConfigProvider } from './ConfigProvider';

/**
 * 组件版本信息
 */
export const version = '1.0.0';

/**
 * 组件库配置
 */
export interface ComponentConfig {
  theme?: 'light' | 'dark';
  primaryColor?: string;
  borderRadius?: number;
  fontSize?: number;
  spacing?: number;
}

/**
 * 默认配置
 */
export const defaultConfig: ComponentConfig = {
  theme: 'light',
  primaryColor: '#007AFF',
  borderRadius: 8,
  fontSize: 14,
  spacing: 16,
};

/**
 * 组件库工具函数
 */
export const utils = {
  /**
   * 生成唯一 ID
   */
  generateId: (): string => {
    return `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * 合并类名
   */
  classNames: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(' ');
  },

  /**
   * 深度合并对象
   */
  deepMerge: <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
    const result = { ...target };

    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = utils.deepMerge(result[key] || {}, source[key] as any);
      } else {
        result[key] = source[key] as any;
      }
    }

    return result;
  },

  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void => {
    let lastCallTime = 0;
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallTime >= wait) {
        lastCallTime = now;
        func(...args);
      }
    };
  },

  /**
   * 格式化文件大小
   */
  formatFileSize: (bytes: number, decimals: number = 2): string => {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
  },

  /**
   * 获取文件扩展名
   */
  getFileExtension: (filename: string): string => {
    if (!filename) return '';
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex === -1 ? '' : filename.slice(lastDotIndex + 1).toLowerCase();
  },

  /**
   * 判断是否为移动端
   */
  isMobile: (): boolean => {
    if (typeof window === 'undefined') return false;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },

  /**
   * 获取设备像素比
   */
  getPixelRatio: (): number => {
    if (typeof window === 'undefined') return 1;
    return window.devicePixelRatio || 1;
  },

  /**
   * 获取视口尺寸
   */
  getViewportSize: (): { width: number; height: number } => {
    if (typeof window === 'undefined') {
      return { width: 375, height: 667 };
    }

    return {
      width: window.innerWidth || document.documentElement.clientWidth,
      height: window.innerHeight || document.documentElement.clientHeight,
    };
  },

  /**
   * 颜色工具
   */
  color: {
    /**
     * 十六进制转 RGB
     */
    hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      } : null;
    },

    /**
     * RGB 转十六进制
     */
    rgbToHex: (r: number, g: number, b: number): string => {
      return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
    },

    /**
     * 调整颜色亮度
     */
    adjustBrightness: (hex: string, percent: number): string => {
      const rgb = utils.color.hexToRgb(hex);
      if (!rgb) return hex;

      const adjust = (value: number) => {
        const adjusted = Math.round(value * (1 + percent / 100));
        return Math.max(0, Math.min(255, adjusted));
      };

      return utils.color.rgbToHex(
        adjust(rgb.r),
        adjust(rgb.g),
        adjust(rgb.b)
      );
    },
  },
};
