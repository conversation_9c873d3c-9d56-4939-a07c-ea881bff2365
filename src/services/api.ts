/**
 * API 服务层
 * 基于高级 HTTP 客户端的 API 服务封装
 */

import { http, HttpConfig, HttpResponse } from '../utils/http';
import { useUserStore } from '../stores/userStore';
import { useNotificationStore } from '../stores/notificationStore';

/**
 * 用户相关 API
 */
export const userApi = {
  /**
   * 用户登录
   */
  login: (data: {
    username: string;
    password: string;
    captcha?: string;
  }): Promise<HttpResponse<{
    token: string;
    refreshToken: string;
    userInfo: UserInfo;
    permissions: string[];
    roles: string[];
  }>> => {
    return http.post('/auth/login', data, {
      loading: true,
      silent: false,
    });
  },

  /**
   * 用户注册
   */
  register: (data: {
    username: string;
    password: string;
    email?: string;
    phone?: string;
    captcha: string;
  }): Promise<HttpResponse<{
    token: string;
    userInfo: UserInfo;
  }>> => {
    return http.post('/auth/register', data);
  },

  /**
   * 刷新令牌
   */
  refreshToken: (refreshToken: string): Promise<HttpResponse<{
    token: string;
    refreshToken: string;
  }>> => {
    return http.post('/auth/refresh', { refreshToken }, {
      silent: true,
      loading: false,
    });
  },

  /**
   * 用户登出
   */
  logout: (): Promise<HttpResponse<void>> => {
    return http.post('/auth/logout', {}, {
      silent: true,
      loading: false,
    });
  },

  /**
   * 获取用户信息
   */
  getUserInfo: (): Promise<HttpResponse<UserInfo>> => {
    return http.get('/user/info', {}, {
      cache: 300, // 缓存5分钟
    });
  },

  /**
   * 更新用户信息
   */
  updateUserInfo: (data: Partial<UserInfo>): Promise<HttpResponse<UserInfo>> => {
    return http.put('/user/info', data);
  },

  /**
   * 修改密码
   */
  changePassword: (data: {
    oldPassword: string;
    newPassword: string;
  }): Promise<HttpResponse<void>> => {
    return http.post('/user/change-password', data);
  },

  /**
   * 上传头像
   */
  uploadAvatar: (filePath: string): Promise<HttpResponse<{ url: string }>> => {
    return http.upload({
      url: '/user/avatar',
      filePath,
      name: 'avatar',
      onProgress: (progress) => {
        console.log('Avatar upload progress:', progress);
      },
    });
  },

  /**
   * 获取用户列表
   */
  getUserList: (params: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  } = {}): Promise<HttpResponse<{
    list: UserInfo[];
    total: number;
    page: number;
    pageSize: number;
  }>> => {
    return http.get('/user/list', params, {
      cache: 60, // 缓存1分钟
    });
  },
};

/**
 * 文件相关 API
 */
export const fileApi = {
  /**
   * 上传文件
   */
  upload: (
    filePath: string,
    options: {
      category?: string;
      onProgress?: (progress: number) => void;
    } = {}
  ): Promise<HttpResponse<{
    url: string;
    filename: string;
    size: number;
    type: string;
  }>> => {
    return http.upload({
      url: '/file/upload',
      filePath,
      name: 'file',
      formData: {
        category: options.category || 'general',
      },
      onProgress: options.onProgress,
    });
  },

  /**
   * 批量上传文件
   */
  batchUpload: async (
    filePaths: string[],
    options: {
      category?: string;
      onProgress?: (progress: number) => void;
      onItemProgress?: (index: number, progress: number) => void;
    } = {}
  ): Promise<HttpResponse<Array<{
    url: string;
    filename: string;
    size: number;
    type: string;
  }>>> => {
    const results = [];
    let totalProgress = 0;

    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];

      try {
        const result = await fileApi.upload(filePath, {
          category: options.category,
          onProgress: (progress) => {
            if (options.onItemProgress) {
              options.onItemProgress(i, progress);
            }

            const currentProgress = (i * 100 + progress) / filePaths.length;
            if (options.onProgress) {
              options.onProgress(Math.round(currentProgress));
            }
          },
        });

        results.push(result.data);
      } catch (error) {
        console.error(`Failed to upload file ${i}:`, error);
        throw error;
      }
    }

    return {
      code: 0,
      message: 'success',
      data: results,
    };
  },

  /**
   * 删除文件
   */
  delete: (url: string): Promise<HttpResponse<void>> => {
    return http.delete('/file/delete', {
      data: { url },
    });
  },

  /**
   * 获取文件列表
   */
  getFileList: (params: {
    page?: number;
    pageSize?: number;
    category?: string;
    type?: string;
  } = {}): Promise<HttpResponse<{
    list: Array<{
      id: string;
      url: string;
      filename: string;
      size: number;
      type: string;
      category: string;
      createTime: string;
    }>;
    total: number;
  }>> => {
    return http.get('/file/list', params);
  },
};

/**
 * 系统相关 API
 */
export const systemApi = {
  /**
   * 获取系统配置
   */
  getConfig: (): Promise<HttpResponse<{
    version: string;
    features: Record<string, boolean>;
    limits: Record<string, number>;
  }>> => {
    return http.get('/system/config', {}, {
      cache: 600, // 缓存10分钟
    });
  },

  /**
   * 获取验证码
   */
  getCaptcha: (): Promise<HttpResponse<{
    image: string;
    key: string;
  }>> => {
    return http.get('/system/captcha', {}, {
      cache: false,
      loading: false,
    });
  },

  /**
   * 发送短信验证码
   */
  sendSmsCode: (phone: string): Promise<HttpResponse<void>> => {
    return http.post('/system/sms-code', { phone }, {
      throttle: 60000, // 60秒内只能发送一次
    });
  },

  /**
   * 意见反馈
   */
  feedback: (data: {
    type: string;
    content: string;
    contact?: string;
    images?: string[];
  }): Promise<HttpResponse<void>> => {
    return http.post('/system/feedback', data);
  },

  /**
   * 检查更新
   */
  checkUpdate: (): Promise<HttpResponse<{
    hasUpdate: boolean;
    version?: string;
    description?: string;
    downloadUrl?: string;
    forceUpdate?: boolean;
  }>> => {
    return http.get('/system/check-update', {}, {
      cache: 300, // 缓存5分钟
      silent: true,
    });
  },

  /**
   * 上报错误
   */
  reportError: (data: {
    message: string;
    stack?: string;
    url?: string;
    userAgent?: string;
    timestamp: number;
    userId?: string;
  }): Promise<HttpResponse<void>> => {
    return http.post('/system/error-report', data, {
      silent: true,
      loading: false,
      retry: 1,
    });
  },

  /**
   * 统计埋点
   */
  track: (data: {
    event: string;
    properties?: Record<string, any>;
    timestamp: number;
    userId?: string;
  }): Promise<HttpResponse<void>> => {
    return http.post('/system/track', data, {
      silent: true,
      loading: false,
      retry: 0,
      debounce: 1000, // 防抖1秒
    });
  },
};

/**
 * 通用 API 工具
 */
export const apiUtils = {
  /**
   * 批量请求
   */
  batch: async <T>(
    requests: Array<() => Promise<HttpResponse<T>>>
  ): Promise<Array<HttpResponse<T> | Error>> => {
    const results = await Promise.allSettled(
      requests.map(request => request())
    );

    return results.map(result =>
      result.status === 'fulfilled' ? result.value : result.reason
    );
  },

  /**
   * 重试请求
   */
  retry: async <T>(
    request: () => Promise<HttpResponse<T>>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<HttpResponse<T>> => {
    let lastError: Error;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await request();
      } catch (error) {
        lastError = error as Error;

        if (i < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
        }
      }
    }

    throw lastError!;
  },

  /**
   * 轮询请求
   */
  poll: <T>(
    request: () => Promise<HttpResponse<T>>,
    condition: (data: T) => boolean,
    options: {
      interval?: number;
      maxAttempts?: number;
      onProgress?: (attempt: number) => void;
    } = {}
  ): Promise<HttpResponse<T>> => {
    const { interval = 1000, maxAttempts = 10, onProgress } = options;

    return new Promise(async (resolve, reject) => {
      let attempts = 0;

      const poll = async () => {
        attempts++;

        if (onProgress) {
          onProgress(attempts);
        }

        try {
          const response = await request();

          if (condition(response.data)) {
            resolve(response);
            return;
          }

          if (attempts >= maxAttempts) {
            reject(new Error('Polling max attempts reached'));
            return;
          }

          setTimeout(poll, interval);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  },
};
