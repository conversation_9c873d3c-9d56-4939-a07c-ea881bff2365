/**
 * Zustand 中间件
 * 提供通用的状态管理中间件
 */

import { StateCreator } from 'zustand';
import { persist, createJSONStorage, PersistOptions } from 'zustand/middleware';
import { subscribeWithSelector } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';
import Taro from '@tarojs/taro';
import { logError } from '../utils/error';
import { debug } from '../utils/debug';

/**
 * 创建 Taro 存储适配器
 */
export function createTaroStorage() {
  return createJSONStorage(() => ({
    getItem: (key: string) => {
      try {
        return Taro.getStorageSync(key);
      } catch (error) {
        logError(error as Error, { context: 'TaroStorage.getItem', key });
        return null;
      }
    },
    setItem: (key: string, value: string) => {
      try {
        Taro.setStorageSync(key, value);
      } catch (error) {
        logError(error as Error, { context: 'TaroStorage.setItem', key });
      }
    },
    removeItem: (key: string) => {
      try {
        Taro.removeStorageSync(key);
      } catch (error) {
        logError(error as Error, { context: 'TaroStorage.removeItem', key });
      }
    },
  }));
}

/**
 * 日志中间件
 */
export const logger = <T>(
  config: StateCreator<T, [], [], T>,
  name?: string
): StateCreator<T, [], [], T> => (set, get, api) =>
  config(
    (...args) => {
      const prevState = get();
      set(...args);
      const nextState = get();
      
      debug(`Store ${name || 'Unknown'} updated`, {
        prevState,
        nextState,
        action: args,
      });
    },
    get,
    api
  );

/**
 * 错误处理中间件
 */
export const errorHandler = <T>(
  config: StateCreator<T, [], [], T>
): StateCreator<T, [], [], T> => (set, get, api) =>
  config(
    (...args) => {
      try {
        set(...args);
      } catch (error) {
        logError(error as Error, {
          context: 'Zustand.errorHandler',
          args,
          state: get(),
        });
        throw error;
      }
    },
    get,
    api
  );

/**
 * 性能监控中间件
 */
export const performanceMonitor = <T>(
  config: StateCreator<T, [], [], T>,
  name?: string
): StateCreator<T, [], [], T> => (set, get, api) =>
  config(
    (...args) => {
      const startTime = performance.now();
      set(...args);
      const endTime = performance.now();
      
      debug(`Store ${name || 'Unknown'} performance`, {
        duration: `${endTime - startTime}ms`,
        action: args,
      });
    },
    get,
    api
  );

/**
 * 状态验证中间件
 */
export const stateValidator = <T>(
  config: StateCreator<T, [], [], T>,
  validator: (state: T) => boolean,
  errorMessage?: string
): StateCreator<T, [], [], T> => (set, get, api) =>
  config(
    (...args) => {
      set(...args);
      const newState = get();
      
      if (!validator(newState)) {
        const error = new Error(errorMessage || 'State validation failed');
        logError(error, { state: newState });
        throw error;
      }
    },
    get,
    api
  );

/**
 * 创建持久化配置
 */
export function createPersistConfig<T>(
  name: string,
  options?: Partial<PersistOptions<T>>
): PersistOptions<T> {
  return {
    name,
    storage: createTaroStorage(),
    version: 1,
    migrate: (persistedState: any, version: number) => {
      debug(`Migrating store ${name} from version ${version}`, persistedState);
      return persistedState;
    },
    onRehydrateStorage: () => (state, error) => {
      if (error) {
        logError(error, { context: 'Store.rehydrate', storeName: name });
      } else {
        debug(`Store ${name} rehydrated`, state);
      }
    },
    ...options,
  };
}

/**
 * 创建开发工具配置
 */
export function createDevtoolsConfig(name: string) {
  return {
    name,
    enabled: process.env.NODE_ENV === 'development',
  };
}

/**
 * 组合中间件
 */
export function composeMiddleware<T>(
  config: StateCreator<T, [], [], T>,
  options: {
    name?: string;
    persist?: PersistOptions<T>;
    enableDevtools?: boolean;
    enableLogger?: boolean;
    enableErrorHandler?: boolean;
    enablePerformanceMonitor?: boolean;
    validator?: (state: T) => boolean;
    validatorErrorMessage?: string;
  } = {}
) {
  const {
    name = 'Store',
    persist: persistOptions,
    enableDevtools = process.env.NODE_ENV === 'development',
    enableLogger = process.env.NODE_ENV === 'development',
    enableErrorHandler = true,
    enablePerformanceMonitor = process.env.NODE_ENV === 'development',
    validator,
    validatorErrorMessage,
  } = options;

  let enhancedConfig = config;

  // 错误处理中间件（最内层）
  if (enableErrorHandler) {
    enhancedConfig = errorHandler(enhancedConfig);
  }

  // 状态验证中间件
  if (validator) {
    enhancedConfig = stateValidator(enhancedConfig, validator, validatorErrorMessage);
  }

  // 性能监控中间件
  if (enablePerformanceMonitor) {
    enhancedConfig = performanceMonitor(enhancedConfig, name);
  }

  // 日志中间件
  if (enableLogger) {
    enhancedConfig = logger(enhancedConfig, name);
  }

  // 订阅中间件
  enhancedConfig = subscribeWithSelector(enhancedConfig);

  // 开发工具中间件
  if (enableDevtools) {
    enhancedConfig = devtools(enhancedConfig, createDevtoolsConfig(name));
  }

  // 持久化中间件（最外层）
  if (persistOptions) {
    enhancedConfig = persist(enhancedConfig, persistOptions);
  }

  return enhancedConfig;
}

/**
 * 状态同步中间件
 * 用于在多个 store 之间同步状态
 */
export function createStateSyncer<T, U>(
  sourceStore: () => T,
  targetStore: (state: Partial<U>) => void,
  mapper: (sourceState: T) => Partial<U>
) {
  return sourceStore.subscribe((state) => {
    const mappedState = mapper(state);
    targetStore(mappedState);
  });
}

/**
 * 批量更新中间件
 */
export const batchUpdates = <T>(
  config: StateCreator<T, [], [], T>
): StateCreator<T, [], [], T> => (set, get, api) => {
  let isUpdating = false;
  let pendingUpdates: Array<() => void> = [];

  const batchedSet = (...args: Parameters<typeof set>) => {
    if (isUpdating) {
      pendingUpdates.push(() => set(...args));
    } else {
      isUpdating = true;
      set(...args);
      
      // 处理待处理的更新
      while (pendingUpdates.length > 0) {
        const update = pendingUpdates.shift();
        if (update) update();
      }
      
      isUpdating = false;
    }
  };

  return config(batchedSet, get, api);
};

/**
 * 防抖中间件
 */
export const debounce = <T>(
  config: StateCreator<T, [], [], T>,
  delay: number = 300
): StateCreator<T, [], [], T> => (set, get, api) => {
  let timeoutId: NodeJS.Timeout | null = null;

  const debouncedSet = (...args: Parameters<typeof set>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      set(...args);
      timeoutId = null;
    }, delay);
  };

  return config(debouncedSet, get, api);
};

/**
 * 节流中间件
 */
export const throttle = <T>(
  config: StateCreator<T, [], [], T>,
  delay: number = 300
): StateCreator<T, [], [], T> => (set, get, api) => {
  let lastCallTime = 0;

  const throttledSet = (...args: Parameters<typeof set>) => {
    const now = Date.now();
    
    if (now - lastCallTime >= delay) {
      set(...args);
      lastCallTime = now;
    }
  };

  return config(throttledSet, get, api);
};
