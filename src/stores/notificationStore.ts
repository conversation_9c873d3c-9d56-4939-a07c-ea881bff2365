/**
 * 通知状态管理
 */

import { create } from 'zustand';
import { composeMiddleware, createPersistConfig } from './middleware';
import { generateUUID } from '../utils/string';

/**
 * 通知类型
 */
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

/**
 * 通知项接口
 */
export interface NotificationItem {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  timestamp: number;
  read: boolean;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
  data?: any;
}

/**
 * 通知状态接口
 */
export interface NotificationState {
  // 通知列表
  notifications: NotificationItem[];
  
  // 未读数量
  unreadCount: number;
  
  // 显示设置
  showNotifications: boolean;
  maxNotifications: number;
  
  // Actions
  addNotification: (notification: Omit<NotificationItem, 'id' | 'timestamp' | 'read'>) => string;
  removeNotification: (id: string) => void;
  updateNotification: (id: string, updates: Partial<NotificationItem>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  clearReadNotifications: () => void;
  
  // 便捷方法
  success: (title: string, message?: string, options?: Partial<NotificationItem>) => string;
  error: (title: string, message?: string, options?: Partial<NotificationItem>) => string;
  warning: (title: string, message?: string, options?: Partial<NotificationItem>) => string;
  info: (title: string, message?: string, options?: Partial<NotificationItem>) => string;
  
  // 设置
  setShowNotifications: (show: boolean) => void;
  setMaxNotifications: (max: number) => void;
}

/**
 * 默认通知持续时间（毫秒）
 */
const DEFAULT_DURATION = {
  success: 3000,
  info: 4000,
  warning: 5000,
  error: 0, // 错误通知不自动消失
};

/**
 * 创建通知状态 Store
 */
export const useNotificationStore = create<NotificationState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      notifications: [],
      unreadCount: 0,
      showNotifications: true,
      maxNotifications: 50,

      // 添加通知
      addNotification: (notification) => {
        const id = generateUUID();
        const newNotification: NotificationItem = {
          id,
          timestamp: Date.now(),
          read: false,
          duration: DEFAULT_DURATION[notification.type],
          ...notification,
        };

        set((state) => {
          const notifications = [newNotification, ...state.notifications];
          
          // 限制通知数量
          if (notifications.length > state.maxNotifications) {
            notifications.splice(state.maxNotifications);
          }
          
          return {
            notifications,
            unreadCount: state.unreadCount + 1,
          };
        });

        // 自动移除通知
        if (newNotification.duration && newNotification.duration > 0) {
          setTimeout(() => {
            get().removeNotification(id);
          }, newNotification.duration);
        }

        return id;
      },

      // 移除通知
      removeNotification: (id) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === id);
          const notifications = state.notifications.filter(n => n.id !== id);
          
          return {
            notifications,
            unreadCount: notification && !notification.read 
              ? state.unreadCount - 1 
              : state.unreadCount,
          };
        });
      },

      // 更新通知
      updateNotification: (id, updates) => {
        set((state) => ({
          notifications: state.notifications.map(notification =>
            notification.id === id
              ? { ...notification, ...updates }
              : notification
          ),
        }));
      },

      // 标记为已读
      markAsRead: (id) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === id);
          if (!notification || notification.read) {
            return state;
          }
          
          return {
            notifications: state.notifications.map(n =>
              n.id === id ? { ...n, read: true } : n
            ),
            unreadCount: state.unreadCount - 1,
          };
        });
      },

      // 标记所有为已读
      markAllAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(notification => ({
            ...notification,
            read: true,
          })),
          unreadCount: 0,
        }));
      },

      // 清除所有通知
      clearNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
        });
      },

      // 清除已读通知
      clearReadNotifications: () => {
        set((state) => ({
          notifications: state.notifications.filter(n => !n.read),
        }));
      },

      // 便捷方法
      success: (title, message, options = {}) => {
        return get().addNotification({
          type: 'success',
          title,
          message,
          ...options,
        });
      },

      error: (title, message, options = {}) => {
        return get().addNotification({
          type: 'error',
          title,
          message,
          persistent: true,
          ...options,
        });
      },

      warning: (title, message, options = {}) => {
        return get().addNotification({
          type: 'warning',
          title,
          message,
          ...options,
        });
      },

      info: (title, message, options = {}) => {
        return get().addNotification({
          type: 'info',
          title,
          message,
          ...options,
        });
      },

      // 设置方法
      setShowNotifications: (show) => {
        set({ showNotifications: show });
      },

      setMaxNotifications: (max) => {
        set((state) => {
          const notifications = state.notifications.slice(0, max);
          const removedCount = state.notifications.length - notifications.length;
          const unreadRemovedCount = state.notifications
            .slice(max)
            .filter(n => !n.read).length;
          
          return {
            maxNotifications: max,
            notifications,
            unreadCount: state.unreadCount - unreadRemovedCount,
          };
        });
      },
    }),
    {
      name: 'NotificationStore',
      persist: createPersistConfig('notification-storage', {
        partialize: (state) => ({
          notifications: state.notifications.filter(n => n.persistent),
          unreadCount: state.notifications.filter(n => n.persistent && !n.read).length,
          showNotifications: state.showNotifications,
          maxNotifications: state.maxNotifications,
        }),
      }),
      enableDevtools: true,
      enableLogger: true,
    }
  )
);

/**
 * 通知状态选择器
 */
export const notificationSelectors = {
  // 基础选择器
  notifications: (state: NotificationState) => state.notifications,
  unreadCount: (state: NotificationState) => state.unreadCount,
  showNotifications: (state: NotificationState) => state.showNotifications,
  
  // 计算属性选择器
  hasUnread: (state: NotificationState) => state.unreadCount > 0,
  recentNotifications: (state: NotificationState, limit: number = 5) => 
    state.notifications.slice(0, limit),
  unreadNotifications: (state: NotificationState) => 
    state.notifications.filter(n => !n.read),
  notificationsByType: (state: NotificationState, type: NotificationType) =>
    state.notifications.filter(n => n.type === type),
  
  // 统计选择器
  getStats: (state: NotificationState) => ({
    total: state.notifications.length,
    unread: state.unreadCount,
    success: state.notifications.filter(n => n.type === 'success').length,
    error: state.notifications.filter(n => n.type === 'error').length,
    warning: state.notifications.filter(n => n.type === 'warning').length,
    info: state.notifications.filter(n => n.type === 'info').length,
  }),
};

/**
 * 通知状态 Hook
 */
export function useNotifications() {
  const store = useNotificationStore();
  
  return {
    // 状态
    notifications: store.notifications,
    unreadCount: store.unreadCount,
    showNotifications: store.showNotifications,
    
    // 计算属性
    hasUnread: notificationSelectors.hasUnread(store),
    recentNotifications: notificationSelectors.recentNotifications(store),
    unreadNotifications: notificationSelectors.unreadNotifications(store),
    stats: notificationSelectors.getStats(store),
    
    // 方法
    add: store.addNotification,
    remove: store.removeNotification,
    update: store.updateNotification,
    markAsRead: store.markAsRead,
    markAllAsRead: store.markAllAsRead,
    clear: store.clearNotifications,
    clearRead: store.clearReadNotifications,
    
    // 便捷方法
    success: store.success,
    error: store.error,
    warning: store.warning,
    info: store.info,
    
    // 设置
    setShow: store.setShowNotifications,
    setMaxNotifications: store.setMaxNotifications,
  };
}

/**
 * 通知便捷 Hook
 */
export function useNotify() {
  const { success, error, warning, info } = useNotifications();
  
  return {
    success,
    error,
    warning,
    info,
  };
}

/**
 * 通知统计 Hook
 */
export function useNotificationStats() {
  const store = useNotificationStore();
  
  return {
    stats: notificationSelectors.getStats(store),
    getByType: (type: NotificationType) => 
      notificationSelectors.notificationsByType(store, type),
  };
}
