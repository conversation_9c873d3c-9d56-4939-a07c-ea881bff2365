/**
 * 应用状态管理
 */

import { create } from 'zustand';
import { composeMiddleware, createPersistConfig } from './middleware';
import { getCurrentPlatform, getCurrentEnvironment } from '../config/env';

/**
 * 应用状态接口
 */
export interface AppState {
  // 基础状态
  loading: boolean;
  theme: Theme;
  platform: Platform;
  environment: Environment;
  language: string;
  version: string;
  
  // 网络状态
  networkStatus: 'online' | 'offline';
  networkType: 'wifi' | 'cellular' | 'none' | 'unknown';
  
  // 设备信息
  deviceInfo: {
    screenWidth: number;
    screenHeight: number;
    pixelRatio: number;
    statusBarHeight: number;
    safeAreaInsets: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
  };
  
  // 应用状态
  orientation: 'portrait' | 'landscape';
  isBackground: boolean;
  isVisible: boolean;
  
  // 全局加载状态
  globalLoading: {
    [key: string]: boolean;
  };
  
  // 全局错误状态
  globalError: {
    message: string;
    code?: string;
    timestamp: number;
  } | null;
  
  // Actions
  setLoading: (loading: boolean) => void;
  setTheme: (theme: Theme) => void;
  setPlatform: (platform: Platform) => void;
  setLanguage: (language: string) => void;
  setNetworkStatus: (status: 'online' | 'offline') => void;
  setNetworkType: (type: AppState['networkType']) => void;
  setOrientation: (orientation: 'portrait' | 'landscape') => void;
  setDeviceInfo: (deviceInfo: Partial<AppState['deviceInfo']>) => void;
  setSafeAreaInsets: (insets: Partial<AppState['deviceInfo']['safeAreaInsets']>) => void;
  setBackground: (isBackground: boolean) => void;
  setVisible: (isVisible: boolean) => void;
  
  // 全局加载管理
  setGlobalLoading: (key: string, loading: boolean) => void;
  clearGlobalLoading: () => void;
  
  // 全局错误管理
  setGlobalError: (error: { message: string; code?: string } | null) => void;
  clearGlobalError: () => void;
  
  // 主题切换
  toggleTheme: () => void;
  
  // 初始化
  initialize: () => Promise<void>;
}

/**
 * 默认设备信息
 */
const defaultDeviceInfo: AppState['deviceInfo'] = {
  screenWidth: 375,
  screenHeight: 667,
  pixelRatio: 2,
  statusBarHeight: 20,
  safeAreaInsets: {
    top: 20,
    bottom: 0,
    left: 0,
    right: 0,
  },
};

/**
 * 创建应用状态 Store
 */
export const useAppStore = create<AppState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      loading: false,
      theme: 'light',
      platform: getCurrentPlatform(),
      environment: getCurrentEnvironment(),
      language: 'zh-CN',
      version: '1.0.0',
      
      networkStatus: 'online',
      networkType: 'unknown',
      
      deviceInfo: defaultDeviceInfo,
      orientation: 'portrait',
      isBackground: false,
      isVisible: true,
      
      globalLoading: {},
      globalError: null,

      // 基础设置方法
      setLoading: (loading) => {
        set({ loading });
      },

      setTheme: (theme) => {
        set({ theme });
        
        // 在 H5 环境中更新 DOM 属性
        if (typeof window !== 'undefined' && window.document) {
          try {
            document.documentElement.setAttribute('data-theme', theme);
            document.documentElement.style.colorScheme = theme;
          } catch (error) {
            console.warn('Failed to update theme in DOM:', error);
          }
        }
      },

      setPlatform: (platform) => {
        set({ platform });
      },

      setLanguage: (language) => {
        set({ language });
        
        // 在 H5 环境中更新 HTML lang 属性
        if (typeof window !== 'undefined' && window.document) {
          try {
            document.documentElement.lang = language;
          } catch (error) {
            console.warn('Failed to update language in DOM:', error);
          }
        }
      },

      setNetworkStatus: (networkStatus) => {
        set({ networkStatus });
      },

      setNetworkType: (networkType) => {
        set({ networkType });
      },

      setOrientation: (orientation) => {
        set({ orientation });
      },

      setDeviceInfo: (newDeviceInfo) => {
        set((state) => ({
          deviceInfo: { ...state.deviceInfo, ...newDeviceInfo },
        }));
      },

      setSafeAreaInsets: (insets) => {
        set((state) => ({
          deviceInfo: {
            ...state.deviceInfo,
            safeAreaInsets: { ...state.deviceInfo.safeAreaInsets, ...insets },
          },
        }));
      },

      setBackground: (isBackground) => {
        set({ isBackground });
      },

      setVisible: (isVisible) => {
        set({ isVisible });
      },

      // 全局加载管理
      setGlobalLoading: (key, loading) => {
        set((state) => ({
          globalLoading: { ...state.globalLoading, [key]: loading },
        }));
      },

      clearGlobalLoading: () => {
        set({ globalLoading: {} });
      },

      // 全局错误管理
      setGlobalError: (error) => {
        set({
          globalError: error ? { ...error, timestamp: Date.now() } : null,
        });
      },

      clearGlobalError: () => {
        set({ globalError: null });
      },

      // 主题切换
      toggleTheme: () => {
        const { theme } = get();
        const newTheme = theme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      },

      // 初始化应用
      initialize: async () => {
        try {
          set({ loading: true });
          
          // 获取设备信息
          if (typeof window !== 'undefined') {
            const deviceInfo = {
              screenWidth: window.screen?.width || window.innerWidth || 375,
              screenHeight: window.screen?.height || window.innerHeight || 667,
              pixelRatio: window.devicePixelRatio || 1,
              statusBarHeight: 20, // 默认值，实际应该从系统获取
            };
            
            get().setDeviceInfo(deviceInfo);
          }
          
          // 检测网络状态
          if (typeof navigator !== 'undefined' && navigator.onLine !== undefined) {
            get().setNetworkStatus(navigator.onLine ? 'online' : 'offline');
          }
          
          // 检测屏幕方向
          if (typeof window !== 'undefined') {
            const orientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
            get().setOrientation(orientation);
          }
          
        } catch (error) {
          console.error('App initialization failed:', error);
          get().setGlobalError({
            message: '应用初始化失败',
            code: 'INIT_ERROR',
          });
        } finally {
          set({ loading: false });
        }
      },
    }),
    {
      name: 'AppStore',
      persist: createPersistConfig('app-storage', {
        partialize: (state) => ({
          theme: state.theme,
          language: state.language,
        }),
      }),
      enableDevtools: true,
      enableLogger: true,
    }
  )
);

/**
 * 应用状态选择器
 */
export const appSelectors = {
  // 基础状态选择器
  loading: (state: AppState) => state.loading,
  theme: (state: AppState) => state.theme,
  platform: (state: AppState) => state.platform,
  language: (state: AppState) => state.language,
  networkStatus: (state: AppState) => state.networkStatus,
  deviceInfo: (state: AppState) => state.deviceInfo,
  
  // 计算属性选择器
  isDarkMode: (state: AppState) => state.theme === 'dark',
  isOnline: (state: AppState) => state.networkStatus === 'online',
  isMobile: (state: AppState) => state.deviceInfo.screenWidth < 768,
  isTablet: (state: AppState) => {
    const { screenWidth } = state.deviceInfo;
    return screenWidth >= 768 && screenWidth < 1024;
  },
  isDesktop: (state: AppState) => state.deviceInfo.screenWidth >= 1024,
  
  // 全局状态选择器
  hasGlobalLoading: (state: AppState) => Object.values(state.globalLoading).some(Boolean),
  globalLoadingKeys: (state: AppState) => Object.keys(state.globalLoading).filter(key => state.globalLoading[key]),
  hasGlobalError: (state: AppState) => state.globalError !== null,
};

/**
 * 应用状态 Hook
 */
export function useApp() {
  const store = useAppStore();
  
  return {
    // 状态
    loading: store.loading,
    theme: store.theme,
    platform: store.platform,
    language: store.language,
    networkStatus: store.networkStatus,
    deviceInfo: store.deviceInfo,
    orientation: store.orientation,
    globalError: store.globalError,
    
    // 计算属性
    isDarkMode: appSelectors.isDarkMode(store),
    isOnline: appSelectors.isOnline(store),
    isMobile: appSelectors.isMobile(store),
    isTablet: appSelectors.isTablet(store),
    isDesktop: appSelectors.isDesktop(store),
    hasGlobalLoading: appSelectors.hasGlobalLoading(store),
    hasGlobalError: appSelectors.hasGlobalError(store),
    
    // 方法
    setLoading: store.setLoading,
    setTheme: store.setTheme,
    setLanguage: store.setLanguage,
    toggleTheme: store.toggleTheme,
    setGlobalLoading: store.setGlobalLoading,
    clearGlobalLoading: store.clearGlobalLoading,
    setGlobalError: store.setGlobalError,
    clearGlobalError: store.clearGlobalError,
    initialize: store.initialize,
  };
}

/**
 * 主题 Hook
 */
export function useTheme() {
  const { theme, isDarkMode, setTheme, toggleTheme } = useApp();
  
  return {
    theme,
    isDarkMode,
    setTheme,
    toggleTheme,
  };
}

/**
 * 网络状态 Hook
 */
export function useNetwork() {
  const store = useAppStore();
  
  return {
    status: store.networkStatus,
    type: store.networkType,
    isOnline: appSelectors.isOnline(store),
    setNetworkStatus: store.setNetworkStatus,
    setNetworkType: store.setNetworkType,
  };
}

/**
 * 设备信息 Hook
 */
export function useDevice() {
  const store = useAppStore();
  
  return {
    deviceInfo: store.deviceInfo,
    orientation: store.orientation,
    isMobile: appSelectors.isMobile(store),
    isTablet: appSelectors.isTablet(store),
    isDesktop: appSelectors.isDesktop(store),
    setDeviceInfo: store.setDeviceInfo,
    setOrientation: store.setOrientation,
  };
}
