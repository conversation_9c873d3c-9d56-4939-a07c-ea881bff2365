/**
 * 路由状态管理
 */

import { create } from 'zustand';
import { composeMiddleware } from './middleware';
import Taro from '@tarojs/taro';

/**
 * 路由状态接口
 */
export interface RouterState {
  // 当前路由信息
  currentRoute: string;
  routeParams: Record<string, any>;
  routeQuery: Record<string, any>;
  
  // 路由历史
  routeHistory: Array<{
    path: string;
    params?: Record<string, any>;
    query?: Record<string, any>;
    timestamp: number;
  }>;
  
  // 标签栏状态
  tabBarVisible: boolean;
  tabBarIndex: number;
  
  // 导航状态
  canGoBack: boolean;
  navigationBarTitle: string;
  navigationBarLoading: boolean;
  
  // Actions
  setCurrentRoute: (route: string, params?: Record<string, any>, query?: Record<string, any>) => void;
  pushRoute: (route: string, params?: Record<string, any>) => void;
  popRoute: () => void;
  setTabBarVisible: (visible: boolean) => void;
  setTabBarIndex: (index: number) => void;
  setNavigationBarTitle: (title: string) => void;
  setNavigationBarLoading: (loading: boolean) => void;
  clearHistory: () => void;
  
  // 导航方法
  navigateTo: (url: string, params?: Record<string, any>) => Promise<void>;
  redirectTo: (url: string, params?: Record<string, any>) => Promise<void>;
  reLaunch: (url: string, params?: Record<string, any>) => Promise<void>;
  switchTab: (url: string) => Promise<void>;
  navigateBack: (delta?: number) => Promise<void>;
  
  // 路由守卫
  beforeRouteChange?: (to: string, from: string) => boolean | Promise<boolean>;
  afterRouteChange?: (to: string, from: string) => void;
}

/**
 * 构建 URL
 */
function buildUrl(path: string, params?: Record<string, any>): string {
  if (!params || Object.keys(params).length === 0) {
    return path;
  }
  
  const queryString = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
    .join('&');
    
  return `${path}?${queryString}`;
}

/**
 * 解析 URL
 */
function parseUrl(url: string): { path: string; query: Record<string, any> } {
  const [path, queryString] = url.split('?');
  const query: Record<string, any> = {};
  
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=');
      if (key) {
        query[decodeURIComponent(key)] = decodeURIComponent(value || '');
      }
    });
  }
  
  return { path, query };
}

/**
 * 创建路由状态 Store
 */
export const useRouterStore = create<RouterState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      currentRoute: '',
      routeParams: {},
      routeQuery: {},
      routeHistory: [],
      tabBarVisible: true,
      tabBarIndex: 0,
      canGoBack: false,
      navigationBarTitle: '',
      navigationBarLoading: false,

      // 基础设置方法
      setCurrentRoute: (route, params = {}, query = {}) => {
        const { currentRoute: prevRoute } = get();
        
        // 添加到历史记录
        if (prevRoute && prevRoute !== route) {
          set((state) => ({
            routeHistory: [
              ...state.routeHistory,
              {
                path: prevRoute,
                params: state.routeParams,
                query: state.routeQuery,
                timestamp: Date.now(),
              },
            ].slice(-20), // 只保留最近20条记录
          }));
        }
        
        set({
          currentRoute: route,
          routeParams: params,
          routeQuery: query,
          canGoBack: get().routeHistory.length > 0,
        });
        
        // 触发路由变化回调
        const { afterRouteChange } = get();
        if (afterRouteChange) {
          afterRouteChange(route, prevRoute);
        }
      },

      pushRoute: (route, params) => {
        get().setCurrentRoute(route, params);
      },

      popRoute: () => {
        const { routeHistory } = get();
        if (routeHistory.length > 0) {
          const lastRoute = routeHistory[routeHistory.length - 1];
          set((state) => ({
            currentRoute: lastRoute.path,
            routeParams: lastRoute.params || {},
            routeQuery: lastRoute.query || {},
            routeHistory: state.routeHistory.slice(0, -1),
            canGoBack: state.routeHistory.length > 1,
          }));
        }
      },

      setTabBarVisible: (visible) => {
        set({ tabBarVisible: visible });
      },

      setTabBarIndex: (index) => {
        set({ tabBarIndex: index });
      },

      setNavigationBarTitle: (title) => {
        set({ navigationBarTitle: title });
        
        // 更新页面标题
        try {
          Taro.setNavigationBarTitle({ title });
        } catch (error) {
          console.warn('Failed to set navigation bar title:', error);
        }
      },

      setNavigationBarLoading: (loading) => {
        set({ navigationBarLoading: loading });
        
        // 显示/隐藏导航栏加载状态
        try {
          if (loading) {
            Taro.showNavigationBarLoading();
          } else {
            Taro.hideNavigationBarLoading();
          }
        } catch (error) {
          console.warn('Failed to set navigation bar loading:', error);
        }
      },

      clearHistory: () => {
        set({ routeHistory: [], canGoBack: false });
      },

      // 导航方法
      navigateTo: async (url, params) => {
        const { beforeRouteChange } = get();
        const { path, query } = parseUrl(url);
        const fullUrl = buildUrl(path, { ...query, ...params });
        
        // 路由守卫检查
        if (beforeRouteChange) {
          const canNavigate = await beforeRouteChange(path, get().currentRoute);
          if (!canNavigate) return;
        }
        
        try {
          await Taro.navigateTo({ url: fullUrl });
          get().setCurrentRoute(path, params, query);
        } catch (error) {
          console.error('Navigation failed:', error);
          throw error;
        }
      },

      redirectTo: async (url, params) => {
        const { beforeRouteChange } = get();
        const { path, query } = parseUrl(url);
        const fullUrl = buildUrl(path, { ...query, ...params });
        
        // 路由守卫检查
        if (beforeRouteChange) {
          const canNavigate = await beforeRouteChange(path, get().currentRoute);
          if (!canNavigate) return;
        }
        
        try {
          await Taro.redirectTo({ url: fullUrl });
          get().setCurrentRoute(path, params, query);
        } catch (error) {
          console.error('Redirect failed:', error);
          throw error;
        }
      },

      reLaunch: async (url, params) => {
        const { beforeRouteChange } = get();
        const { path, query } = parseUrl(url);
        const fullUrl = buildUrl(path, { ...query, ...params });
        
        // 路由守卫检查
        if (beforeRouteChange) {
          const canNavigate = await beforeRouteChange(path, get().currentRoute);
          if (!canNavigate) return;
        }
        
        try {
          await Taro.reLaunch({ url: fullUrl });
          get().setCurrentRoute(path, params, query);
          get().clearHistory();
        } catch (error) {
          console.error('ReLaunch failed:', error);
          throw error;
        }
      },

      switchTab: async (url) => {
        const { beforeRouteChange } = get();
        const { path } = parseUrl(url);
        
        // 路由守卫检查
        if (beforeRouteChange) {
          const canNavigate = await beforeRouteChange(path, get().currentRoute);
          if (!canNavigate) return;
        }
        
        try {
          await Taro.switchTab({ url });
          get().setCurrentRoute(path);
        } catch (error) {
          console.error('Switch tab failed:', error);
          throw error;
        }
      },

      navigateBack: async (delta = 1) => {
        try {
          await Taro.navigateBack({ delta });
          
          // 更新路由状态
          for (let i = 0; i < delta; i++) {
            get().popRoute();
          }
        } catch (error) {
          console.error('Navigate back failed:', error);
          throw error;
        }
      },
    }),
    {
      name: 'RouterStore',
      enableDevtools: true,
      enableLogger: true,
    }
  )
);

/**
 * 路由状态选择器
 */
export const routerSelectors = {
  currentRoute: (state: RouterState) => state.currentRoute,
  routeParams: (state: RouterState) => state.routeParams,
  routeQuery: (state: RouterState) => state.routeQuery,
  canGoBack: (state: RouterState) => state.canGoBack,
  historyLength: (state: RouterState) => state.routeHistory.length,
  
  // 获取路由参数
  getParam: (state: RouterState, key: string, defaultValue?: any) => {
    return state.routeParams[key] ?? state.routeQuery[key] ?? defaultValue;
  },
  
  // 检查是否为指定路由
  isRoute: (state: RouterState, route: string) => {
    return state.currentRoute === route;
  },
  
  // 检查是否为标签页路由
  isTabRoute: (state: RouterState) => {
    const tabRoutes = ['/pages/index/index', '/pages/profile/index'];
    return tabRoutes.includes(state.currentRoute);
  },
};

/**
 * 路由状态 Hook
 */
export function useRouter() {
  const store = useRouterStore();
  
  return {
    // 状态
    currentRoute: store.currentRoute,
    routeParams: store.routeParams,
    routeQuery: store.routeQuery,
    canGoBack: store.canGoBack,
    navigationBarTitle: store.navigationBarTitle,
    
    // 计算属性
    historyLength: routerSelectors.historyLength(store),
    isTabRoute: routerSelectors.isTabRoute(store),
    
    // 方法
    navigateTo: store.navigateTo,
    redirectTo: store.redirectTo,
    reLaunch: store.reLaunch,
    switchTab: store.switchTab,
    navigateBack: store.navigateBack,
    setNavigationBarTitle: store.setNavigationBarTitle,
    setNavigationBarLoading: store.setNavigationBarLoading,
    
    // 工具方法
    getParam: (key: string, defaultValue?: any) => 
      routerSelectors.getParam(store, key, defaultValue),
    isRoute: (route: string) => routerSelectors.isRoute(store, route),
  };
}

/**
 * 路由参数 Hook
 */
export function useRouteParams<T extends Record<string, any> = Record<string, any>>(): T {
  const { routeParams, routeQuery } = useRouterStore();
  return { ...routeParams, ...routeQuery } as T;
}

/**
 * 路由守卫 Hook
 */
export function useRouteGuard(
  beforeChange?: (to: string, from: string) => boolean | Promise<boolean>,
  afterChange?: (to: string, from: string) => void
) {
  const store = useRouterStore();
  
  React.useEffect(() => {
    if (beforeChange) {
      store.beforeRouteChange = beforeChange;
    }
    if (afterChange) {
      store.afterRouteChange = afterChange;
    }
    
    return () => {
      store.beforeRouteChange = undefined;
      store.afterRouteChange = undefined;
    };
  }, [beforeChange, afterChange, store]);
}
