/**
 * 缓存状态管理
 */

import { create } from 'zustand';
import { composeMiddleware } from './middleware';

/**
 * 缓存项接口
 */
interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

/**
 * 缓存状态接口
 */
export interface CacheState {
  // API 缓存
  apiCache: Map<string, CacheItem>;
  
  // 图片缓存
  imageCache: Map<string, string>;
  
  // 页面缓存
  pageCache: Map<string, any>;
  
  // 用户数据缓存
  userDataCache: Map<string, CacheItem>;
  
  // Actions
  setApiCache: (key: string, data: any, ttl?: number) => void;
  getApiCache: (key: string) => any;
  removeApiCache: (key: string) => void;
  clearApiCache: () => void;
  
  setImageCache: (key: string, url: string) => void;
  getImageCache: (key: string) => string | undefined;
  removeImageCache: (key: string) => void;
  clearImageCache: () => void;
  
  setPageCache: (key: string, data: any) => void;
  getPageCache: (key: string) => any;
  removePageCache: (key: string) => void;
  clearPageCache: () => void;
  
  setUserDataCache: (key: string, data: any, ttl?: number) => void;
  getUserDataCache: (key: string) => any;
  removeUserDataCache: (key: string) => void;
  clearUserDataCache: () => void;
  
  // 通用缓存方法
  set: (type: 'api' | 'image' | 'page' | 'userData', key: string, data: any, ttl?: number) => void;
  get: (type: 'api' | 'image' | 'page' | 'userData', key: string) => any;
  remove: (type: 'api' | 'image' | 'page' | 'userData', key: string) => void;
  clear: (type?: 'api' | 'image' | 'page' | 'userData') => void;
  
  // 缓存管理
  clearExpired: () => void;
  getCacheSize: () => { api: number; image: number; page: number; userData: number };
  getCacheInfo: () => {
    totalItems: number;
    expiredItems: number;
    memoryUsage: number;
  };
}

/**
 * 默认 TTL（秒）
 */
const DEFAULT_TTL = {
  api: 5 * 60, // 5分钟
  userData: 30 * 60, // 30分钟
  page: 10 * 60, // 10分钟
  image: 60 * 60, // 1小时
};

/**
 * 检查缓存是否过期
 */
function isExpired(item: CacheItem): boolean {
  return Date.now() - item.timestamp > item.ttl * 1000;
}

/**
 * 创建缓存项
 */
function createCacheItem<T>(key: string, data: T, ttl: number): CacheItem<T> {
  return {
    key,
    data,
    timestamp: Date.now(),
    ttl,
  };
}

/**
 * 创建缓存状态 Store
 */
export const useCacheStore = create<CacheState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      apiCache: new Map(),
      imageCache: new Map(),
      pageCache: new Map(),
      userDataCache: new Map(),

      // API 缓存方法
      setApiCache: (key, data, ttl = DEFAULT_TTL.api) => {
        set((state) => {
          const newCache = new Map(state.apiCache);
          newCache.set(key, createCacheItem(key, data, ttl));
          return { apiCache: newCache };
        });
      },

      getApiCache: (key) => {
        const { apiCache } = get();
        const item = apiCache.get(key);
        
        if (!item) return undefined;
        
        if (isExpired(item)) {
          get().removeApiCache(key);
          return undefined;
        }
        
        return item.data;
      },

      removeApiCache: (key) => {
        set((state) => {
          const newCache = new Map(state.apiCache);
          newCache.delete(key);
          return { apiCache: newCache };
        });
      },

      clearApiCache: () => {
        set({ apiCache: new Map() });
      },

      // 图片缓存方法
      setImageCache: (key, url) => {
        set((state) => {
          const newCache = new Map(state.imageCache);
          newCache.set(key, url);
          return { imageCache: newCache };
        });
      },

      getImageCache: (key) => {
        const { imageCache } = get();
        return imageCache.get(key);
      },

      removeImageCache: (key) => {
        set((state) => {
          const newCache = new Map(state.imageCache);
          newCache.delete(key);
          return { imageCache: newCache };
        });
      },

      clearImageCache: () => {
        set({ imageCache: new Map() });
      },

      // 页面缓存方法
      setPageCache: (key, data) => {
        set((state) => {
          const newCache = new Map(state.pageCache);
          newCache.set(key, data);
          return { pageCache: newCache };
        });
      },

      getPageCache: (key) => {
        const { pageCache } = get();
        return pageCache.get(key);
      },

      removePageCache: (key) => {
        set((state) => {
          const newCache = new Map(state.pageCache);
          newCache.delete(key);
          return { pageCache: newCache };
        });
      },

      clearPageCache: () => {
        set({ pageCache: new Map() });
      },

      // 用户数据缓存方法
      setUserDataCache: (key, data, ttl = DEFAULT_TTL.userData) => {
        set((state) => {
          const newCache = new Map(state.userDataCache);
          newCache.set(key, createCacheItem(key, data, ttl));
          return { userDataCache: newCache };
        });
      },

      getUserDataCache: (key) => {
        const { userDataCache } = get();
        const item = userDataCache.get(key);
        
        if (!item) return undefined;
        
        if (isExpired(item)) {
          get().removeUserDataCache(key);
          return undefined;
        }
        
        return item.data;
      },

      removeUserDataCache: (key) => {
        set((state) => {
          const newCache = new Map(state.userDataCache);
          newCache.delete(key);
          return { userDataCache: newCache };
        });
      },

      clearUserDataCache: () => {
        set({ userDataCache: new Map() });
      },

      // 通用缓存方法
      set: (type, key, data, ttl) => {
        switch (type) {
          case 'api':
            get().setApiCache(key, data, ttl);
            break;
          case 'image':
            get().setImageCache(key, data);
            break;
          case 'page':
            get().setPageCache(key, data);
            break;
          case 'userData':
            get().setUserDataCache(key, data, ttl);
            break;
        }
      },

      get: (type, key) => {
        switch (type) {
          case 'api':
            return get().getApiCache(key);
          case 'image':
            return get().getImageCache(key);
          case 'page':
            return get().getPageCache(key);
          case 'userData':
            return get().getUserDataCache(key);
          default:
            return undefined;
        }
      },

      remove: (type, key) => {
        switch (type) {
          case 'api':
            get().removeApiCache(key);
            break;
          case 'image':
            get().removeImageCache(key);
            break;
          case 'page':
            get().removePageCache(key);
            break;
          case 'userData':
            get().removeUserDataCache(key);
            break;
        }
      },

      clear: (type) => {
        if (type) {
          switch (type) {
            case 'api':
              get().clearApiCache();
              break;
            case 'image':
              get().clearImageCache();
              break;
            case 'page':
              get().clearPageCache();
              break;
            case 'userData':
              get().clearUserDataCache();
              break;
          }
        } else {
          // 清除所有缓存
          set({
            apiCache: new Map(),
            imageCache: new Map(),
            pageCache: new Map(),
            userDataCache: new Map(),
          });
        }
      },

      // 清除过期缓存
      clearExpired: () => {
        const { apiCache, userDataCache } = get();
        
        // 清除过期的 API 缓存
        const newApiCache = new Map();
        apiCache.forEach((item, key) => {
          if (!isExpired(item)) {
            newApiCache.set(key, item);
          }
        });
        
        // 清除过期的用户数据缓存
        const newUserDataCache = new Map();
        userDataCache.forEach((item, key) => {
          if (!isExpired(item)) {
            newUserDataCache.set(key, item);
          }
        });
        
        set({
          apiCache: newApiCache,
          userDataCache: newUserDataCache,
        });
      },

      // 获取缓存大小
      getCacheSize: () => {
        const { apiCache, imageCache, pageCache, userDataCache } = get();
        
        return {
          api: apiCache.size,
          image: imageCache.size,
          page: pageCache.size,
          userData: userDataCache.size,
        };
      },

      // 获取缓存信息
      getCacheInfo: () => {
        const { apiCache, imageCache, pageCache, userDataCache } = get();
        
        let totalItems = 0;
        let expiredItems = 0;
        let memoryUsage = 0;
        
        // 统计 API 缓存
        apiCache.forEach((item) => {
          totalItems++;
          if (isExpired(item)) expiredItems++;
          memoryUsage += JSON.stringify(item).length;
        });
        
        // 统计图片缓存
        imageCache.forEach((url) => {
          totalItems++;
          memoryUsage += url.length;
        });
        
        // 统计页面缓存
        pageCache.forEach((data) => {
          totalItems++;
          memoryUsage += JSON.stringify(data).length;
        });
        
        // 统计用户数据缓存
        userDataCache.forEach((item) => {
          totalItems++;
          if (isExpired(item)) expiredItems++;
          memoryUsage += JSON.stringify(item).length;
        });
        
        return {
          totalItems,
          expiredItems,
          memoryUsage,
        };
      },
    }),
    {
      name: 'CacheStore',
      enableDevtools: true,
      enableLogger: false, // 缓存操作频繁，关闭日志
    }
  )
);

/**
 * 缓存状态 Hook
 */
export function useCache() {
  const store = useCacheStore();
  
  return {
    // API 缓存
    setApiCache: store.setApiCache,
    getApiCache: store.getApiCache,
    removeApiCache: store.removeApiCache,
    clearApiCache: store.clearApiCache,
    
    // 图片缓存
    setImageCache: store.setImageCache,
    getImageCache: store.getImageCache,
    removeImageCache: store.removeImageCache,
    clearImageCache: store.clearImageCache,
    
    // 页面缓存
    setPageCache: store.setPageCache,
    getPageCache: store.getPageCache,
    removePageCache: store.removePageCache,
    clearPageCache: store.clearPageCache,
    
    // 用户数据缓存
    setUserDataCache: store.setUserDataCache,
    getUserDataCache: store.getUserDataCache,
    removeUserDataCache: store.removeUserDataCache,
    clearUserDataCache: store.clearUserDataCache,
    
    // 通用方法
    set: store.set,
    get: store.get,
    remove: store.remove,
    clear: store.clear,
    clearExpired: store.clearExpired,
    getCacheSize: store.getCacheSize,
    getCacheInfo: store.getCacheInfo,
  };
}

/**
 * API 缓存 Hook
 */
export function useApiCache() {
  const { setApiCache, getApiCache, removeApiCache, clearApiCache } = useCache();
  
  return {
    set: setApiCache,
    get: getApiCache,
    remove: removeApiCache,
    clear: clearApiCache,
  };
}

// 定期清理过期缓存
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    try {
      useCacheStore.getState().clearExpired();
    } catch (error) {
      console.warn('Failed to clear expired cache:', error);
    }
  }, 5 * 60 * 1000); // 每5分钟清理一次
}
