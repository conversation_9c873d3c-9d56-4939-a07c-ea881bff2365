/**
 * 上传状态管理
 */

import { create } from 'zustand';
import { composeMiddleware } from './middleware';
import { generateUUID } from '../utils/string';

/**
 * 上传状态类型
 */
export type UploadStatus = 'pending' | 'uploading' | 'success' | 'error' | 'cancelled';

/**
 * 上传项接口
 */
export interface UploadItem {
  id: string;
  file: File | string; // File 对象或文件路径
  fileName: string;
  fileSize: number;
  fileType: string;
  progress: number;
  status: UploadStatus;
  url?: string;
  error?: string;
  uploadTime?: number;
  completeTime?: number;
  retryCount: number;
  maxRetries: number;
  category?: string;
  metadata?: Record<string, any>;
}

/**
 * 上传配置接口
 */
export interface UploadConfig {
  maxFileSize: number;
  allowedTypes: string[];
  maxConcurrent: number;
  chunkSize?: number;
  autoRetry: boolean;
  maxRetries: number;
}

/**
 * 上传状态接口
 */
export interface UploadState {
  // 上传队列
  uploads: Map<string, UploadItem>;
  
  // 上传配置
  config: UploadConfig;
  
  // 统计信息
  stats: {
    total: number;
    pending: number;
    uploading: number;
    success: number;
    error: number;
    cancelled: number;
  };
  
  // 当前上传中的文件数量
  activeUploads: number;
  
  // Actions
  addUpload: (upload: Omit<UploadItem, 'id' | 'progress' | 'status' | 'retryCount'>) => string;
  updateUpload: (id: string, updates: Partial<UploadItem>) => void;
  updateUploadProgress: (id: string, progress: number) => void;
  updateUploadStatus: (id: string, status: UploadStatus) => void;
  setUploadUrl: (id: string, url: string) => void;
  setUploadError: (id: string, error: string) => void;
  removeUpload: (id: string) => void;
  clearUploads: () => void;
  clearCompletedUploads: () => void;
  
  // 批量操作
  retryUpload: (id: string) => void;
  retryAllFailed: () => void;
  cancelUpload: (id: string) => void;
  cancelAllUploads: () => void;
  
  // 配置管理
  updateConfig: (config: Partial<UploadConfig>) => void;
  
  // 查询方法
  getUpload: (id: string) => UploadItem | undefined;
  getUploadsByStatus: (status: UploadStatus) => UploadItem[];
  getUploadsByCategory: (category: string) => UploadItem[];
  
  // 统计方法
  updateStats: () => void;
}

/**
 * 默认上传配置
 */
const defaultConfig: UploadConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4'],
  maxConcurrent: 3,
  chunkSize: 1024 * 1024, // 1MB
  autoRetry: true,
  maxRetries: 3,
};

/**
 * 创建上传状态 Store
 */
export const useUploadStore = create<UploadState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      uploads: new Map(),
      config: defaultConfig,
      stats: {
        total: 0,
        pending: 0,
        uploading: 0,
        success: 0,
        error: 0,
        cancelled: 0,
      },
      activeUploads: 0,

      // 添加上传
      addUpload: (upload) => {
        const id = generateUUID();
        const newUpload: UploadItem = {
          id,
          progress: 0,
          status: 'pending',
          retryCount: 0,
          maxRetries: get().config.maxRetries,
          uploadTime: Date.now(),
          ...upload,
        };

        set((state) => {
          const newUploads = new Map(state.uploads);
          newUploads.set(id, newUpload);
          return { uploads: newUploads };
        });

        get().updateStats();
        return id;
      },

      // 更新上传
      updateUpload: (id, updates) => {
        set((state) => {
          const upload = state.uploads.get(id);
          if (!upload) return state;

          const newUploads = new Map(state.uploads);
          newUploads.set(id, { ...upload, ...updates });
          return { uploads: newUploads };
        });

        get().updateStats();
      },

      // 更新上传进度
      updateUploadProgress: (id, progress) => {
        get().updateUpload(id, { progress: Math.min(100, Math.max(0, progress)) });
      },

      // 更新上传状态
      updateUploadStatus: (id, status) => {
        const updates: Partial<UploadItem> = { status };
        
        if (status === 'success' || status === 'error' || status === 'cancelled') {
          updates.completeTime = Date.now();
        }
        
        get().updateUpload(id, updates);
      },

      // 设置上传 URL
      setUploadUrl: (id, url) => {
        get().updateUpload(id, { url, status: 'success', progress: 100 });
      },

      // 设置上传错误
      setUploadError: (id, error) => {
        get().updateUpload(id, { error, status: 'error' });
      },

      // 移除上传
      removeUpload: (id) => {
        set((state) => {
          const newUploads = new Map(state.uploads);
          newUploads.delete(id);
          return { uploads: newUploads };
        });

        get().updateStats();
      },

      // 清除所有上传
      clearUploads: () => {
        set({ uploads: new Map() });
        get().updateStats();
      },

      // 清除已完成的上传
      clearCompletedUploads: () => {
        set((state) => {
          const newUploads = new Map();
          state.uploads.forEach((upload, id) => {
            if (upload.status === 'pending' || upload.status === 'uploading') {
              newUploads.set(id, upload);
            }
          });
          return { uploads: newUploads };
        });

        get().updateStats();
      },

      // 重试上传
      retryUpload: (id) => {
        const upload = get().uploads.get(id);
        if (!upload || upload.retryCount >= upload.maxRetries) return;

        get().updateUpload(id, {
          status: 'pending',
          progress: 0,
          error: undefined,
          retryCount: upload.retryCount + 1,
        });
      },

      // 重试所有失败的上传
      retryAllFailed: () => {
        const { uploads } = get();
        uploads.forEach((upload, id) => {
          if (upload.status === 'error' && upload.retryCount < upload.maxRetries) {
            get().retryUpload(id);
          }
        });
      },

      // 取消上传
      cancelUpload: (id) => {
        get().updateUploadStatus(id, 'cancelled');
      },

      // 取消所有上传
      cancelAllUploads: () => {
        const { uploads } = get();
        uploads.forEach((upload, id) => {
          if (upload.status === 'pending' || upload.status === 'uploading') {
            get().cancelUpload(id);
          }
        });
      },

      // 更新配置
      updateConfig: (newConfig) => {
        set((state) => ({
          config: { ...state.config, ...newConfig },
        }));
      },

      // 获取上传
      getUpload: (id) => {
        return get().uploads.get(id);
      },

      // 按状态获取上传
      getUploadsByStatus: (status) => {
        const { uploads } = get();
        return Array.from(uploads.values()).filter(upload => upload.status === status);
      },

      // 按分类获取上传
      getUploadsByCategory: (category) => {
        const { uploads } = get();
        return Array.from(uploads.values()).filter(upload => upload.category === category);
      },

      // 更新统计信息
      updateStats: () => {
        const { uploads } = get();
        const stats = {
          total: uploads.size,
          pending: 0,
          uploading: 0,
          success: 0,
          error: 0,
          cancelled: 0,
        };

        uploads.forEach(upload => {
          stats[upload.status]++;
        });

        set({ 
          stats,
          activeUploads: stats.uploading,
        });
      },
    }),
    {
      name: 'UploadStore',
      enableDevtools: true,
      enableLogger: true,
    }
  )
);

/**
 * 上传状态选择器
 */
export const uploadSelectors = {
  // 基础选择器
  uploads: (state: UploadState) => Array.from(state.uploads.values()),
  stats: (state: UploadState) => state.stats,
  config: (state: UploadState) => state.config,
  activeUploads: (state: UploadState) => state.activeUploads,
  
  // 计算属性选择器
  hasActiveUploads: (state: UploadState) => state.activeUploads > 0,
  hasFailedUploads: (state: UploadState) => state.stats.error > 0,
  canRetry: (state: UploadState) => {
    return Array.from(state.uploads.values()).some(
      upload => upload.status === 'error' && upload.retryCount < upload.maxRetries
    );
  },
  
  // 进度计算
  totalProgress: (state: UploadState) => {
    const uploads = Array.from(state.uploads.values());
    if (uploads.length === 0) return 0;
    
    const totalProgress = uploads.reduce((sum, upload) => sum + upload.progress, 0);
    return Math.round(totalProgress / uploads.length);
  },
  
  // 最近上传
  recentUploads: (state: UploadState, limit: number = 10) => {
    return Array.from(state.uploads.values())
      .sort((a, b) => (b.uploadTime || 0) - (a.uploadTime || 0))
      .slice(0, limit);
  },
};

/**
 * 上传状态 Hook
 */
export function useUploads() {
  const store = useUploadStore();
  
  return {
    // 状态
    uploads: uploadSelectors.uploads(store),
    stats: store.stats,
    config: store.config,
    activeUploads: store.activeUploads,
    
    // 计算属性
    hasActiveUploads: uploadSelectors.hasActiveUploads(store),
    hasFailedUploads: uploadSelectors.hasFailedUploads(store),
    canRetry: uploadSelectors.canRetry(store),
    totalProgress: uploadSelectors.totalProgress(store),
    recentUploads: uploadSelectors.recentUploads(store),
    
    // 方法
    add: store.addUpload,
    update: store.updateUpload,
    updateProgress: store.updateUploadProgress,
    updateStatus: store.updateUploadStatus,
    setUrl: store.setUploadUrl,
    setError: store.setUploadError,
    remove: store.removeUpload,
    clear: store.clearUploads,
    clearCompleted: store.clearCompletedUploads,
    
    // 批量操作
    retry: store.retryUpload,
    retryAllFailed: store.retryAllFailed,
    cancel: store.cancelUpload,
    cancelAll: store.cancelAllUploads,
    
    // 查询方法
    get: store.getUpload,
    getByStatus: store.getUploadsByStatus,
    getByCategory: store.getUploadsByCategory,
    
    // 配置
    updateConfig: store.updateConfig,
  };
}

/**
 * 上传统计 Hook
 */
export function useUploadStats() {
  const store = useUploadStore();
  
  return {
    stats: store.stats,
    totalProgress: uploadSelectors.totalProgress(store),
    hasActiveUploads: uploadSelectors.hasActiveUploads(store),
    hasFailedUploads: uploadSelectors.hasFailedUploads(store),
  };
}

/**
 * 上传配置 Hook
 */
export function useUploadConfig() {
  const { config, updateConfig } = useUploadStore();
  
  return {
    config,
    updateConfig,
  };
}
