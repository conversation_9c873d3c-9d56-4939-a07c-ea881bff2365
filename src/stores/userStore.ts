/**
 * 用户状态管理
 */

import { create } from 'zustand';
import { composeMiddleware, createPersistConfig } from './middleware';

/**
 * 用户状态接口
 */
export interface UserState {
  // 用户信息
  userInfo: UserInfo | null;
  token: string | null;
  refreshToken: string | null;
  isLogin: boolean;
  permissions: string[];
  roles: string[];
  
  // 用户偏好
  preferences: {
    language: string;
    timezone: string;
    dateFormat: string;
    currency: string;
  };
  
  // 登录状态
  loginTime: number | null;
  lastActiveTime: number | null;
  
  // Actions
  setUserInfo: (userInfo: UserInfo | null) => void;
  setToken: (token: string | null) => void;
  setRefreshToken: (refreshToken: string | null) => void;
  setPermissions: (permissions: string[]) => void;
  setRoles: (roles: string[]) => void;
  setPreferences: (preferences: Partial<UserState['preferences']>) => void;
  updateLastActiveTime: () => void;
  
  login: (userInfo: UserInfo, token: string, refreshToken?: string) => void;
  logout: () => void;
  updateProfile: (updates: Partial<UserInfo>) => void;
  
  // 权限检查
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;
  
  // 会话管理
  isSessionValid: () => boolean;
  getSessionDuration: () => number;
  refreshSession: () => void;
}

/**
 * 默认用户偏好
 */
const defaultPreferences: UserState['preferences'] = {
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  currency: 'CNY',
};

/**
 * 创建用户状态 Store
 */
export const useUserStore = create<UserState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      userInfo: null,
      token: null,
      refreshToken: null,
      isLogin: false,
      permissions: [],
      roles: [],
      preferences: defaultPreferences,
      loginTime: null,
      lastActiveTime: null,

      // 基础设置方法
      setUserInfo: (userInfo) => {
        set({ userInfo });
      },

      setToken: (token) => {
        set({ token });
      },

      setRefreshToken: (refreshToken) => {
        set({ refreshToken });
      },

      setPermissions: (permissions) => {
        set({ permissions });
      },

      setRoles: (roles) => {
        set({ roles });
      },

      setPreferences: (newPreferences) => {
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences },
        }));
      },

      updateLastActiveTime: () => {
        set({ lastActiveTime: Date.now() });
      },

      // 登录方法
      login: (userInfo, token, refreshToken) => {
        const now = Date.now();
        set({
          userInfo,
          token,
          refreshToken,
          isLogin: true,
          loginTime: now,
          lastActiveTime: now,
        });
      },

      // 登出方法
      logout: () => {
        set({
          userInfo: null,
          token: null,
          refreshToken: null,
          isLogin: false,
          permissions: [],
          roles: [],
          loginTime: null,
          lastActiveTime: null,
        });
      },

      // 更新用户资料
      updateProfile: (updates) => {
        set((state) => ({
          userInfo: state.userInfo ? { ...state.userInfo, ...updates } : null,
        }));
      },

      // 权限检查方法
      hasPermission: (permission) => {
        const { permissions } = get();
        return permissions.includes(permission);
      },

      hasRole: (role) => {
        const { roles } = get();
        return roles.includes(role);
      },

      hasAnyPermission: (permissions) => {
        const { permissions: userPermissions } = get();
        return permissions.some(permission => userPermissions.includes(permission));
      },

      hasAllPermissions: (permissions) => {
        const { permissions: userPermissions } = get();
        return permissions.every(permission => userPermissions.includes(permission));
      },

      hasAnyRole: (roles) => {
        const { roles: userRoles } = get();
        return roles.some(role => userRoles.includes(role));
      },

      hasAllRoles: (roles) => {
        const { roles: userRoles } = get();
        return roles.every(role => userRoles.includes(role));
      },

      // 会话管理方法
      isSessionValid: () => {
        const { isLogin, loginTime } = get();
        if (!isLogin || !loginTime) return false;
        
        // 检查会话是否过期（24小时）
        const sessionTimeout = 24 * 60 * 60 * 1000; // 24小时
        return Date.now() - loginTime < sessionTimeout;
      },

      getSessionDuration: () => {
        const { loginTime } = get();
        if (!loginTime) return 0;
        return Date.now() - loginTime;
      },

      refreshSession: () => {
        set({ lastActiveTime: Date.now() });
      },
    }),
    {
      name: 'UserStore',
      persist: createPersistConfig('user-storage', {
        partialize: (state) => ({
          userInfo: state.userInfo,
          token: state.token,
          refreshToken: state.refreshToken,
          isLogin: state.isLogin,
          permissions: state.permissions,
          roles: state.roles,
          preferences: state.preferences,
          loginTime: state.loginTime,
        }),
      }),
      enableDevtools: true,
      enableLogger: true,
      validator: (state) => {
        // 验证登录状态的一致性
        if (state.isLogin && (!state.token || !state.userInfo)) {
          return false;
        }
        if (!state.isLogin && (state.token || state.userInfo)) {
          return false;
        }
        return true;
      },
      validatorErrorMessage: 'User state validation failed: login state inconsistency',
    }
  )
);

/**
 * 用户状态选择器
 */
export const userSelectors = {
  // 基础信息选择器
  userInfo: (state: UserState) => state.userInfo,
  isLogin: (state: UserState) => state.isLogin,
  token: (state: UserState) => state.token,
  permissions: (state: UserState) => state.permissions,
  roles: (state: UserState) => state.roles,
  preferences: (state: UserState) => state.preferences,
  
  // 计算属性选择器
  displayName: (state: UserState) => {
    const { userInfo } = state;
    return userInfo?.nickname || userInfo?.phone || '未知用户';
  },
  
  isAdmin: (state: UserState) => {
    return state.roles.includes('admin') || state.roles.includes('super_admin');
  },
  
  sessionInfo: (state: UserState) => ({
    isValid: state.isSessionValid(),
    duration: state.getSessionDuration(),
    loginTime: state.loginTime,
    lastActiveTime: state.lastActiveTime,
  }),
};

/**
 * 用户状态 Hook
 */
export function useUser() {
  const store = useUserStore();
  
  return {
    // 状态
    userInfo: store.userInfo,
    isLogin: store.isLogin,
    token: store.token,
    permissions: store.permissions,
    roles: store.roles,
    preferences: store.preferences,
    
    // 计算属性
    displayName: userSelectors.displayName(store),
    isAdmin: userSelectors.isAdmin(store),
    sessionInfo: userSelectors.sessionInfo(store),
    
    // 方法
    login: store.login,
    logout: store.logout,
    updateProfile: store.updateProfile,
    setPreferences: store.setPreferences,
    hasPermission: store.hasPermission,
    hasRole: store.hasRole,
    hasAnyPermission: store.hasAnyPermission,
    hasAllPermissions: store.hasAllPermissions,
    hasAnyRole: store.hasAnyRole,
    hasAllRoles: store.hasAllRoles,
    refreshSession: store.refreshSession,
  };
}

/**
 * 权限检查 Hook
 */
export function usePermissions() {
  const {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,
    hasAllRoles,
    permissions,
    roles,
    isAdmin,
  } = useUser();
  
  return {
    permissions,
    roles,
    isAdmin,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,
    hasAllRoles,
  };
}
