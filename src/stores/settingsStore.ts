/**
 * 设置状态管理
 */

import { create } from 'zustand';
import { composeMiddleware, createPersistConfig } from './middleware';

/**
 * 设置状态接口
 */
export interface SettingsState {
  // 通用设置
  autoLogin: boolean;
  rememberPassword: boolean;
  biometricAuth: boolean;
  
  // 通知设置
  pushNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  notificationTypes: {
    system: boolean;
    message: boolean;
    update: boolean;
    promotion: boolean;
  };
  
  // 隐私设置
  locationAccess: boolean;
  cameraAccess: boolean;
  microphoneAccess: boolean;
  contactsAccess: boolean;
  analyticsEnabled: boolean;
  crashReportEnabled: boolean;
  
  // 显示设置
  fontSize: 'small' | 'medium' | 'large';
  autoNightMode: boolean;
  nightModeSchedule: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  animationsEnabled: boolean;
  reducedMotion: boolean;
  
  // 缓存设置
  imageCacheSize: number;
  dataCacheSize: number;
  autoClearCache: boolean;
  cacheExpiry: number;
  
  // 网络设置
  dataUsageMode: 'normal' | 'saver' | 'unlimited';
  autoDownloadImages: boolean;
  autoDownloadVideos: boolean;
  wifiOnlyDownload: boolean;
  
  // 语言和地区设置
  language: string;
  region: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  currency: string;
  
  // 开发者设置
  debugMode: boolean;
  showPerformanceStats: boolean;
  enableLogging: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  
  // Actions
  updateSetting: <K extends keyof Omit<SettingsState, 'updateSetting' | 'resetSettings' | 'exportSettings' | 'importSettings'>>(
    key: K,
    value: SettingsState[K]
  ) => void;
  updateNotificationTypes: (types: Partial<SettingsState['notificationTypes']>) => void;
  updateNightModeSchedule: (schedule: Partial<SettingsState['nightModeSchedule']>) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (settings: string) => boolean;
}

/**
 * 默认设置
 */
const defaultSettings: Omit<SettingsState, 'updateSetting' | 'resetSettings' | 'exportSettings' | 'importSettings' | 'updateNotificationTypes' | 'updateNightModeSchedule'> = {
  // 通用设置
  autoLogin: false,
  rememberPassword: true,
  biometricAuth: false,
  
  // 通知设置
  pushNotifications: true,
  soundEnabled: true,
  vibrationEnabled: true,
  notificationTypes: {
    system: true,
    message: true,
    update: true,
    promotion: false,
  },
  
  // 隐私设置
  locationAccess: false,
  cameraAccess: false,
  microphoneAccess: false,
  contactsAccess: false,
  analyticsEnabled: true,
  crashReportEnabled: true,
  
  // 显示设置
  fontSize: 'medium',
  autoNightMode: false,
  nightModeSchedule: {
    enabled: false,
    startTime: '22:00',
    endTime: '06:00',
  },
  animationsEnabled: true,
  reducedMotion: false,
  
  // 缓存设置
  imageCacheSize: 100, // MB
  dataCacheSize: 50, // MB
  autoClearCache: true,
  cacheExpiry: 7, // 天
  
  // 网络设置
  dataUsageMode: 'normal',
  autoDownloadImages: true,
  autoDownloadVideos: false,
  wifiOnlyDownload: false,
  
  // 语言和地区设置
  language: 'zh-CN',
  region: 'CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24h',
  currency: 'CNY',
  
  // 开发者设置
  debugMode: process.env.NODE_ENV === 'development',
  showPerformanceStats: false,
  enableLogging: true,
  logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'warn',
};

/**
 * 创建设置状态 Store
 */
export const useSettingsStore = create<SettingsState>()(
  composeMiddleware(
    (set, get) => ({
      // 初始状态
      ...defaultSettings,

      // 更新设置
      updateSetting: (key, value) => {
        set({ [key]: value });
      },

      // 更新通知类型设置
      updateNotificationTypes: (types) => {
        set((state) => ({
          notificationTypes: { ...state.notificationTypes, ...types },
        }));
      },

      // 更新夜间模式计划
      updateNightModeSchedule: (schedule) => {
        set((state) => ({
          nightModeSchedule: { ...state.nightModeSchedule, ...schedule },
        }));
      },

      // 重置设置
      resetSettings: () => {
        set(defaultSettings);
      },

      // 导出设置
      exportSettings: () => {
        const state = get();
        const settings = { ...state };
        
        // 移除方法
        delete (settings as any).updateSetting;
        delete (settings as any).resetSettings;
        delete (settings as any).exportSettings;
        delete (settings as any).importSettings;
        delete (settings as any).updateNotificationTypes;
        delete (settings as any).updateNightModeSchedule;
        
        return JSON.stringify(settings, null, 2);
      },

      // 导入设置
      importSettings: (settingsJson) => {
        try {
          const settings = JSON.parse(settingsJson);
          
          // 验证设置格式
          if (typeof settings !== 'object' || settings === null) {
            return false;
          }
          
          // 只导入有效的设置项
          const validKeys = Object.keys(defaultSettings);
          const validSettings: any = {};
          
          validKeys.forEach(key => {
            if (key in settings) {
              validSettings[key] = settings[key];
            }
          });
          
          set(validSettings);
          return true;
        } catch (error) {
          console.error('Failed to import settings:', error);
          return false;
        }
      },
    }),
    {
      name: 'SettingsStore',
      persist: createPersistConfig('settings-storage'),
      enableDevtools: true,
      enableLogger: true,
    }
  )
);

/**
 * 设置状态选择器
 */
export const settingsSelectors = {
  // 通用设置选择器
  authSettings: (state: SettingsState) => ({
    autoLogin: state.autoLogin,
    rememberPassword: state.rememberPassword,
    biometricAuth: state.biometricAuth,
  }),
  
  // 通知设置选择器
  notificationSettings: (state: SettingsState) => ({
    pushNotifications: state.pushNotifications,
    soundEnabled: state.soundEnabled,
    vibrationEnabled: state.vibrationEnabled,
    notificationTypes: state.notificationTypes,
  }),
  
  // 隐私设置选择器
  privacySettings: (state: SettingsState) => ({
    locationAccess: state.locationAccess,
    cameraAccess: state.cameraAccess,
    microphoneAccess: state.microphoneAccess,
    contactsAccess: state.contactsAccess,
    analyticsEnabled: state.analyticsEnabled,
    crashReportEnabled: state.crashReportEnabled,
  }),
  
  // 显示设置选择器
  displaySettings: (state: SettingsState) => ({
    fontSize: state.fontSize,
    autoNightMode: state.autoNightMode,
    nightModeSchedule: state.nightModeSchedule,
    animationsEnabled: state.animationsEnabled,
    reducedMotion: state.reducedMotion,
  }),
  
  // 缓存设置选择器
  cacheSettings: (state: SettingsState) => ({
    imageCacheSize: state.imageCacheSize,
    dataCacheSize: state.dataCacheSize,
    autoClearCache: state.autoClearCache,
    cacheExpiry: state.cacheExpiry,
  }),
  
  // 网络设置选择器
  networkSettings: (state: SettingsState) => ({
    dataUsageMode: state.dataUsageMode,
    autoDownloadImages: state.autoDownloadImages,
    autoDownloadVideos: state.autoDownloadVideos,
    wifiOnlyDownload: state.wifiOnlyDownload,
  }),
  
  // 语言和地区设置选择器
  localeSettings: (state: SettingsState) => ({
    language: state.language,
    region: state.region,
    timezone: state.timezone,
    dateFormat: state.dateFormat,
    timeFormat: state.timeFormat,
    currency: state.currency,
  }),
  
  // 开发者设置选择器
  developerSettings: (state: SettingsState) => ({
    debugMode: state.debugMode,
    showPerformanceStats: state.showPerformanceStats,
    enableLogging: state.enableLogging,
    logLevel: state.logLevel,
  }),
};

/**
 * 设置状态 Hook
 */
export function useSettings() {
  const store = useSettingsStore();
  
  return {
    // 所有设置
    settings: store,
    
    // 分类设置
    authSettings: settingsSelectors.authSettings(store),
    notificationSettings: settingsSelectors.notificationSettings(store),
    privacySettings: settingsSelectors.privacySettings(store),
    displaySettings: settingsSelectors.displaySettings(store),
    cacheSettings: settingsSelectors.cacheSettings(store),
    networkSettings: settingsSelectors.networkSettings(store),
    localeSettings: settingsSelectors.localeSettings(store),
    developerSettings: settingsSelectors.developerSettings(store),
    
    // 方法
    updateSetting: store.updateSetting,
    updateNotificationTypes: store.updateNotificationTypes,
    updateNightModeSchedule: store.updateNightModeSchedule,
    resetSettings: store.resetSettings,
    exportSettings: store.exportSettings,
    importSettings: store.importSettings,
  };
}

/**
 * 通知设置 Hook
 */
export function useNotificationSettings() {
  const store = useSettingsStore();
  
  return {
    settings: settingsSelectors.notificationSettings(store),
    updateTypes: store.updateNotificationTypes,
    updateSetting: store.updateSetting,
  };
}

/**
 * 显示设置 Hook
 */
export function useDisplaySettings() {
  const store = useSettingsStore();
  
  return {
    settings: settingsSelectors.displaySettings(store),
    updateSchedule: store.updateNightModeSchedule,
    updateSetting: store.updateSetting,
  };
}

/**
 * 隐私设置 Hook
 */
export function usePrivacySettings() {
  const store = useSettingsStore();
  
  return {
    settings: settingsSelectors.privacySettings(store),
    updateSetting: store.updateSetting,
  };
}

/**
 * 开发者设置 Hook
 */
export function useDeveloperSettings() {
  const store = useSettingsStore();
  
  return {
    settings: settingsSelectors.developerSettings(store),
    updateSetting: store.updateSetting,
  };
}
