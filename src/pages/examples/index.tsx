/**
 * 组件示例页面
 * 展示各种组件的使用方法和最佳实践
 */

import React, { useState } from 'react';
import { View, Text } from '@tarojs/components';
import {
  Button,
  Input,
  Modal,
  Container,
  Row,
  Col,
  Space,
  Divider,
  Icon,
  Loading,
  Transition
} from '@/components';
import { useAppStore } from '@/stores/appStore';
import './index.less';

const ExamplesPage: React.FC = () => {
  const { theme, toggleTheme } = useAppStore();
  const [modalVisible, setModalVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [transitionVisible, setTransitionVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  // 模拟异步操作
  const handleAsyncAction = async () => {
    setLoading(true);
    try {
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('操作完成');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="examples-page" safeArea>
      <View className="examples-header">
        <Text className="examples-title">组件示例</Text>
        <Button size="sm" onClick={toggleTheme}>
          {theme === 'light' ? '🌙' : '☀️'} 切换主题
        </Button>
      </View>

      <Divider>按钮组件</Divider>
      <View className="examples-section">
        <Text className="section-title">不同类型的按钮</Text>
        <Space wrap>
          <Button type="default">默认按钮</Button>
          <Button type="primary">主要按钮</Button>
          <Button type="secondary">次要按钮</Button>
          <Button type="danger">危险按钮</Button>
          <Button type="ghost">幽灵按钮</Button>
        </Space>

        <Text className="section-title">不同尺寸的按钮</Text>
        <Space>
          <Button size="sm">小按钮</Button>
          <Button size="md">中按钮</Button>
          <Button size="lg">大按钮</Button>
        </Space>

        <Text className="section-title">特殊状态的按钮</Text>
        <Space>
          <Button loading>加载中</Button>
          <Button disabled>禁用状态</Button>
          <Button round>圆角按钮</Button>
          <Button block>块级按钮</Button>
        </Space>
      </View>

      <Divider>输入框组件</Divider>
      <View className="examples-section">
        <Text className="section-title">基础输入框</Text>
        <Space direction="vertical" size="md">
          <Input
            placeholder="请输入用户名"
            value={inputValue}
            onChange={setInputValue}
          />
          <Input
            type="password"
            placeholder="请输入密码"
          />
          <Input
            type="number"
            placeholder="请输入数字"
          />
          <Input
            type="textarea"
            placeholder="请输入多行文本"
            maxLength={200}
          />
        </Space>

        <Text className="section-title">带图标的输入框</Text>
        <Space direction="vertical" size="md">
          <Input
            placeholder="搜索"
            prefix={<Icon name="search" />}
          />
          <Input
            placeholder="邮箱"
            suffix={<Icon name="mail" />}
          />
        </Space>

        <Text className="section-title">验证状态</Text>
        <Space direction="vertical" size="md">
          <Input status="success" placeholder="验证成功" />
          <Input status="warning" placeholder="验证警告" />
          <Input status="error" placeholder="验证失败" />
        </Space>
      </View>

      <Divider>模态框组件</Divider>
      <View className="examples-section">
        <Text className="section-title">基础模态框</Text>
        <Space>
          <Button onClick={() => setModalVisible(true)}>
            打开模态框
          </Button>
        </Space>

        <Modal
          visible={modalVisible}
          title="示例模态框"
          onClose={() => setModalVisible(false)}
        >
          <View className="modal-content">
            <Text>这是一个示例模态框的内容。</Text>
            <Text>您可以在这里放置任何内容。</Text>
          </View>
        </Modal>
      </View>

      <Divider>布局组件</Divider>
      <View className="examples-section">
        <Text className="section-title">网格布局</Text>
        <Row gutter={16}>
          <Col span={6}>
            <View className="grid-item">1/4</View>
          </Col>
          <Col span={6}>
            <View className="grid-item">1/4</View>
          </Col>
          <Col span={6}>
            <View className="grid-item">1/4</View>
          </Col>
          <Col span={6}>
            <View className="grid-item">1/4</View>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <View className="grid-item">1/3</View>
          </Col>
          <Col span={8}>
            <View className="grid-item">1/3</View>
          </Col>
          <Col span={8}>
            <View className="grid-item">1/3</View>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <View className="grid-item">1/2</View>
          </Col>
          <Col span={12}>
            <View className="grid-item">1/2</View>
          </Col>
        </Row>

        <Text className="section-title">间距组件</Text>
        <Space direction="vertical" size="lg">
          <Space>
            <Button>按钮1</Button>
            <Button>按钮2</Button>
            <Button>按钮3</Button>
          </Space>
          <Space size="lg">
            <Button>大间距</Button>
            <Button>大间距</Button>
          </Space>
        </Space>
      </View>

      <Divider>图标组件</Divider>
      <View className="examples-section">
        <Text className="section-title">基础图标</Text>
        <Space wrap>
          <Icon name="home" />
          <Icon name="user" />
          <Icon name="settings" />
          <Icon name="search" />
          <Icon name="heart" />
          <Icon name="star" />
        </Space>

        <Text className="section-title">不同尺寸</Text>
        <Space>
          <Icon name="star" size="sm" />
          <Icon name="star" size="md" />
          <Icon name="star" size="lg" />
          <Icon name="star" size="xl" />
        </Space>

        <Text className="section-title">特殊效果</Text>
        <Space>
          <Icon name="loading" spin />
          <Icon name="heart" color="#ff4757" />
          <Icon name="bell" badge badgeContent="5" />
        </Space>
      </View>

      <Divider>动画组件</Divider>
      <View className="examples-section">
        <Text className="section-title">过渡动画</Text>
        <Space direction="vertical" size="md">
          <Button onClick={() => setTransitionVisible(!transitionVisible)}>
            切换显示
          </Button>
          <Transition visible={transitionVisible} type="fade">
            <View className="transition-content">
              淡入淡出动画
            </View>
          </Transition>
        </Space>

        <Text className="section-title">加载动画</Text>
        <Space wrap>
          <Loading type="spinner" />
          <Loading type="dots" />
          <Loading type="pulse" />
          <Loading type="wave" />
        </Space>

        <Text className="section-title">异步操作示例</Text>
        <Space direction="vertical" size="md">
          <Button onClick={handleAsyncAction} loading={loading}>
            {loading ? '处理中...' : '开始异步操作'}
          </Button>
          {loading && (
            <Loading text="正在处理，请稍候..." />
          )}
        </Space>
      </View>

      <Divider>最佳实践</Divider>
      <View className="examples-section">
        <Text className="section-title">表单示例</Text>
        <View className="form-example">
          <Space direction="vertical" size="md">
            <Input placeholder="用户名" />
            <Input type="password" placeholder="密码" />
            <Space>
              <Button type="primary" block>登录</Button>
              <Button block>取消</Button>
            </Space>
          </Space>
        </View>

        <Text className="section-title">卡片布局</Text>
        <Row gutter={16}>
          <Col span={12}>
            <View className="card-example">
              <Icon name="user" size="lg" />
              <Text className="card-title">用户管理</Text>
              <Text className="card-desc">管理系统用户</Text>
            </View>
          </Col>
          <Col span={12}>
            <View className="card-example">
              <Icon name="settings" size="lg" />
              <Text className="card-title">系统设置</Text>
              <Text className="card-desc">配置系统参数</Text>
            </View>
          </Col>
        </Row>
      </View>
    </Container>
  );
};

export default ExamplesPage;
