/**
 * 示例页面样式
 */

@import '../../styles/variables.less';

.examples-page {
  padding: @spacing-4;
  background-color: var(--color-bg-primary);
  min-height: 100vh;

  .examples-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-6;
    padding: @spacing-4;
    background-color: var(--color-bg-secondary);
    border-radius: @border-radius-lg;

    .examples-title {
      font-size: @font-size-2xl;
      font-weight: @font-weight-bold;
      color: var(--color-text-primary);
    }
  }

  .examples-section {
    margin-bottom: @spacing-8;

    .section-title {
      display: block;
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: var(--color-text-primary);
      margin: @spacing-4 0 @spacing-3 0;
    }
  }

  // 网格示例样式
  .grid-item {
    padding: @spacing-3;
    background-color: var(--color-primary);
    color: @color-white;
    text-align: center;
    border-radius: @border-radius-base;
    font-weight: @font-weight-medium;
  }

  // 过渡动画内容样式
  .transition-content {
    padding: @spacing-4;
    background-color: var(--color-bg-secondary);
    border-radius: @border-radius-base;
    text-align: center;
    color: var(--color-text-primary);
  }

  // 表单示例样式
  .form-example {
    padding: @spacing-4;
    background-color: var(--color-bg-secondary);
    border-radius: @border-radius-lg;
    border: 1px solid var(--color-border);
  }

  // 卡片示例样式
  .card-example {
    padding: @spacing-4;
    background-color: var(--color-bg-secondary);
    border-radius: @border-radius-lg;
    border: 1px solid var(--color-border);
    text-align: center;
    transition: all @duration-base @ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: @box-shadow-md;
    }

    .card-title {
      display: block;
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: var(--color-text-primary);
      margin: @spacing-2 0;
    }

    .card-desc {
      display: block;
      font-size: @font-size-sm;
      color: var(--color-text-secondary);
      line-height: @line-height-relaxed;
    }
  }

  // 模态框内容样式
  .modal-content {
    padding: @spacing-2 0;

    text {
      display: block;
      margin-bottom: @spacing-2;
      color: var(--color-text-primary);
      line-height: @line-height-relaxed;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    padding: @spacing-2;

    .examples-header {
      flex-direction: column;
      gap: @spacing-3;
      text-align: center;

      .examples-title {
        font-size: @font-size-xl;
      }
    }

    .examples-section {
      margin-bottom: @spacing-6;

      .section-title {
        font-size: @font-size-md;
      }
    }

    .card-example {
      margin-bottom: @spacing-3;
    }
  }

  // 暗色主题适配
  &.dark {
    .grid-item {
      background-color: var(--color-primary-dark);
    }

    .card-example {
      &:hover {
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
      }
    }
  }
}
