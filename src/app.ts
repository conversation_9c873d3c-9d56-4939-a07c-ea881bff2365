import { PropsWithChildren, useEffect } from 'react'
import { useLaunch, useDidShow, useDidHide } from '@tarojs/taro'
import { useAppStore } from './store'
import PlatformAdapter from './utils/platform'

import './app.less'
import './styles/global.scss'

function App({ children }: PropsWithChildren<any>) {
  const { setPlatform, setTheme } = useAppStore()

  useLaunch(() => {
    console.log('App launched.')
    
    // 初始化平台信息
    const platform = PlatformAdapter.getCurrentPlatform()
    setPlatform(platform)
    
    // 初始化主题
    const savedTheme = PlatformAdapter.storage.get('theme') || 'light'
    setTheme(savedTheme)
    
    // 输出平台信息
    console.log('Platform:', platform)
    console.log('System Info:', PlatformAdapter.getSystemInfo())
  })

  useDidShow(() => {
    console.log('App did show.')
  })

  useDidHide(() => {
    console.log('App did hide.')
  })

  useEffect(() => {
    // 应用启动后的额外初始化逻辑
    const initApp = async () => {
      try {
        // 检查网络状态
        const networkType = await PlatformAdapter.getNetworkType()
        console.log('Network type:', networkType)
        
        // 可以在这里添加其他初始化逻辑
        // 比如检查更新、获取系统配置等
      } catch (error) {
        console.error('App initialization error:', error)
      }
    }
    
    initApp()
  }, [])

  // children 是将要会渲染的页面
  return children
}
  


export default App
