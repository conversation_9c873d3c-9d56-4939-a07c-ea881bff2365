/**
 * 打印样式
 * 定义打印时的样式规则
 */

@import './variables.scss';

// ==================== 打印媒体查询 ====================

@media print {
  // ==================== 全局打印设置 ====================
  
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  
  html {
    font-size: 12pt;
  }
  
  body {
    margin: 0;
    padding: 0;
    font-family: "Times New Roman", serif;
    font-size: 12pt;
    line-height: 1.4;
    color: #000;
    background: #fff;
  }
  
  // ==================== 页面设置 ====================
  
  @page {
    margin: 2cm;
    size: A4;
  }
  
  @page :first {
    margin-top: 3cm;
  }
  
  @page :left {
    margin-left: 3cm;
    margin-right: 2cm;
  }
  
  @page :right {
    margin-left: 2cm;
    margin-right: 3cm;
  }
  
  // ==================== 隐藏不需要打印的元素 ====================
  
  .no-print,
  .print-hidden,
  nav,
  .navbar,
  .sidebar,
  .menu,
  .toolbar,
  .footer,
  .pagination,
  .breadcrumb,
  .tabs,
  .modal,
  .popup,
  .tooltip,
  .dropdown,
  .overlay,
  button:not(.print-button),
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  .btn:not(.print-button),
  .button:not(.print-button),
  .form-actions,
  .actions,
  .controls,
  .edit-controls,
  .admin-controls,
  .social-share,
  .comments-form,
  .advertisement,
  .ads,
  .banner,
  .promo,
  .video,
  .audio,
  .interactive,
  .animation,
  .carousel,
  .slider,
  .gallery-controls,
  .search-form,
  .filters,
  .sort-controls {
    display: none !important;
  }
  
  // ==================== 显示打印专用元素 ====================
  
  .print-only,
  .print-visible {
    display: block !important;
  }
  
  .print-inline {
    display: inline !important;
  }
  
  .print-inline-block {
    display: inline-block !important;
  }
  
  // ==================== 标题样式 ====================
  
  h1, h2, h3, h4, h5, h6 {
    color: #000;
    page-break-after: avoid;
    page-break-inside: avoid;
    font-weight: bold;
    margin-top: 1em;
    margin-bottom: 0.5em;
  }
  
  h1 {
    font-size: 18pt;
    margin-top: 0;
  }
  
  h2 {
    font-size: 16pt;
  }
  
  h3 {
    font-size: 14pt;
  }
  
  h4 {
    font-size: 12pt;
  }
  
  h5, h6 {
    font-size: 11pt;
  }
  
  // ==================== 文本样式 ====================
  
  p {
    margin: 0 0 1em 0;
    orphans: 3;
    widows: 3;
  }
  
  blockquote {
    margin: 1em 2em;
    padding: 0.5em 1em;
    border-left: 3px solid #ccc;
    font-style: italic;
    page-break-inside: avoid;
  }
  
  // ==================== 链接样式 ====================
  
  a {
    color: #000;
    text-decoration: underline;
    
    &[href^="http"]:after {
      content: " (" attr(href) ")";
      font-size: 10pt;
      color: #666;
    }
    
    &[href^="mailto:"]:after {
      content: " (" attr(href) ")";
      font-size: 10pt;
      color: #666;
    }
    
    &[href^="tel:"]:after {
      content: " (" attr(href) ")";
      font-size: 10pt;
      color: #666;
    }
  }
  
  // 内部链接不显示 URL
  a[href^="#"]:after,
  a[href^="/"]:after,
  a[href*="javascript:"]:after {
    content: "";
  }
  
  // ==================== 列表样式 ====================
  
  ul, ol {
    margin: 0 0 1em 0;
    padding-left: 2em;
  }
  
  li {
    margin-bottom: 0.25em;
  }
  
  // ==================== 表格样式 ====================
  
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    page-break-inside: avoid;
  }
  
  th, td {
    border: 1px solid #000;
    padding: 0.5em;
    text-align: left;
    vertical-align: top;
  }
  
  th {
    background: #f0f0f0;
    font-weight: bold;
  }
  
  thead {
    display: table-header-group;
  }
  
  tbody {
    display: table-row-group;
  }
  
  tfoot {
    display: table-footer-group;
  }
  
  // ==================== 图片样式 ====================
  
  img {
    max-width: 100% !important;
    height: auto !important;
    page-break-inside: avoid;
    page-break-after: avoid;
  }
  
  figure {
    margin: 1em 0;
    page-break-inside: avoid;
  }
  
  figcaption {
    font-size: 10pt;
    font-style: italic;
    text-align: center;
    margin-top: 0.5em;
  }
  
  // ==================== 代码样式 ====================
  
  code {
    font-family: "Courier New", monospace;
    font-size: 10pt;
    background: #f5f5f5;
    padding: 0.1em 0.3em;
    border: 1px solid #ddd;
  }
  
  pre {
    font-family: "Courier New", monospace;
    font-size: 9pt;
    background: #f5f5f5;
    padding: 1em;
    border: 1px solid #ddd;
    white-space: pre-wrap;
    page-break-inside: avoid;
    overflow: visible;
  }
  
  // ==================== 分页控制 ====================
  
  .page-break-before {
    page-break-before: always;
  }
  
  .page-break-after {
    page-break-after: always;
  }
  
  .page-break-inside-avoid {
    page-break-inside: avoid;
  }
  
  .page-break-inside-auto {
    page-break-inside: auto;
  }
  
  // ==================== 打印专用样式 ====================
  
  .print-header {
    position: running(header);
    text-align: center;
    font-size: 10pt;
    border-bottom: 1px solid #000;
    padding-bottom: 0.5em;
    margin-bottom: 1em;
  }
  
  .print-footer {
    position: running(footer);
    text-align: center;
    font-size: 10pt;
    border-top: 1px solid #000;
    padding-top: 0.5em;
    margin-top: 1em;
  }
  
  .print-page-number:after {
    content: counter(page);
  }
  
  .print-total-pages:after {
    content: counter(pages);
  }
  
  // ==================== 表单样式 ====================
  
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="url"],
  textarea,
  select {
    border: 1px solid #000;
    padding: 0.2em;
    background: #fff;
  }
  
  input[type="checkbox"],
  input[type="radio"] {
    -webkit-appearance: none;
    appearance: none;
    width: 0.8em;
    height: 0.8em;
    border: 1px solid #000;
    margin-right: 0.3em;
    
    &:checked {
      background: #000;
    }
  }
  
  input[type="checkbox"]:checked:after {
    content: "✓";
    color: #fff;
    font-size: 0.6em;
    text-align: center;
    line-height: 0.8em;
  }
  
  input[type="radio"] {
    border-radius: 50%;
  }
  
  // ==================== 工具类 ====================
  
  .print-text-left {
    text-align: left !important;
  }
  
  .print-text-center {
    text-align: center !important;
  }
  
  .print-text-right {
    text-align: right !important;
  }
  
  .print-text-justify {
    text-align: justify !important;
  }
  
  .print-font-small {
    font-size: 10pt !important;
  }
  
  .print-font-large {
    font-size: 14pt !important;
  }
  
  .print-bold {
    font-weight: bold !important;
  }
  
  .print-italic {
    font-style: italic !important;
  }
  
  .print-underline {
    text-decoration: underline !important;
  }
  
  .print-no-margin {
    margin: 0 !important;
  }
  
  .print-no-padding {
    padding: 0 !important;
  }
  
  .print-margin-top {
    margin-top: 1em !important;
  }
  
  .print-margin-bottom {
    margin-bottom: 1em !important;
  }
  
  .print-border {
    border: 1px solid #000 !important;
  }
  
  .print-border-top {
    border-top: 1px solid #000 !important;
  }
  
  .print-border-bottom {
    border-bottom: 1px solid #000 !important;
  }
  
  .print-background-gray {
    background: #f0f0f0 !important;
  }
  
  // ==================== 布局调整 ====================
  
  .container,
  .wrapper,
  .content {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    float: none !important;
  }
  
  .row,
  .columns,
  .grid {
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .col,
  .column {
    width: 100% !important;
    float: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  // ==================== 阴影和效果移除 ====================
  
  * {
    box-shadow: none !important;
    text-shadow: none !important;
    filter: none !important;
    transform: none !important;
    transition: none !important;
    animation: none !important;
  }
}
