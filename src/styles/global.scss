/**
 * 全局样式
 * 定义全局的基础样式和重置样式
 */

@import './variables.scss';
@import './mixins.scss';

// ==================== 全局重置 ====================

* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: $line-height-normal;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: $font-family-base;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-normal;
  color: $color-text-primary;
  background-color: $color-bg-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ==================== 标题重置 ====================

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
  color: $color-text-primary;
}

h1 {
  font-size: $font-size-4xl;
}

h2 {
  font-size: $font-size-3xl;
}

h3 {
  font-size: $font-size-2xl;
}

h4 {
  font-size: $font-size-xl;
}

h5 {
  font-size: $font-size-lg;
}

h6 {
  font-size: $font-size-base;
}

// ==================== 文本元素重置 ====================

p {
  margin: 0 0 $spacing-4 0;
  line-height: $line-height-relaxed;
}

a {
  color: $color-primary;
  text-decoration: none;
  @include transition(color);
  
  &:hover {
    color: $color-primary-dark;
  }
  
  &:active {
    color: $color-primary-light;
  }
}

strong, b {
  font-weight: $font-weight-bold;
}

em, i {
  font-style: italic;
}

small {
  font-size: $font-size-sm;
}

code {
  font-family: $font-family-mono;
  font-size: 0.875em;
  padding: 2px 4px;
  background-color: $color-gray-100;
  border-radius: $border-radius-sm;
  color: $color-danger;
}

pre {
  font-family: $font-family-mono;
  font-size: $font-size-sm;
  line-height: $line-height-relaxed;
  padding: $spacing-4;
  background-color: $color-gray-100;
  border-radius: $border-radius-base;
  overflow-x: auto;
  
  code {
    padding: 0;
    background: none;
    color: inherit;
  }
}

// ==================== 列表重置 ====================

ul, ol {
  margin: 0 0 $spacing-4 0;
  padding-left: $spacing-6;
}

li {
  margin-bottom: $spacing-1;
}

// ==================== 表格重置 ====================

table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  padding: $spacing-3 $spacing-4;
  text-align: left;
  border-bottom: 1px solid $color-border;
}

th {
  font-weight: $font-weight-semibold;
  background-color: $color-bg-secondary;
}

// ==================== 表单元素重置 ====================

button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

button {
  @include button-reset;
}

input, select, textarea {
  border: 1px solid $color-border;
  border-radius: $border-radius-base;
  padding: $spacing-2 $spacing-3;
  background-color: $color-white;
  @include transition(border-color);
  
  &:focus {
    outline: none;
    border-color: $color-primary;
    box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
  }
  
  &:disabled {
    background-color: $color-bg-disabled;
    color: $color-text-disabled;
    cursor: not-allowed;
  }
}

textarea {
  resize: vertical;
  min-height: 80px;
}

// ==================== 图片和媒体 ====================

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

svg {
  vertical-align: middle;
}

// ==================== 工具类 ====================

// 显示/隐藏
.show {
  display: block !important;
}

.hide {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

.visible {
  visibility: visible !important;
}

// 文本对齐
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

.text-justify {
  text-align: justify !important;
}

// 文本颜色
.text-primary {
  color: $color-primary !important;
}

.text-success {
  color: $color-success !important;
}

.text-warning {
  color: $color-warning !important;
}

.text-danger {
  color: $color-danger !important;
}

.text-info {
  color: $color-info !important;
}

.text-muted {
  color: $color-text-secondary !important;
}

// 背景颜色
.bg-primary {
  background-color: $color-primary !important;
}

.bg-success {
  background-color: $color-success !important;
}

.bg-warning {
  background-color: $color-warning !important;
}

.bg-danger {
  background-color: $color-danger !important;
}

.bg-info {
  background-color: $color-info !important;
}

.bg-light {
  background-color: $color-bg-secondary !important;
}

.bg-dark {
  background-color: $color-gray-800 !important;
}

// 边距工具类
@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in (
    0: 0,
    1: $spacing-1,
    2: $spacing-2,
    3: $spacing-3,
    4: $spacing-4,
    5: $spacing-5,
    6: $spacing-6,
    8: $spacing-8,
    10: $spacing-10,
    12: $spacing-12,
    16: $spacing-16,
    20: $spacing-20,
    24: $spacing-24
  ) {
    .#{$abbrev}-#{$size} {
      #{$prop}: $length !important;
    }
    
    .#{$abbrev}t-#{$size} {
      #{$prop}-top: $length !important;
    }
    
    .#{$abbrev}r-#{$size} {
      #{$prop}-right: $length !important;
    }
    
    .#{$abbrev}b-#{$size} {
      #{$prop}-bottom: $length !important;
    }
    
    .#{$abbrev}l-#{$size} {
      #{$prop}-left: $length !important;
    }
    
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length !important;
      #{$prop}-right: $length !important;
    }
    
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length !important;
      #{$prop}-bottom: $length !important;
    }
  }
}

// Flexbox 工具类
.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-start {
  justify-content: flex-start !important;
}

.justify-center {
  justify-content: center !important;
}

.justify-end {
  justify-content: flex-end !important;
}

.justify-between {
  justify-content: space-between !important;
}

.justify-around {
  justify-content: space-around !important;
}

.align-start {
  align-items: flex-start !important;
}

.align-center {
  align-items: center !important;
}

.align-end {
  align-items: flex-end !important;
}

.align-stretch {
  align-items: stretch !important;
}

.flex-1 {
  flex: 1 !important;
}

.flex-auto {
  flex: auto !important;
}

.flex-none {
  flex: none !important;
}

// 圆角工具类
.rounded-none {
  border-radius: 0 !important;
}

.rounded-sm {
  border-radius: $border-radius-sm !important;
}

.rounded {
  border-radius: $border-radius-base !important;
}

.rounded-md {
  border-radius: $border-radius-md !important;
}

.rounded-lg {
  border-radius: $border-radius-lg !important;
}

.rounded-xl {
  border-radius: $border-radius-xl !important;
}

.rounded-full {
  border-radius: $border-radius-full !important;
}

// 阴影工具类
.shadow-none {
  box-shadow: none !important;
}

.shadow-sm {
  box-shadow: $box-shadow-sm !important;
}

.shadow {
  box-shadow: $box-shadow-base !important;
}

.shadow-md {
  box-shadow: $box-shadow-md !important;
}

.shadow-lg {
  box-shadow: $box-shadow-lg !important;
}

.shadow-xl {
  box-shadow: $box-shadow-xl !important;
}

// ==================== 暗色主题 ====================

[data-theme="dark"] {
  body {
    color: $dark-color-text-primary;
    background-color: $dark-color-bg-primary;
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: $dark-color-text-primary;
  }
  
  code {
    background-color: $color-gray-800;
    color: $color-danger-light;
  }
  
  pre {
    background-color: $color-gray-800;
  }
  
  th {
    background-color: $dark-color-bg-tertiary;
  }
  
  th, td {
    border-bottom-color: $dark-color-border;
  }
  
  input, select, textarea {
    background-color: $dark-color-bg-tertiary;
    border-color: $dark-color-border;
    color: $dark-color-text-primary;
    
    &:focus {
      border-color: $color-primary;
    }
    
    &:disabled {
      background-color: $color-gray-900;
      color: $color-gray-600;
    }
  }
}
