/**
 * 样式系统入口文件
 * 统一导入所有样式文件
 */

// 基础样式
@import './variables.less';
@import './mixins.less';
@import './themes.less';
@import './global.less';

// 组件样式（如果需要全局导入）
// @import '../components/Button/index.less';
// @import '../components/Input/index.less';
// @import '../components/Modal/index.less';

// 页面样式（如果需要全局导入）
// @import '../pages/index/index.less';
// @import '../pages/profile/index.less';

// 工具样式
@import './utilities.less';

/**
 * 样式系统版本信息
 */
@style-system-version: '1.0.0';

/**
 * 样式系统配置
 */
@enable-responsive: true;
@enable-dark-mode: true;
@enable-animations: true;
@enable-shadows: true;
@enable-gradients: true;
@enable-transitions: true;
@enable-utilities: true;

/**
 * 条件导入样式
 */

// 响应式样式
& when (@enable-responsive = true) {
  @import './responsive.less';
}

// 动画样式
& when (@enable-animations = true) {
  @import './animations.less';
}

// 打印样式
@import './print.less';
