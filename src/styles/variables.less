/**
 * 样式变量定义
 * 定义全局的颜色、字体、间距等设计令牌
 */

// ==================== 颜色系统 ====================

// 主色调
@color-primary: #007AFF;
@color-primary-light: #4DA3FF;
@color-primary-dark: #0056CC;

// 功能色
@color-success: #34C759;
@color-success-light: #5DD87A;
@color-success-dark: #248A3D;

@color-warning: #FF9500;
@color-warning-light: #FFB340;
@color-warning-dark: #CC7700;

@color-danger: #FF3B30;
@color-danger-light: #FF6B60;
@color-danger-dark: #CC2E24;

@color-info: #5AC8FA;
@color-info-light: #7DD3FC;
@color-info-dark: #0891B2;

// 中性色
@color-white: #FFFFFF;
@color-black: #000000;

@color-gray-50: #F9FAFB;
@color-gray-100: #F3F4F6;
@color-gray-200: #E5E7EB;
@color-gray-300: #D1D5DB;
@color-gray-400: #9CA3AF;
@color-gray-500: #6B7280;
@color-gray-600: #4B5563;
@color-gray-700: #374151;
@color-gray-800: #1F2937;
@color-gray-900: #111827;

// 文本颜色
@color-text-primary: #1F2937;
@color-text-secondary: #6B7280;
@color-text-tertiary: #9CA3AF;
@color-text-disabled: #D1D5DB;
@color-text-placeholder: #9CA3AF;
@color-text-inverse: #FFFFFF;

// 背景色
@color-bg-primary: #FFFFFF;
@color-bg-secondary: #F9FAFB;
@color-bg-tertiary: #F3F4F6;
@color-bg-disabled: #F3F4F6;
@color-bg-mask: rgba(0, 0, 0, 0.5);

// 边框色
@color-border: #E5E7EB;
@color-border-light: #F3F4F6;
@color-border-dark: #D1D5DB;

// 阴影色
@color-shadow: rgba(0, 0, 0, 0.1);
@color-shadow-light: rgba(0, 0, 0, 0.05);
@color-shadow-dark: rgba(0, 0, 0, 0.2);

// ==================== 字体系统 ====================

// 字体族
@font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
@font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

// 字体大小
@font-size-xs: 10px;
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-md: 16px;
@font-size-lg: 18px;
@font-size-xl: 20px;
@font-size-2xl: 24px;
@font-size-3xl: 30px;
@font-size-4xl: 36px;

// 字体粗细
@font-weight-light: 300;
@font-weight-normal: 400;
@font-weight-medium: 500;
@font-weight-semibold: 600;
@font-weight-bold: 700;

// 行高
@line-height-tight: 1.2;
@line-height-normal: 1.5;
@line-height-relaxed: 1.6;
@line-height-loose: 2;

// ==================== 间距系统 ====================

// 基础间距单位
@spacing-unit: 4px;

// 间距尺寸
@spacing-0: 0;
@spacing-1: 4px; // 4px
@spacing-2: 8px; // 8px
@spacing-3: 12px; // 12px
@spacing-4: 16px; // 16px
@spacing-5: 20px; // 20px
@spacing-6: 24px; // 24px
@spacing-8: 32px; // 32px
@spacing-10: 40px; // 40px
@spacing-12: 48px; // 48px
@spacing-16: 64px; // 64px
@spacing-20: 80px; // 80px
@spacing-24: 96px; // 96px

// 常用间距别名
@spacing-xs: @spacing-1;
@spacing-sm: @spacing-2;
@spacing-md: @spacing-4;
@spacing-lg: @spacing-6;
@spacing-xl: @spacing-8;

// ==================== 圆角系统 ====================

@border-radius-none: 0;
@border-radius-sm: 4px;
@border-radius-base: 8px;
@border-radius-md: 12px;
@border-radius-lg: 16px;
@border-radius-xl: 20px;
@border-radius-2xl: 24px;
@border-radius-full: 9999px;

// ==================== 边框系统 ====================

@border-width-none: 0;
@border-width-thin: 0.5px;
@border-width-base: 1px;
@border-width-thick: 2px;

// ==================== 阴影系统 ====================

@box-shadow-none: none;
@box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
@box-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
@box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
@box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
@box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
@box-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// ==================== 动画系统 ====================

// 动画时长
@duration-fast: 0.15s;
@duration-base: 0.3s;
@duration-slow: 0.5s;

// 动画缓动函数
@ease-in: cubic-bezier(0.4, 0, 1, 1);
@ease-out: cubic-bezier(0, 0, 0.2, 1);
@ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
@ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// ==================== 层级系统 ====================

@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal-backdrop: 1040;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;
@z-index-toast: 1080;

// ==================== 断点系统 ====================

@breakpoint-xs: 0;
@breakpoint-sm: 576px;
@breakpoint-md: 768px;
@breakpoint-lg: 992px;
@breakpoint-xl: 1200px;
@breakpoint-2xl: 1400px;

// ==================== 组件尺寸 ====================

// 按钮高度
@button-height-sm: 32px;
@button-height-md: 40px;
@button-height-lg: 48px;

// 输入框高度
@input-height-sm: 32px;
@input-height-md: 40px;
@input-height-lg: 48px;

// 头像尺寸
@avatar-size-xs: 24px;
@avatar-size-sm: 32px;
@avatar-size-md: 40px;
@avatar-size-lg: 64px;
@avatar-size-xl: 80px;

// 图标尺寸
@icon-size-xs: 12px;
@icon-size-sm: 16px;
@icon-size-md: 20px;
@icon-size-lg: 24px;
@icon-size-xl: 32px;

// ==================== 暗色主题 ====================

// 暗色主题颜色
@dark-color-bg-primary: #1F2937;
@dark-color-bg-secondary: #111827;
@dark-color-bg-tertiary: #374151;

@dark-color-text-primary: #F9FAFB;
@dark-color-text-secondary: #D1D5DB;
@dark-color-text-tertiary: #9CA3AF;

@dark-color-border: #374151;
@dark-color-border-light: #4B5563;
@dark-color-border-dark: #1F2937;

// ==================== 特殊效果 ====================

// 毛玻璃效果
@backdrop-blur: blur(20px);
@backdrop-saturate: saturate(180%);

// 渐变色
@gradient-primary: linear-gradient(135deg, @color-primary 0%, @color-primary-light 100%);
@gradient-success: linear-gradient(135deg, @color-success 0%, @color-success-light 100%);
@gradient-warning: linear-gradient(135deg, @color-warning 0%, @color-warning-light 100%);
@gradient-danger: linear-gradient(135deg, @color-danger 0%, @color-danger-light 100%);

// ==================== 安全区域 ====================

// 安全区域变量（用于适配刘海屏等）
@safe-area-inset-top: env(safe-area-inset-top);
@safe-area-inset-right: env(safe-area-inset-right);
@safe-area-inset-bottom: env(safe-area-inset-bottom);
@safe-area-inset-left: env(safe-area-inset-left);
