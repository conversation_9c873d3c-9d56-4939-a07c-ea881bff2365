/**
 * 工具样式类
 * 提供常用的原子化样式类
 */

@import './variables.scss';
@import './mixins.scss';

// ==================== 宽度工具类 ====================

.w-auto { width: auto !important; }
.w-full { width: 100% !important; }
.w-screen { width: 100vw !important; }
.w-min { width: min-content !important; }
.w-max { width: max-content !important; }
.w-fit { width: fit-content !important; }

// 固定宽度
@for $i from 1 through 12 {
  .w-#{$i} { width: percentage($i / 12) !important; }
}

// 像素宽度
$widths: (
  0: 0,
  1: 4px,
  2: 8px,
  3: 12px,
  4: 16px,
  5: 20px,
  6: 24px,
  8: 32px,
  10: 40px,
  12: 48px,
  16: 64px,
  20: 80px,
  24: 96px,
  32: 128px,
  40: 160px,
  48: 192px,
  56: 224px,
  64: 256px,
  72: 288px,
  80: 320px,
  96: 384px
);

@each $name, $value in $widths {
  .w-#{$name} { width: $value !important; }
}

// ==================== 高度工具类 ====================

.h-auto { height: auto !important; }
.h-full { height: 100% !important; }
.h-screen { height: 100vh !important; }
.h-min { height: min-content !important; }
.h-max { height: max-content !important; }
.h-fit { height: fit-content !important; }

// 像素高度
@each $name, $value in $widths {
  .h-#{$name} { height: $value !important; }
}

// ==================== 最小/最大宽高 ====================

.min-w-0 { min-width: 0 !important; }
.min-w-full { min-width: 100% !important; }
.min-w-min { min-width: min-content !important; }
.min-w-max { min-width: max-content !important; }
.min-w-fit { min-width: fit-content !important; }

.max-w-none { max-width: none !important; }
.max-w-xs { max-width: 320px !important; }
.max-w-sm { max-width: 384px !important; }
.max-w-md { max-width: 448px !important; }
.max-w-lg { max-width: 512px !important; }
.max-w-xl { max-width: 576px !important; }
.max-w-2xl { max-width: 672px !important; }
.max-w-3xl { max-width: 768px !important; }
.max-w-4xl { max-width: 896px !important; }
.max-w-5xl { max-width: 1024px !important; }
.max-w-6xl { max-width: 1152px !important; }
.max-w-7xl { max-width: 1280px !important; }
.max-w-full { max-width: 100% !important; }
.max-w-min { max-width: min-content !important; }
.max-w-max { max-width: max-content !important; }
.max-w-fit { max-width: fit-content !important; }

.min-h-0 { min-height: 0 !important; }
.min-h-full { min-height: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

.max-h-full { max-height: 100% !important; }
.max-h-screen { max-height: 100vh !important; }

// ==================== 定位工具类 ====================

.static { position: static !important; }
.fixed { position: fixed !important; }
.absolute { position: absolute !important; }
.relative { position: relative !important; }
.sticky { position: sticky !important; }

// 定位偏移
$positions: (
  0: 0,
  1: 4px,
  2: 8px,
  3: 12px,
  4: 16px,
  5: 20px,
  6: 24px,
  8: 32px,
  10: 40px,
  12: 48px,
  16: 64px,
  20: 80px,
  24: 96px,
  auto: auto,
  '1/2': 50%,
  '1/3': 33.333333%,
  '2/3': 66.666667%,
  '1/4': 25%,
  '3/4': 75%,
  full: 100%
);

@each $name, $value in $positions {
  .top-#{$name} { top: $value !important; }
  .right-#{$name} { right: $value !important; }
  .bottom-#{$name} { bottom: $value !important; }
  .left-#{$name} { left: $value !important; }
}

// ==================== 层级工具类 ====================

.z-auto { z-index: auto !important; }
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

// ==================== 溢出工具类 ====================

.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

// ==================== 字体工具类 ====================

.font-thin { font-weight: 100 !important; }
.font-extralight { font-weight: 200 !important; }
.font-light { font-weight: $font-weight-light !important; }
.font-normal { font-weight: $font-weight-normal !important; }
.font-medium { font-weight: $font-weight-medium !important; }
.font-semibold { font-weight: $font-weight-semibold !important; }
.font-bold { font-weight: $font-weight-bold !important; }
.font-extrabold { font-weight: 800 !important; }
.font-black { font-weight: 900 !important; }

// 字体大小
.text-xs { font-size: $font-size-xs !important; }
.text-sm { font-size: $font-size-sm !important; }
.text-base { font-size: $font-size-base !important; }
.text-lg { font-size: $font-size-lg !important; }
.text-xl { font-size: $font-size-xl !important; }
.text-2xl { font-size: $font-size-2xl !important; }
.text-3xl { font-size: $font-size-3xl !important; }
.text-4xl { font-size: $font-size-4xl !important; }

// 行高
.leading-none { line-height: 1 !important; }
.leading-tight { line-height: $line-height-tight !important; }
.leading-snug { line-height: 1.375 !important; }
.leading-normal { line-height: $line-height-normal !important; }
.leading-relaxed { line-height: $line-height-relaxed !important; }
.leading-loose { line-height: $line-height-loose !important; }

// 文本装饰
.underline { text-decoration: underline !important; }
.line-through { text-decoration: line-through !important; }
.no-underline { text-decoration: none !important; }

// 文本转换
.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }
.normal-case { text-transform: none !important; }

// 文本溢出
.truncate {
  @include text-ellipsis;
}

.text-ellipsis {
  text-overflow: ellipsis !important;
}

.text-clip {
  text-overflow: clip !important;
}

// ==================== 边框工具类 ====================

.border { border-width: $border-width-base !important; }
.border-0 { border-width: 0 !important; }
.border-2 { border-width: 2px !important; }
.border-4 { border-width: 4px !important; }
.border-8 { border-width: 8px !important; }

.border-t { border-top-width: $border-width-base !important; }
.border-r { border-right-width: $border-width-base !important; }
.border-b { border-bottom-width: $border-width-base !important; }
.border-l { border-left-width: $border-width-base !important; }

.border-solid { border-style: solid !important; }
.border-dashed { border-style: dashed !important; }
.border-dotted { border-style: dotted !important; }
.border-double { border-style: double !important; }
.border-none { border-style: none !important; }

// 边框颜色
.border-transparent { border-color: transparent !important; }
.border-current { border-color: currentColor !important; }
.border-primary { border-color: var(--color-primary) !important; }
.border-success { border-color: var(--color-success) !important; }
.border-warning { border-color: var(--color-warning) !important; }
.border-danger { border-color: var(--color-danger) !important; }
.border-info { border-color: var(--color-info) !important; }
.border-gray { border-color: var(--color-border) !important; }

// ==================== 透明度工具类 ====================

.opacity-0 { opacity: 0 !important; }
.opacity-5 { opacity: 0.05 !important; }
.opacity-10 { opacity: 0.1 !important; }
.opacity-20 { opacity: 0.2 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-30 { opacity: 0.3 !important; }
.opacity-40 { opacity: 0.4 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-60 { opacity: 0.6 !important; }
.opacity-70 { opacity: 0.7 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-80 { opacity: 0.8 !important; }
.opacity-90 { opacity: 0.9 !important; }
.opacity-95 { opacity: 0.95 !important; }
.opacity-100 { opacity: 1 !important; }

// ==================== 变换工具类 ====================

.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)) !important; }
.transform-none { transform: none !important; }

// 缩放
.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; }
.scale-50 { --tw-scale-x: 0.5; --tw-scale-y: 0.5; }
.scale-75 { --tw-scale-x: 0.75; --tw-scale-y: 0.75; }
.scale-90 { --tw-scale-x: 0.9; --tw-scale-y: 0.9; }
.scale-95 { --tw-scale-x: 0.95; --tw-scale-y: 0.95; }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }
.scale-125 { --tw-scale-x: 1.25; --tw-scale-y: 1.25; }
.scale-150 { --tw-scale-x: 1.5; --tw-scale-y: 1.5; }

// 旋转
.rotate-0 { --tw-rotate: 0deg; }
.rotate-1 { --tw-rotate: 1deg; }
.rotate-2 { --tw-rotate: 2deg; }
.rotate-3 { --tw-rotate: 3deg; }
.rotate-6 { --tw-rotate: 6deg; }
.rotate-12 { --tw-rotate: 12deg; }
.rotate-45 { --tw-rotate: 45deg; }
.rotate-90 { --tw-rotate: 90deg; }
.rotate-180 { --tw-rotate: 180deg; }

// ==================== 过渡工具类 ====================

.transition-none { transition-property: none !important; }
.transition-all { transition-property: all !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.transition { transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.transition-colors { transition-property: background-color, border-color, color, fill, stroke !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.transition-opacity { transition-property: opacity !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.transition-shadow { transition-property: box-shadow !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
.transition-transform { transition-property: transform !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }

// 过渡时长
.duration-75 { transition-duration: 75ms !important; }
.duration-100 { transition-duration: 100ms !important; }
.duration-150 { transition-duration: 150ms !important; }
.duration-200 { transition-duration: 200ms !important; }
.duration-300 { transition-duration: 300ms !important; }
.duration-500 { transition-duration: 500ms !important; }
.duration-700 { transition-duration: 700ms !important; }
.duration-1000 { transition-duration: 1000ms !important; }

// 过渡缓动
.ease-linear { transition-timing-function: linear !important; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1) !important; }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1) !important; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; }

// ==================== 光标工具类 ====================

.cursor-auto { cursor: auto !important; }
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-help { cursor: help !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

// ==================== 选择工具类 ====================

.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }
.select-auto { user-select: auto !important; }

// ==================== 指针事件工具类 ====================

.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }
