/**
 * 动画样式
 * 定义各种动画效果和关键帧
 */

@import './variables.scss';
@import './mixins.scss';

// ==================== 基础动画关键帧 ====================

// 淡入淡出
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 滑动动画
@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

// 缩放动画
@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: scale(0.3);
  }
}

@keyframes zoomInUp {
  from {
    opacity: 0;
    transform: scale(0.1) translateY(2000px);
  }
  60% {
    opacity: 1;
    transform: scale(0.475) translateY(-60px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 弹跳动画
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-30px);
  }
  70% {
    transform: translateY(-15px);
  }
  90% {
    transform: translateY(-4px);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceOut {
  20% {
    transform: scale(0.9);
  }
  50%, 55% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(0.3);
  }
}

// 旋转动画
@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-200deg);
  }
  to {
    opacity: 1;
    transform: rotate(0);
  }
}

@keyframes rotateOut {
  from {
    opacity: 1;
    transform: rotate(0);
  }
  to {
    opacity: 0;
    transform: rotate(200deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 翻转动画
@keyframes flipInX {
  from {
    opacity: 0;
    transform: perspective(400px) rotateX(90deg);
  }
  40% {
    transform: perspective(400px) rotateX(-20deg);
  }
  60% {
    transform: perspective(400px) rotateX(10deg);
  }
  80% {
    transform: perspective(400px) rotateX(-5deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateX(0deg);
  }
}

@keyframes flipInY {
  from {
    opacity: 0;
    transform: perspective(400px) rotateY(90deg);
  }
  40% {
    transform: perspective(400px) rotateY(-20deg);
  }
  60% {
    transform: perspective(400px) rotateY(10deg);
  }
  80% {
    transform: perspective(400px) rotateY(-5deg);
  }
  to {
    opacity: 1;
    transform: perspective(400px) rotateY(0deg);
  }
}

// 摇摆动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes wobble {
  0% {
    transform: translateX(0%);
  }
  15% {
    transform: translateX(-25%) rotate(-5deg);
  }
  30% {
    transform: translateX(20%) rotate(3deg);
  }
  45% {
    transform: translateX(-15%) rotate(-3deg);
  }
  60% {
    transform: translateX(10%) rotate(2deg);
  }
  75% {
    transform: translateX(-5%) rotate(-1deg);
  }
  100% {
    transform: translateX(0%);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

// 闪烁动画
@keyframes flash {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// ==================== 动画工具类 ====================

// 淡入淡出类
.animate-fade-in {
  animation: fadeIn $duration-base $ease-out;
}

.animate-fade-out {
  animation: fadeOut $duration-base $ease-in;
}

.animate-fade-in-up {
  animation: fadeInUp $duration-base $ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown $duration-base $ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft $duration-base $ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight $duration-base $ease-out;
}

// 滑动类
.animate-slide-in-up {
  animation: slideInUp $duration-base $ease-out;
}

.animate-slide-in-down {
  animation: slideInDown $duration-base $ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft $duration-base $ease-out;
}

.animate-slide-in-right {
  animation: slideInRight $duration-base $ease-out;
}

.animate-slide-out-up {
  animation: slideOutUp $duration-base $ease-in;
}

.animate-slide-out-down {
  animation: slideOutDown $duration-base $ease-in;
}

.animate-slide-out-left {
  animation: slideOutLeft $duration-base $ease-in;
}

.animate-slide-out-right {
  animation: slideOutRight $duration-base $ease-in;
}

// 缩放类
.animate-zoom-in {
  animation: zoomIn $duration-base $ease-out;
}

.animate-zoom-out {
  animation: zoomOut $duration-base $ease-in;
}

.animate-zoom-in-up {
  animation: zoomInUp $duration-slow $ease-out;
}

// 弹跳类
.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-bounce-in {
  animation: bounceIn $duration-slow $ease-out;
}

.animate-bounce-out {
  animation: bounceOut $duration-slow $ease-in;
}

// 旋转类
.animate-rotate-in {
  animation: rotateIn $duration-base $ease-out;
}

.animate-rotate-out {
  animation: rotateOut $duration-base $ease-in;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// 翻转类
.animate-flip-in-x {
  animation: flipInX $duration-slow $ease-out;
}

.animate-flip-in-y {
  animation: flipInY $duration-slow $ease-out;
}

// 摇摆类
.animate-shake {
  animation: shake 0.82s $ease-in-out;
}

.animate-wobble {
  animation: wobble 1s $ease-in-out;
}

// 脉冲类
.animate-pulse {
  animation: pulse 2s $ease-in-out infinite;
}

.animate-heartbeat {
  animation: heartbeat 1.5s $ease-in-out infinite;
}

// 闪烁类
.animate-flash {
  animation: flash 1s $ease-in-out infinite;
}

.animate-blink {
  animation: blink 1s $ease-in-out infinite;
}

// ==================== 动画控制类 ====================

// 动画延迟
.animate-delay-75 { animation-delay: 75ms !important; }
.animate-delay-100 { animation-delay: 100ms !important; }
.animate-delay-150 { animation-delay: 150ms !important; }
.animate-delay-200 { animation-delay: 200ms !important; }
.animate-delay-300 { animation-delay: 300ms !important; }
.animate-delay-500 { animation-delay: 500ms !important; }
.animate-delay-700 { animation-delay: 700ms !important; }
.animate-delay-1000 { animation-delay: 1000ms !important; }

// 动画时长
.animate-duration-75 { animation-duration: 75ms !important; }
.animate-duration-100 { animation-duration: 100ms !important; }
.animate-duration-150 { animation-duration: 150ms !important; }
.animate-duration-200 { animation-duration: 200ms !important; }
.animate-duration-300 { animation-duration: 300ms !important; }
.animate-duration-500 { animation-duration: 500ms !important; }
.animate-duration-700 { animation-duration: 700ms !important; }
.animate-duration-1000 { animation-duration: 1000ms !important; }

// 动画填充模式
.animate-fill-none { animation-fill-mode: none !important; }
.animate-fill-forwards { animation-fill-mode: forwards !important; }
.animate-fill-backwards { animation-fill-mode: backwards !important; }
.animate-fill-both { animation-fill-mode: both !important; }

// 动画播放状态
.animate-paused { animation-play-state: paused !important; }
.animate-running { animation-play-state: running !important; }

// 动画迭代次数
.animate-once { animation-iteration-count: 1 !important; }
.animate-twice { animation-iteration-count: 2 !important; }
.animate-infinite { animation-iteration-count: infinite !important; }

// 动画方向
.animate-normal { animation-direction: normal !important; }
.animate-reverse { animation-direction: reverse !important; }
.animate-alternate { animation-direction: alternate !important; }
.animate-alternate-reverse { animation-direction: alternate-reverse !important; }

// ==================== 悬停动画 ====================

.hover-scale:hover {
  transform: scale(1.05);
  transition: transform $duration-fast $ease-out;
}

.hover-scale-sm:hover {
  transform: scale(1.02);
  transition: transform $duration-fast $ease-out;
}

.hover-scale-lg:hover {
  transform: scale(1.1);
  transition: transform $duration-fast $ease-out;
}

.hover-rotate:hover {
  transform: rotate(5deg);
  transition: transform $duration-fast $ease-out;
}

.hover-bounce:hover {
  animation: bounce 0.6s;
}

.hover-pulse:hover {
  animation: pulse 1s;
}

.hover-shake:hover {
  animation: shake 0.5s;
}

// ==================== 加载动画 ====================

.loading-spinner {
  animation: spin 1s linear infinite;
}

.loading-dots {
  &::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
  }
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

// ==================== 响应式动画控制 ====================

// 减少动画（用户偏好）
@media (prefers-reduced-motion: reduce) {
  .animate-respect-motion {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .animate-fade-in,
  .animate-fade-out,
  .animate-slide-in-up,
  .animate-slide-in-down,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-zoom-in,
  .animate-zoom-out {
    animation: none !important;
  }
}
