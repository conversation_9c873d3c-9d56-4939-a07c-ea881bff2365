/**
 * 主题系统
 * 定义不同主题的颜色变量和样式
 */

@import './variables.scss';

// ==================== 主题变量映射 ====================

:root {
  // 主色调
  --color-primary: #{$color-primary};
  --color-primary-light: #{$color-primary-light};
  --color-primary-dark: #{$color-primary-dark};
  
  // 功能色
  --color-success: #{$color-success};
  --color-success-light: #{$color-success-light};
  --color-success-dark: #{$color-success-dark};
  
  --color-warning: #{$color-warning};
  --color-warning-light: #{$color-warning-light};
  --color-warning-dark: #{$color-warning-dark};
  
  --color-danger: #{$color-danger};
  --color-danger-light: #{$color-danger-light};
  --color-danger-dark: #{$color-danger-dark};
  
  --color-info: #{$color-info};
  --color-info-light: #{$color-info-light};
  --color-info-dark: #{$color-info-dark};
  
  // 中性色
  --color-white: #{$color-white};
  --color-black: #{$color-black};
  
  // 文本颜色
  --color-text-primary: #{$color-text-primary};
  --color-text-secondary: #{$color-text-secondary};
  --color-text-tertiary: #{$color-text-tertiary};
  --color-text-disabled: #{$color-text-disabled};
  --color-text-placeholder: #{$color-text-placeholder};
  --color-text-inverse: #{$color-text-inverse};
  
  // 背景色
  --color-bg-primary: #{$color-bg-primary};
  --color-bg-secondary: #{$color-bg-secondary};
  --color-bg-tertiary: #{$color-bg-tertiary};
  --color-bg-disabled: #{$color-bg-disabled};
  --color-bg-mask: #{$color-bg-mask};
  
  // 边框色
  --color-border: #{$color-border};
  --color-border-light: #{$color-border-light};
  --color-border-dark: #{$color-border-dark};
  
  // 阴影色
  --color-shadow: #{$color-shadow};
  --color-shadow-light: #{$color-shadow-light};
  --color-shadow-dark: #{$color-shadow-dark};
}

// ==================== 亮色主题 ====================

[data-theme="light"] {
  // 主色调
  --color-primary: #{$color-primary};
  --color-primary-light: #{$color-primary-light};
  --color-primary-dark: #{$color-primary-dark};
  
  // 功能色
  --color-success: #{$color-success};
  --color-success-light: #{$color-success-light};
  --color-success-dark: #{$color-success-dark};
  
  --color-warning: #{$color-warning};
  --color-warning-light: #{$color-warning-light};
  --color-warning-dark: #{$color-warning-dark};
  
  --color-danger: #{$color-danger};
  --color-danger-light: #{$color-danger-light};
  --color-danger-dark: #{$color-danger-dark};
  
  --color-info: #{$color-info};
  --color-info-light: #{$color-info-light};
  --color-info-dark: #{$color-info-dark};
  
  // 文本颜色
  --color-text-primary: #{$color-text-primary};
  --color-text-secondary: #{$color-text-secondary};
  --color-text-tertiary: #{$color-text-tertiary};
  --color-text-disabled: #{$color-text-disabled};
  --color-text-placeholder: #{$color-text-placeholder};
  --color-text-inverse: #{$color-text-inverse};
  
  // 背景色
  --color-bg-primary: #{$color-bg-primary};
  --color-bg-secondary: #{$color-bg-secondary};
  --color-bg-tertiary: #{$color-bg-tertiary};
  --color-bg-disabled: #{$color-bg-disabled};
  --color-bg-mask: #{$color-bg-mask};
  
  // 边框色
  --color-border: #{$color-border};
  --color-border-light: #{$color-border-light};
  --color-border-dark: #{$color-border-dark};
  
  // 阴影色
  --color-shadow: #{$color-shadow};
  --color-shadow-light: #{$color-shadow-light};
  --color-shadow-dark: #{$color-shadow-dark};
}

// ==================== 暗色主题 ====================

[data-theme="dark"] {
  // 主色调（暗色主题下稍微调亮）
  --color-primary: #{lighten($color-primary, 10%)};
  --color-primary-light: #{lighten($color-primary-light, 10%)};
  --color-primary-dark: #{$color-primary};
  
  // 功能色（暗色主题下稍微调亮）
  --color-success: #{lighten($color-success, 10%)};
  --color-success-light: #{lighten($color-success-light, 10%)};
  --color-success-dark: #{$color-success};
  
  --color-warning: #{lighten($color-warning, 10%)};
  --color-warning-light: #{lighten($color-warning-light, 10%)};
  --color-warning-dark: #{$color-warning};
  
  --color-danger: #{lighten($color-danger, 10%)};
  --color-danger-light: #{lighten($color-danger-light, 10%)};
  --color-danger-dark: #{$color-danger};
  
  --color-info: #{lighten($color-info, 10%)};
  --color-info-light: #{lighten($color-info-light, 10%)};
  --color-info-dark: #{$color-info};
  
  // 文本颜色
  --color-text-primary: #{$dark-color-text-primary};
  --color-text-secondary: #{$dark-color-text-secondary};
  --color-text-tertiary: #{$dark-color-text-tertiary};
  --color-text-disabled: #{$color-gray-600};
  --color-text-placeholder: #{$color-gray-500};
  --color-text-inverse: #{$color-gray-900};
  
  // 背景色
  --color-bg-primary: #{$dark-color-bg-primary};
  --color-bg-secondary: #{$dark-color-bg-secondary};
  --color-bg-tertiary: #{$dark-color-bg-tertiary};
  --color-bg-disabled: #{$color-gray-900};
  --color-bg-mask: rgba(0, 0, 0, 0.7);
  
  // 边框色
  --color-border: #{$dark-color-border};
  --color-border-light: #{$dark-color-border-light};
  --color-border-dark: #{$dark-color-border-dark};
  
  // 阴影色
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-light: rgba(0, 0, 0, 0.2);
  --color-shadow-dark: rgba(0, 0, 0, 0.5);
}

// ==================== 自动主题（跟随系统） ====================

@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    // 主色调
    --color-primary: #{lighten($color-primary, 10%)};
    --color-primary-light: #{lighten($color-primary-light, 10%)};
    --color-primary-dark: #{$color-primary};
    
    // 功能色
    --color-success: #{lighten($color-success, 10%)};
    --color-success-light: #{lighten($color-success-light, 10%)};
    --color-success-dark: #{$color-success};
    
    --color-warning: #{lighten($color-warning, 10%)};
    --color-warning-light: #{lighten($color-warning-light, 10%)};
    --color-warning-dark: #{$color-warning};
    
    --color-danger: #{lighten($color-danger, 10%)};
    --color-danger-light: #{lighten($color-danger-light, 10%)};
    --color-danger-dark: #{$color-danger};
    
    --color-info: #{lighten($color-info, 10%)};
    --color-info-light: #{lighten($color-info-light, 10%)};
    --color-info-dark: #{$color-info};
    
    // 文本颜色
    --color-text-primary: #{$dark-color-text-primary};
    --color-text-secondary: #{$dark-color-text-secondary};
    --color-text-tertiary: #{$dark-color-text-tertiary};
    --color-text-disabled: #{$color-gray-600};
    --color-text-placeholder: #{$color-gray-500};
    --color-text-inverse: #{$color-gray-900};
    
    // 背景色
    --color-bg-primary: #{$dark-color-bg-primary};
    --color-bg-secondary: #{$dark-color-bg-secondary};
    --color-bg-tertiary: #{$dark-color-bg-tertiary};
    --color-bg-disabled: #{$color-gray-900};
    --color-bg-mask: rgba(0, 0, 0, 0.7);
    
    // 边框色
    --color-border: #{$dark-color-border};
    --color-border-light: #{$dark-color-border-light};
    --color-border-dark: #{$dark-color-border-dark};
    
    // 阴影色
    --color-shadow: rgba(0, 0, 0, 0.3);
    --color-shadow-light: rgba(0, 0, 0, 0.2);
    --color-shadow-dark: rgba(0, 0, 0, 0.5);
  }
}

// ==================== 主题切换动画 ====================

* {
  transition: 
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease,
    box-shadow 0.3s ease;
}

// ==================== 自定义主题支持 ====================

// 蓝色主题
[data-theme="blue"] {
  --color-primary: #1E40AF;
  --color-primary-light: #3B82F6;
  --color-primary-dark: #1E3A8A;
}

// 绿色主题
[data-theme="green"] {
  --color-primary: #059669;
  --color-primary-light: #10B981;
  --color-primary-dark: #047857;
}

// 紫色主题
[data-theme="purple"] {
  --color-primary: #7C3AED;
  --color-primary-light: #8B5CF6;
  --color-primary-dark: #6D28D9;
}

// 粉色主题
[data-theme="pink"] {
  --color-primary: #DB2777;
  --color-primary-light: #EC4899;
  --color-primary-dark: #BE185D;
}

// 橙色主题
[data-theme="orange"] {
  --color-primary: #EA580C;
  --color-primary-light: #F97316;
  --color-primary-dark: #C2410C;
}

// 红色主题
[data-theme="red"] {
  --color-primary: #DC2626;
  --color-primary-light: #EF4444;
  --color-primary-dark: #B91C1C;
}

// ==================== 高对比度主题 ====================

[data-theme="high-contrast"] {
  --color-primary: #0000FF;
  --color-primary-light: #4169E1;
  --color-primary-dark: #000080;
  
  --color-text-primary: #000000;
  --color-text-secondary: #333333;
  --color-text-tertiary: #666666;
  
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F0F0F0;
  --color-bg-tertiary: #E0E0E0;
  
  --color-border: #000000;
  --color-border-light: #333333;
  --color-border-dark: #000000;
}

// ==================== 护眼主题 ====================

[data-theme="eye-care"] {
  --color-primary: #8B4513;
  --color-primary-light: #A0522D;
  --color-primary-dark: #654321;
  
  --color-text-primary: #2F4F2F;
  --color-text-secondary: #556B2F;
  --color-text-tertiary: #6B8E23;
  
  --color-bg-primary: #F5F5DC;
  --color-bg-secondary: #F0F8E8;
  --color-bg-tertiary: #E6F3E6;
  
  --color-border: #D2B48C;
  --color-border-light: #DDD8C0;
  --color-border-dark: #C8B99C;
}

// ==================== 主题工具类 ====================

.theme-transition {
  transition: 
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease,
    box-shadow 0.3s ease !important;
}

.theme-no-transition {
  transition: none !important;
}

// ==================== 主题适配媒体查询 ====================

// 减少动画（用户偏好）
@media (prefers-reduced-motion: reduce) {
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
}

// 高对比度（用户偏好）
@media (prefers-contrast: high) {
  :root {
    --color-border: var(--color-text-primary);
    --color-border-light: var(--color-text-secondary);
    --color-border-dark: var(--color-text-primary);
  }
}

// 透明度减少（用户偏好）
@media (prefers-reduced-transparency: reduce) {
  :root {
    --color-bg-mask: rgb(0, 0, 0);
  }
  
  .glass-effect {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
}
