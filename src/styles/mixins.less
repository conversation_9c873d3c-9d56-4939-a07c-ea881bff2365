/**
 * 样式混入函数
 * 定义可复用的样式混入
 */

@import './variables.less';

// ==================== 响应式断点 ====================

/**
 * 响应式断点混入
 */
.respond-to(@breakpoint) {
  & when (@breakpoint = xs) {
    @media (min-width: @breakpoint-xs) {
      @content();
    }
  }
  & when (@breakpoint = sm) {
    @media (min-width: @breakpoint-sm) {
      @content();
    }
  }
  & when (@breakpoint = md) {
    @media (min-width: @breakpoint-md) {
      @content();
    }
  }
  & when (@breakpoint = lg) {
    @media (min-width: @breakpoint-lg) {
      @content();
    }
  }
  & when (@breakpoint = xl) {
    @media (min-width: @breakpoint-xl) {
      @content();
    }
  }
  & when (@breakpoint = 2xl) {
    @media (min-width: @breakpoint-2xl) {
      @content();
    }
  }
}

/**
 * 最大宽度断点
 */
.respond-to-max(@breakpoint) {
  & when (@breakpoint = xs) {
    @media (max-width: (@breakpoint-sm - 1px)) {
      @content();
    }
  }
  & when (@breakpoint = sm) {
    @media (max-width: (@breakpoint-md - 1px)) {
      @content();
    }
  }
  & when (@breakpoint = md) {
    @media (max-width: (@breakpoint-lg - 1px)) {
      @content();
    }
  }
  & when (@breakpoint = lg) {
    @media (max-width: (@breakpoint-xl - 1px)) {
      @content();
    }
  }
  & when (@breakpoint = xl) {
    @media (max-width: (@breakpoint-2xl - 1px)) {
      @content();
    }
  }
}

// ==================== 布局混入 ====================

/**
 * Flexbox 居中
 */
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * Flexbox 垂直居中
 */
.flex-center-vertical() {
  display: flex;
  align-items: center;
}

/**
 * Flexbox 水平居中
 */
.flex-center-horizontal() {
  display: flex;
  justify-content: center;
}

/**
 * Flexbox 两端对齐
 */
.flex-between() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/**
 * Flexbox 环绕对齐
 */
.flex-around() {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

/**
 * 绝对定位居中
 */
.absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/**
 * 固定宽高比
 */
.aspect-ratio(@width, @height) {
  position: relative;

  &::before {
    content: '';
    display: block;
    padding-top: percentage(@height / @width);
  }

  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// ==================== 文本混入 ====================

/**
 * 文本省略号
 */
.text-ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/**
 * 多行文本省略号
 */
.text-ellipsis-multiline(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/**
 * 文本不可选择
 */
.text-no-select() {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/**
 * 文本换行
 */
.text-break() {
  word-wrap: break-word;
  word-break: break-all;
  hyphens: auto;
}

// ==================== 视觉效果混入 ====================

/**
 * 阴影
 */
.shadow(@level: base) {
  & when (@level = sm) {
    box-shadow: @box-shadow-sm;
  }
  & when (@level = base) {
    box-shadow: @box-shadow-base;
  }
  & when (@level = md) {
    box-shadow: @box-shadow-md;
  }
  & when (@level = lg) {
    box-shadow: @box-shadow-lg;
  }
  & when (@level = xl) {
    box-shadow: @box-shadow-xl;
  }
  & when (@level = 2xl) {
    box-shadow: @box-shadow-2xl;
  }
  & when (@level = none) {
    box-shadow: none;
  }
}

/**
 * 毛玻璃效果
 */
.glass-effect(@opacity: 0.8) {
  background: rgba(255, 255, 255, @opacity);
  backdrop-filter: @backdrop-blur @backdrop-saturate;
  -webkit-backdrop-filter: @backdrop-blur @backdrop-saturate;
}

/**
 * 渐变背景
 */
.gradient-bg(@direction: 135deg, @start-color: @color-primary, @end-color: @color-primary-light) {
  background: linear-gradient(@direction, @start-color 0%, @end-color 100%);
}

/**
 * 边框渐变
 */
.gradient-border(@width: 1px, @direction: 135deg, @start-color: @color-primary, @end-color: @color-primary-light) {
  position: relative;
  background: linear-gradient(@direction, @start-color, @end-color);
  border-radius: inherit;

  &::before {
    content: '';
    position: absolute;
    top: @width;
    left: @width;
    right: @width;
    bottom: @width;
    background: inherit;
    border-radius: inherit;
  }
}

// ==================== 动画混入 ====================

/**
 * 过渡动画
 */
.transition(@property: all, @duration: @duration-base, @timing: @ease-in-out) {
  transition: @property @duration @timing;
}

/**
 * 悬停效果
 */
.hover-effect(@scale: 1.05, @duration: @duration-fast) {
  transition: transform @duration @ease-out;

  &:hover {
    transform: scale(@scale);
  }
}

/**
 * 点击效果
 */
.active-effect(@scale: 0.95, @duration: @duration-fast) {
  transition: transform @duration @ease-out;

  &:active {
    transform: scale(@scale);
  }
}

/**
 * 淡入动画
 */
.fade-in(@duration: @duration-base) {
  animation: fade-in @duration @ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/**
 * 滑入动画
 */
.slide-in(@direction: up, @distance: 20px, @duration: @duration-base) {
  & when (@direction = up) {
    animation: slide-in-up @duration @ease-out;
  }
  & when (@direction = down) {
    animation: slide-in-down @duration @ease-out;
  }
  & when (@direction = left) {
    animation: slide-in-left @duration @ease-out;
  }
  & when (@direction = right) {
    animation: slide-in-right @duration @ease-out;
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// ==================== 工具混入 ====================

/**
 * 清除浮动
 */
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

/**
 * 隐藏元素（保持布局）
 */
.visually-hidden() {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/**
 * 重置按钮样式
 */
.button-reset() {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: none;
}

/**
 * 重置列表样式
 */
.list-reset() {
  list-style: none;
  padding: 0;
  margin: 0;
}

/**
 * 滚动条样式
 */
.scrollbar(@width: 6px, @track-color: transparent, @thumb-color: rgba(0, 0, 0, 0.2)) {
  &::-webkit-scrollbar {
    width: @width;
    height: @width;
  }

  &::-webkit-scrollbar-track {
    background: @track-color;
  }

  &::-webkit-scrollbar-thumb {
    background: @thumb-color;
    border-radius: @width / 2;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// ==================== 安全区域混入 ====================

/**
 * 安全区域内边距
 */
.safe-area-padding(@position: all) {
  & when (@position = all) {
    padding-top: @safe-area-inset-top;
    padding-right: @safe-area-inset-right;
    padding-bottom: @safe-area-inset-bottom;
    padding-left: @safe-area-inset-left;
  }
  & when (@position = top) {
    padding-top: @safe-area-inset-top;
  }
  & when (@position = right) {
    padding-right: @safe-area-inset-right;
  }
  & when (@position = bottom) {
    padding-bottom: @safe-area-inset-bottom;
  }
  & when (@position = left) {
    padding-left: @safe-area-inset-left;
  }
}

/**
 * 安全区域外边距
 */
.safe-area-margin(@position: all) {
  & when (@position = all) {
    margin-top: @safe-area-inset-top;
    margin-right: @safe-area-inset-right;
    margin-bottom: @safe-area-inset-bottom;
    margin-left: @safe-area-inset-left;
  }
  & when (@position = top) {
    margin-top: @safe-area-inset-top;
  }
  & when (@position = right) {
    margin-right: @safe-area-inset-right;
  }
  & when (@position = bottom) {
    margin-bottom: @safe-area-inset-bottom;
  }
  & when (@position = left) {
    margin-left: @safe-area-inset-left;
  }
}
