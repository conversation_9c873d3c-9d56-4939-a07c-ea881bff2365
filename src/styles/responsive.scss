/**
 * 响应式样式
 * 定义不同屏幕尺寸下的样式规则
 */

@import './variables.scss';
@import './mixins.scss';

// ==================== 响应式断点工具类 ====================

// 小屏幕及以上 (≥576px)
@include respond-to(sm) {
  .sm\:block { display: block !important; }
  .sm\:inline-block { display: inline-block !important; }
  .sm\:inline { display: inline !important; }
  .sm\:flex { display: flex !important; }
  .sm\:inline-flex { display: inline-flex !important; }
  .sm\:grid { display: grid !important; }
  .sm\:hidden { display: none !important; }
  
  .sm\:w-auto { width: auto !important; }
  .sm\:w-full { width: 100% !important; }
  .sm\:w-1\/2 { width: 50% !important; }
  .sm\:w-1\/3 { width: 33.333333% !important; }
  .sm\:w-2\/3 { width: 66.666667% !important; }
  .sm\:w-1\/4 { width: 25% !important; }
  .sm\:w-3\/4 { width: 75% !important; }
  
  .sm\:text-left { text-align: left !important; }
  .sm\:text-center { text-align: center !important; }
  .sm\:text-right { text-align: right !important; }
  
  .sm\:text-xs { font-size: $font-size-xs !important; }
  .sm\:text-sm { font-size: $font-size-sm !important; }
  .sm\:text-base { font-size: $font-size-base !important; }
  .sm\:text-lg { font-size: $font-size-lg !important; }
  .sm\:text-xl { font-size: $font-size-xl !important; }
  .sm\:text-2xl { font-size: $font-size-2xl !important; }
  
  .sm\:flex-row { flex-direction: row !important; }
  .sm\:flex-col { flex-direction: column !important; }
  .sm\:justify-start { justify-content: flex-start !important; }
  .sm\:justify-center { justify-content: center !important; }
  .sm\:justify-end { justify-content: flex-end !important; }
  .sm\:justify-between { justify-content: space-between !important; }
  
  @for $i from 0 through 24 {
    .sm\:m-#{$i} { margin: #{$i * $spacing-unit} !important; }
    .sm\:p-#{$i} { padding: #{$i * $spacing-unit} !important; }
    .sm\:mt-#{$i} { margin-top: #{$i * $spacing-unit} !important; }
    .sm\:mr-#{$i} { margin-right: #{$i * $spacing-unit} !important; }
    .sm\:mb-#{$i} { margin-bottom: #{$i * $spacing-unit} !important; }
    .sm\:ml-#{$i} { margin-left: #{$i * $spacing-unit} !important; }
    .sm\:pt-#{$i} { padding-top: #{$i * $spacing-unit} !important; }
    .sm\:pr-#{$i} { padding-right: #{$i * $spacing-unit} !important; }
    .sm\:pb-#{$i} { padding-bottom: #{$i * $spacing-unit} !important; }
    .sm\:pl-#{$i} { padding-left: #{$i * $spacing-unit} !important; }
  }
}

// 中等屏幕及以上 (≥768px)
@include respond-to(md) {
  .md\:block { display: block !important; }
  .md\:inline-block { display: inline-block !important; }
  .md\:inline { display: inline !important; }
  .md\:flex { display: flex !important; }
  .md\:inline-flex { display: inline-flex !important; }
  .md\:grid { display: grid !important; }
  .md\:hidden { display: none !important; }
  
  .md\:w-auto { width: auto !important; }
  .md\:w-full { width: 100% !important; }
  .md\:w-1\/2 { width: 50% !important; }
  .md\:w-1\/3 { width: 33.333333% !important; }
  .md\:w-2\/3 { width: 66.666667% !important; }
  .md\:w-1\/4 { width: 25% !important; }
  .md\:w-3\/4 { width: 75% !important; }
  
  .md\:text-left { text-align: left !important; }
  .md\:text-center { text-align: center !important; }
  .md\:text-right { text-align: right !important; }
  
  .md\:text-xs { font-size: $font-size-xs !important; }
  .md\:text-sm { font-size: $font-size-sm !important; }
  .md\:text-base { font-size: $font-size-base !important; }
  .md\:text-lg { font-size: $font-size-lg !important; }
  .md\:text-xl { font-size: $font-size-xl !important; }
  .md\:text-2xl { font-size: $font-size-2xl !important; }
  
  .md\:flex-row { flex-direction: row !important; }
  .md\:flex-col { flex-direction: column !important; }
  .md\:justify-start { justify-content: flex-start !important; }
  .md\:justify-center { justify-content: center !important; }
  .md\:justify-end { justify-content: flex-end !important; }
  .md\:justify-between { justify-content: space-between !important; }
  
  @for $i from 0 through 24 {
    .md\:m-#{$i} { margin: #{$i * $spacing-unit} !important; }
    .md\:p-#{$i} { padding: #{$i * $spacing-unit} !important; }
    .md\:mt-#{$i} { margin-top: #{$i * $spacing-unit} !important; }
    .md\:mr-#{$i} { margin-right: #{$i * $spacing-unit} !important; }
    .md\:mb-#{$i} { margin-bottom: #{$i * $spacing-unit} !important; }
    .md\:ml-#{$i} { margin-left: #{$i * $spacing-unit} !important; }
    .md\:pt-#{$i} { padding-top: #{$i * $spacing-unit} !important; }
    .md\:pr-#{$i} { padding-right: #{$i * $spacing-unit} !important; }
    .md\:pb-#{$i} { padding-bottom: #{$i * $spacing-unit} !important; }
    .md\:pl-#{$i} { padding-left: #{$i * $spacing-unit} !important; }
  }
}

// 大屏幕及以上 (≥992px)
@include respond-to(lg) {
  .lg\:block { display: block !important; }
  .lg\:inline-block { display: inline-block !important; }
  .lg\:inline { display: inline !important; }
  .lg\:flex { display: flex !important; }
  .lg\:inline-flex { display: inline-flex !important; }
  .lg\:grid { display: grid !important; }
  .lg\:hidden { display: none !important; }
  
  .lg\:w-auto { width: auto !important; }
  .lg\:w-full { width: 100% !important; }
  .lg\:w-1\/2 { width: 50% !important; }
  .lg\:w-1\/3 { width: 33.333333% !important; }
  .lg\:w-2\/3 { width: 66.666667% !important; }
  .lg\:w-1\/4 { width: 25% !important; }
  .lg\:w-3\/4 { width: 75% !important; }
  
  .lg\:text-left { text-align: left !important; }
  .lg\:text-center { text-align: center !important; }
  .lg\:text-right { text-align: right !important; }
  
  .lg\:text-xs { font-size: $font-size-xs !important; }
  .lg\:text-sm { font-size: $font-size-sm !important; }
  .lg\:text-base { font-size: $font-size-base !important; }
  .lg\:text-lg { font-size: $font-size-lg !important; }
  .lg\:text-xl { font-size: $font-size-xl !important; }
  .lg\:text-2xl { font-size: $font-size-2xl !important; }
  
  .lg\:flex-row { flex-direction: row !important; }
  .lg\:flex-col { flex-direction: column !important; }
  .lg\:justify-start { justify-content: flex-start !important; }
  .lg\:justify-center { justify-content: center !important; }
  .lg\:justify-end { justify-content: flex-end !important; }
  .lg\:justify-between { justify-content: space-between !important; }
  
  @for $i from 0 through 24 {
    .lg\:m-#{$i} { margin: #{$i * $spacing-unit} !important; }
    .lg\:p-#{$i} { padding: #{$i * $spacing-unit} !important; }
    .lg\:mt-#{$i} { margin-top: #{$i * $spacing-unit} !important; }
    .lg\:mr-#{$i} { margin-right: #{$i * $spacing-unit} !important; }
    .lg\:mb-#{$i} { margin-bottom: #{$i * $spacing-unit} !important; }
    .lg\:ml-#{$i} { margin-left: #{$i * $spacing-unit} !important; }
    .lg\:pt-#{$i} { padding-top: #{$i * $spacing-unit} !important; }
    .lg\:pr-#{$i} { padding-right: #{$i * $spacing-unit} !important; }
    .lg\:pb-#{$i} { padding-bottom: #{$i * $spacing-unit} !important; }
    .lg\:pl-#{$i} { padding-left: #{$i * $spacing-unit} !important; }
  }
}

// 超大屏幕及以上 (≥1200px)
@include respond-to(xl) {
  .xl\:block { display: block !important; }
  .xl\:inline-block { display: inline-block !important; }
  .xl\:inline { display: inline !important; }
  .xl\:flex { display: flex !important; }
  .xl\:inline-flex { display: inline-flex !important; }
  .xl\:grid { display: grid !important; }
  .xl\:hidden { display: none !important; }
  
  .xl\:w-auto { width: auto !important; }
  .xl\:w-full { width: 100% !important; }
  .xl\:w-1\/2 { width: 50% !important; }
  .xl\:w-1\/3 { width: 33.333333% !important; }
  .xl\:w-2\/3 { width: 66.666667% !important; }
  .xl\:w-1\/4 { width: 25% !important; }
  .xl\:w-3\/4 { width: 75% !important; }
  
  .xl\:text-left { text-align: left !important; }
  .xl\:text-center { text-align: center !important; }
  .xl\:text-right { text-align: right !important; }
  
  .xl\:text-xs { font-size: $font-size-xs !important; }
  .xl\:text-sm { font-size: $font-size-sm !important; }
  .xl\:text-base { font-size: $font-size-base !important; }
  .xl\:text-lg { font-size: $font-size-lg !important; }
  .xl\:text-xl { font-size: $font-size-xl !important; }
  .xl\:text-2xl { font-size: $font-size-2xl !important; }
  
  .xl\:flex-row { flex-direction: row !important; }
  .xl\:flex-col { flex-direction: column !important; }
  .xl\:justify-start { justify-content: flex-start !important; }
  .xl\:justify-center { justify-content: center !important; }
  .xl\:justify-end { justify-content: flex-end !important; }
  .xl\:justify-between { justify-content: space-between !important; }
  
  @for $i from 0 through 24 {
    .xl\:m-#{$i} { margin: #{$i * $spacing-unit} !important; }
    .xl\:p-#{$i} { padding: #{$i * $spacing-unit} !important; }
    .xl\:mt-#{$i} { margin-top: #{$i * $spacing-unit} !important; }
    .xl\:mr-#{$i} { margin-right: #{$i * $spacing-unit} !important; }
    .xl\:mb-#{$i} { margin-bottom: #{$i * $spacing-unit} !important; }
    .xl\:ml-#{$i} { margin-left: #{$i * $spacing-unit} !important; }
    .xl\:pt-#{$i} { padding-top: #{$i * $spacing-unit} !important; }
    .xl\:pr-#{$i} { padding-right: #{$i * $spacing-unit} !important; }
    .xl\:pb-#{$i} { padding-bottom: #{$i * $spacing-unit} !important; }
    .xl\:pl-#{$i} { padding-left: #{$i * $spacing-unit} !important; }
  }
}

// ==================== 容器样式 ====================

.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: $spacing-4;
  padding-right: $spacing-4;
  
  @include respond-to(sm) {
    max-width: 540px;
    padding-left: $spacing-6;
    padding-right: $spacing-6;
  }
  
  @include respond-to(md) {
    max-width: 720px;
  }
  
  @include respond-to(lg) {
    max-width: 960px;
  }
  
  @include respond-to(xl) {
    max-width: 1140px;
  }
  
  @include respond-to(2xl) {
    max-width: 1320px;
  }
}

// ==================== 网格系统 ====================

.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -#{$spacing-3};
  margin-right: -#{$spacing-3};
}

.col {
  flex: 1 0 0%;
  padding-left: $spacing-3;
  padding-right: $spacing-3;
}

// 自动列
.col-auto {
  flex: 0 0 auto;
  width: auto;
  padding-left: $spacing-3;
  padding-right: $spacing-3;
}

// 固定列宽
@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
    padding-left: $spacing-3;
    padding-right: $spacing-3;
  }
}

// 响应式列
@include respond-to(sm) {
  @for $i from 1 through 12 {
    .col-sm-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

@include respond-to(md) {
  @for $i from 1 through 12 {
    .col-md-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

@include respond-to(lg) {
  @for $i from 1 through 12 {
    .col-lg-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

@include respond-to(xl) {
  @for $i from 1 through 12 {
    .col-xl-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

// ==================== 移动端优化 ====================

// 移动端隐藏
@include respond-to-max(sm) {
  .hidden-mobile {
    display: none !important;
  }
}

// 桌面端隐藏
@include respond-to(md) {
  .hidden-desktop {
    display: none !important;
  }
}

// 移动端显示
@include respond-to-max(sm) {
  .visible-mobile {
    display: block !important;
  }
}

// 桌面端显示
@include respond-to(md) {
  .visible-desktop {
    display: block !important;
  }
}

// ==================== 触摸优化 ====================

// 触摸目标最小尺寸
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

// 触摸友好的间距
.touch-spacing {
  margin: $spacing-2;
  
  @include respond-to(md) {
    margin: $spacing-1;
  }
}

// ==================== 安全区域适配 ====================

.safe-area-top {
  @include safe-area-padding('top');
}

.safe-area-bottom {
  @include safe-area-padding('bottom');
}

.safe-area-left {
  @include safe-area-padding('left');
}

.safe-area-right {
  @include safe-area-padding('right');
}

.safe-area-all {
  @include safe-area-padding('all');
}
