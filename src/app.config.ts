export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/login/index',
    'pages/profile/index',
    'pages/settings/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'NoeMo App',
    navigationBarTextStyle: 'black',
    enablePullDownRefresh: false,
    onReachBottomDistance: 50
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#007aff',
    backgroundColor: '#ffffff',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/icons/home.png',
        selectedIconPath: 'assets/icons/home-active.png'
      },
      {
        pagePath: 'pages/profile/index',
        text: '我的',
        iconPath: 'assets/icons/profile.png',
        selectedIconPath: 'assets/icons/profile-active.png'
      }
    ]
  },
  // 多端适配配置
  networkTimeout: {
    request: 10000,
    downloadFile: 10000
  },
  debug: process.env.NODE_ENV === 'development'
})
