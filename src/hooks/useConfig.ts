/**
 * 配置管理 Hook
 * 提供配置相关的 React Hook
 */

import { useMemo } from 'react';
import {
  getConfig,
  getCurrentEnvironment,
  getCurrentPlatform,
  isFeatureEnabled,
  getServiceConfig,
  getMiniProgramConfig,
  getEnvironmentInfo,
  isDevelopment,
  isTest,
  isProduction,
} from '../config/env';

/**
 * 使用配置 Hook
 * @returns 配置对象和相关方法
 */
export function useConfig() {
  const config = useMemo(() => getConfig(), []);
  
  const environment = useMemo(() => getCurrentEnvironment(), []);
  const platform = useMemo(() => getCurrentPlatform(), []);
  const environmentInfo = useMemo(() => getEnvironmentInfo(), []);
  
  return {
    config,
    environment,
    platform,
    environmentInfo,
    isDevelopment: isDevelopment(),
    isTest: isTest(),
    isProduction: isProduction(),
    isFeatureEnabled,
    getServiceConfig,
    getMiniProgramConfig,
  };
}

/**
 * 使用 API 配置 Hook
 * @returns API 相关配置
 */
export function useApiConfig() {
  const config = getConfig();
  
  return useMemo(() => ({
    baseUrl: config.apiBaseUrl,
    timeout: config.apiTimeout,
    retryTimes: config.apiRetryTimes,
  }), [config]);
}

/**
 * 使用上传配置 Hook
 * @returns 上传相关配置
 */
export function useUploadConfig() {
  const config = getConfig();
  
  return useMemo(() => ({
    baseUrl: config.uploadBaseUrl,
    maxSize: config.uploadMaxSize,
    allowedTypes: config.uploadAllowedTypes,
  }), [config]);
}

/**
 * 使用存储配置 Hook
 * @returns 存储相关配置
 */
export function useStorageConfig() {
  const config = getConfig();
  
  return useMemo(() => ({
    prefix: config.storagePrefix,
    ttl: config.storageTTL,
  }), [config]);
}

/**
 * 使用日志配置 Hook
 * @returns 日志相关配置
 */
export function useLogConfig() {
  const config = getConfig();
  
  return useMemo(() => ({
    level: config.logLevel,
    enabled: config.logEnabled,
    maxEntries: config.logMaxEntries,
  }), [config]);
}

/**
 * 使用性能监控配置 Hook
 * @returns 性能监控相关配置
 */
export function usePerformanceConfig() {
  const config = getConfig();
  
  return useMemo(() => ({
    enabled: config.performanceEnabled,
    maxEntries: config.performanceMaxEntries,
  }), [config]);
}

/**
 * 使用错误监控配置 Hook
 * @returns 错误监控相关配置
 */
export function useErrorConfig() {
  const config = getConfig();
  
  return useMemo(() => ({
    enabled: config.errorReportEnabled,
    reportUrl: config.errorReportUrl,
  }), [config]);
}

/**
 * 使用功能开关 Hook
 * @returns 功能开关相关方法
 */
export function useFeatures() {
  const config = getConfig();
  
  return useMemo(() => ({
    features: config.features,
    isEnabled: (feature: keyof typeof config.features) => config.features[feature],
    enableDebugPanel: config.features.enableDebugPanel,
    enablePerformanceMonitor: config.features.enablePerformanceMonitor,
    enableErrorBoundary: config.features.enableErrorBoundary,
    enableOfflineMode: config.features.enableOfflineMode,
    enableDarkMode: config.features.enableDarkMode,
    enableBiometric: config.features.enableBiometric,
  }), [config]);
}

/**
 * 使用服务配置 Hook
 * @param serviceName 服务名称
 * @returns 指定服务的配置
 */
export function useServiceConfig<T extends keyof ReturnType<typeof getConfig>['services']>(
  serviceName: T
) {
  const config = getConfig();
  
  return useMemo(() => config.services[serviceName], [config, serviceName]);
}

/**
 * 使用地图服务配置 Hook
 * @returns 地图服务配置
 */
export function useMapConfig() {
  return useServiceConfig('map');
}

/**
 * 使用统计服务配置 Hook
 * @returns 统计服务配置
 */
export function useAnalyticsConfig() {
  return useServiceConfig('analytics');
}

/**
 * 使用推送服务配置 Hook
 * @returns 推送服务配置
 */
export function usePushConfig() {
  return useServiceConfig('push');
}

/**
 * 使用支付服务配置 Hook
 * @returns 支付服务配置
 */
export function usePaymentConfig() {
  return useServiceConfig('payment');
}

/**
 * 使用小程序配置 Hook
 * @returns 当前平台的小程序配置
 */
export function useMiniProgramConfig() {
  return useMemo(() => getMiniProgramConfig(), []);
}

/**
 * 使用平台检测 Hook
 * @returns 平台检测相关方法
 */
export function usePlatform() {
  const platform = getCurrentPlatform();
  
  return useMemo(() => ({
    platform,
    isWeapp: platform === 'weapp',
    isAlipay: platform === 'alipay',
    isTT: platform === 'tt',
    isSwan: platform === 'swan',
    isQQ: platform === 'qq',
    isJD: platform === 'jd',
    isXHS: platform === 'xhs',
    isH5: platform === 'h5',
    isRN: platform === 'rn',
    isHarmony: platform === 'harmony',
    isMiniProgram: ['weapp', 'alipay', 'tt', 'swan', 'qq', 'jd', 'xhs'].includes(platform),
  }), [platform]);
}

/**
 * 使用环境检测 Hook
 * @returns 环境检测相关方法
 */
export function useEnvironment() {
  const environment = getCurrentEnvironment();
  
  return useMemo(() => ({
    environment,
    isDevelopment: environment === 'development',
    isTest: environment === 'test',
    isProduction: environment === 'production',
  }), [environment]);
}

/**
 * 使用调试模式 Hook
 * @returns 调试模式相关信息
 */
export function useDebugMode() {
  const { isDevelopment } = useEnvironment();
  const { enableDebugPanel } = useFeatures();
  
  return useMemo(() => ({
    isDebugMode: isDevelopment,
    isDebugPanelEnabled: enableDebugPanel,
    showDebugInfo: isDevelopment && enableDebugPanel,
  }), [isDevelopment, enableDebugPanel]);
}

/**
 * 使用应用信息 Hook
 * @returns 应用基本信息
 */
export function useAppInfo() {
  const config = getConfig();
  
  return useMemo(() => ({
    name: config.appName,
    version: config.appVersion,
    description: config.appDescription,
  }), [config]);
}
