/**
 * 网络请求封装（兼容层）
 * 为了保持向后兼容，这里重新导出高级 HTTP 客户端
 */

// 重新导出高级 HTTP 客户端
export {
  http as default,
  http,
  HttpClient,
  type HttpConfig as RequestConfig,
  type HttpResponse as ResponseData,
  type HttpInterceptor as RequestInterceptor,
  type UploadConfig,
  type DownloadConfig
} from './http';

// 重新导出 API 服务
export {
  userApi,
  fileApi,
  systemApi,
  apiUtils
} from '../services/api';

/**
 * 创建请求实例的便捷函数
 */
import { http } from './http';

/**
 * 默认请求实例
 * @deprecated 建议直接使用 http 或创建新的 HttpClient 实例
 */
export const request = http;

/**
 * 便捷的请求方法
 * @deprecated 建议直接使用 http.get, http.post 等方法
 */
export const get = http.get.bind(http);
export const post = http.post.bind(http);
export const put = http.put.bind(http);
export const del = http.delete.bind(http);
export const patch = http.patch.bind(http);
export const upload = http.upload.bind(http);
export const download = http.download.bind(http);
