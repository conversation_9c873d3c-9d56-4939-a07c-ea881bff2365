/**
 * 调试工具函数
 * 支持多端兼容的调试和日志功能
 */

import { storage } from './storage';

/**
 * 日志级别
 */
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * 日志条目接口
 */
interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: number;
  data?: any;
  stack?: string;
}

/**
 * 日志配置接口
 */
interface LogConfig {
  level: LogLevel;
  enabled: boolean;
  console: boolean;
  storage: boolean;
  maxEntries: number;
  prefix: string;
}

/**
 * 默认日志配置
 */
const defaultConfig: LogConfig = {
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'warn',
  enabled: true,
  console: true,
  storage: true,
  maxEntries: 1000,
  prefix: '[NoeMo]',
};

/**
 * 当前日志配置
 */
let currentConfig: LogConfig = { ...defaultConfig };

/**
 * 日志级别权重
 */
const levelWeights: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

/**
 * 日志级别颜色（仅 H5 环境）
 */
const levelColors: Record<LogLevel, string> = {
  debug: '#6B7280',
  info: '#3B82F6',
  warn: '#F59E0B',
  error: '#EF4444',
};

/**
 * 内存中的日志存储
 */
const logEntries: LogEntry[] = [];

/**
 * 配置日志系统
 * @param config 日志配置
 */
export function configureLogger(config: Partial<LogConfig>): void {
  currentConfig = { ...currentConfig, ...config };
}

/**
 * 检查日志级别是否应该输出
 * @param level 日志级别
 * @returns 是否应该输出
 */
function shouldLog(level: LogLevel): boolean {
  if (!currentConfig.enabled) return false;
  return levelWeights[level] >= levelWeights[currentConfig.level];
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 * @returns 格式化后的时间字符串
 */
function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp);
  return date.toISOString().replace('T', ' ').replace('Z', '');
}

/**
 * 创建日志条目
 * @param level 日志级别
 * @param message 日志消息
 * @param data 附加数据
 * @returns 日志条目
 */
function createLogEntry(level: LogLevel, message: string, data?: any): LogEntry {
  const entry: LogEntry = {
    level,
    message,
    timestamp: Date.now(),
    data,
  };

  // 在错误级别时添加堆栈信息
  if (level === 'error') {
    entry.stack = new Error().stack;
  }

  return entry;
}

/**
 * 输出日志到控制台
 * @param entry 日志条目
 */
function logToConsole(entry: LogEntry): void {
  if (!currentConfig.console) return;

  const timestamp = formatTimestamp(entry.timestamp);
  const prefix = currentConfig.prefix;
  const message = `${prefix} [${entry.level.toUpperCase()}] ${timestamp} ${entry.message}`;

  // 在 H5 环境中使用彩色输出
  if (typeof window !== 'undefined' && window.console) {
    const color = levelColors[entry.level];
    console.log(`%c${message}`, `color: ${color}`, entry.data || '');

    if (entry.stack && entry.level === 'error') {
      console.log(`%cStack: ${entry.stack}`, 'color: #6B7280');
    }
  } else {
    // 在小程序环境中使用普通输出
    const consoleMethod = console[entry.level] || console.log;
    consoleMethod(message, entry.data || '');

    if (entry.stack && entry.level === 'error') {
      console.log('Stack:', entry.stack);
    }
  }
}

/**
 * 存储日志到本地
 * @param entry 日志条目
 */
function logToStorage(entry: LogEntry): void {
  if (!currentConfig.storage) return;

  try {
    // 添加到内存存储
    logEntries.push(entry);

    // 保持日志数量在限制内
    if (logEntries.length > currentConfig.maxEntries) {
      logEntries.shift();
    }

    // 存储到本地存储
    const storedLogs = storage.getItem<LogEntry[]>('app_logs', []);
    storedLogs.push(entry);

    // 只保留最近的日志
    if (storedLogs.length > currentConfig.maxEntries) {
      storedLogs.splice(0, storedLogs.length - currentConfig.maxEntries);
    }

    storage.setItem('app_logs', storedLogs);
  } catch (error) {
    console.error('Failed to store log entry:', error);
  }
}

/**
 * 通用日志函数
 * @param level 日志级别
 * @param message 日志消息
 * @param data 附加数据
 */
function log(level: LogLevel, message: string, data?: any): void {
  if (!shouldLog(level)) return;

  const entry = createLogEntry(level, message, data);

  logToConsole(entry);
  logToStorage(entry);
}

/**
 * 调试日志
 * @param message 日志消息
 * @param data 附加数据
 */
export function debug(message: string, data?: any): void {
  log('debug', message, data);
}

/**
 * 信息日志
 * @param message 日志消息
 * @param data 附加数据
 */
export function info(message: string, data?: any): void {
  log('info', message, data);
}

/**
 * 警告日志
 * @param message 日志消息
 * @param data 附加数据
 */
export function warn(message: string, data?: any): void {
  log('warn', message, data);
}

/**
 * 错误日志
 * @param message 日志消息
 * @param data 附加数据
 */
export function error(message: string, data?: any): void {
  log('error', message, data);
}

/**
 * 获取内存中的日志
 * @param level 过滤的日志级别
 * @returns 日志条目数组
 */
export function getLogs(level?: LogLevel): LogEntry[] {
  if (level) {
    return logEntries.filter(entry => entry.level === level);
  }
  return [...logEntries];
}

/**
 * 获取存储的日志
 * @returns 日志条目数组
 */
export function getStoredLogs(): LogEntry[] {
  try {
    return storage.getItem<LogEntry[]>('app_logs', []);
  } catch (error) {
    console.error('Failed to get stored logs:', error);
    return [];
  }
}

/**
 * 清除日志
 */
export function clearLogs(): void {
  logEntries.length = 0;

  try {
    storage.removeItem('app_logs');
  } catch (error) {
    console.error('Failed to clear stored logs:', error);
  }
}

/**
 * 导出日志为文本
 * @param level 过滤的日志级别
 * @returns 日志文本
 */
export function exportLogs(level?: LogLevel): string {
  const logs = level ? getLogs(level) : getLogs();

  return logs
    .map(entry => {
      const timestamp = formatTimestamp(entry.timestamp);
      const dataStr = entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : '';
      const stackStr = entry.stack ? ` | Stack: ${entry.stack}` : '';
      return `[${entry.level.toUpperCase()}] ${timestamp} ${entry.message}${dataStr}${stackStr}`;
    })
    .join('\n');
}

/**
 * 性能计时器
 */
class Timer {
  private startTime: number;
  private name: string;

  constructor(name: string) {
    this.name = name;
    this.startTime = Date.now();
    debug(`Timer started: ${name}`);
  }

  end(): number {
    const duration = Date.now() - this.startTime;
    debug(`Timer ended: ${this.name}`, { duration: `${duration}ms` });
    return duration;
  }
}

/**
 * 创建性能计时器
 * @param name 计时器名称
 * @returns 计时器实例
 */
export function createTimer(name: string): Timer {
  return new Timer(name);
}

/**
 * 计时装饰器
 * @param name 计时名称
 * @returns 装饰器函数
 */
export function timing(name?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const timerName = name || `${target.constructor.name}.${propertyName}`;

    descriptor.value = function (...args: any[]) {
      const timer = createTimer(timerName);

      try {
        const result = method.apply(this, args);

        // 如果是 Promise，等待完成后结束计时
        if (result && typeof result.then === 'function') {
          return result.finally(() => timer.end());
        } else {
          timer.end();
          return result;
        }
      } catch (error) {
        timer.end();
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 调试信息收集器
 */
export class DebugInfo {
  private static instance: DebugInfo;
  private info: Record<string, any> = {};

  static getInstance(): DebugInfo {
    if (!DebugInfo.instance) {
      DebugInfo.instance = new DebugInfo();
    }
    return DebugInfo.instance;
  }

  set(key: string, value: any): void {
    this.info[key] = value;
    debug(`Debug info set: ${key}`, value);
  }

  get(key: string): any {
    return this.info[key];
  }

  getAll(): Record<string, any> {
    return { ...this.info };
  }

  clear(): void {
    this.info = {};
    debug('Debug info cleared');
  }

  export(): string {
    return JSON.stringify(this.info, null, 2);
  }
}

/**
 * 获取调试信息实例
 */
export const debugInfo = DebugInfo.getInstance();

/**
 * 断言函数
 * @param condition 条件
 * @param message 错误消息
 */
export function assert(condition: any, message: string = 'Assertion failed'): asserts condition {
  if (!condition) {
    error(`Assertion failed: ${message}`);
    throw new Error(message);
  }
}

/**
 * 调试模式检查
 * @returns 是否为调试模式
 */
export function isDebugMode(): boolean {
  return process.env.NODE_ENV === 'development' || currentConfig.level === 'debug';
}

/**
 * 仅在调试模式下执行
 * @param fn 要执行的函数
 */
export function debugOnly(fn: () => void): void {
  if (isDebugMode()) {
    fn();
  }
}

/**
 * 创建调试组
 */
export function createDebugGroup(name: string) {
  return {
    debug: (message: string, data?: any) => debug(`[${name}] ${message}`, data),
    info: (message: string, data?: any) => info(`[${name}] ${message}`, data),
    warn: (message: string, data?: any) => warn(`[${name}] ${message}`, data),
    error: (message: string, data?: any) => error(`[${name}] ${message}`, data),
  };
}

/**
 * 日志过滤器接口
 */
interface LogFilter {
  level?: LogLevel[];
  message?: RegExp;
  timeRange?: {
    start: number;
    end: number;
  };
  tags?: string[];
}

/**
 * 日志统计信息
 */
interface LogStats {
  total: number;
  byLevel: Record<LogLevel, number>;
  timeRange: {
    start: number;
    end: number;
  };
  topMessages: Array<{
    message: string;
    count: number;
  }>;
}

/**
 * 高级日志管理器
 */
export class AdvancedLogger {
  private logs: LogEntry[] = [];
  private config: LogConfig;
  private filters: LogFilter[] = [];
  private tags: Set<string> = new Set();
  private subscribers: Array<(entry: LogEntry) => void> = [];

  constructor(config: Partial<LogConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * 添加日志条目
   */
  addLog(level: LogLevel, message: string, data?: any, tags?: string[]) {
    const entry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      data,
      stack: level === 'error' ? new Error().stack : undefined,
    };

    // 添加标签
    if (tags) {
      tags.forEach(tag => this.tags.add(tag));
      (entry as any).tags = tags;
    }

    this.logs.push(entry);

    // 限制日志数量
    if (this.logs.length > this.config.maxEntries) {
      this.logs = this.logs.slice(-this.config.maxEntries);
    }

    // 通知订阅者
    this.subscribers.forEach(subscriber => {
      try {
        subscriber(entry);
      } catch (e) {
        console.error('Log subscriber error:', e);
      }
    });

    // 存储到本地
    if (this.config.storage) {
      this.saveToStorage();
    }
  }

  /**
   * 订阅日志事件
   */
  subscribe(callback: (entry: LogEntry) => void): () => void {
    this.subscribers.push(callback);

    return () => {
      const index = this.subscribers.indexOf(callback);
      if (index > -1) {
        this.subscribers.splice(index, 1);
      }
    };
  }

  /**
   * 过滤日志
   */
  filterLogs(filter: LogFilter): LogEntry[] {
    return this.logs.filter(entry => {
      // 级别过滤
      if (filter.level && !filter.level.includes(entry.level)) {
        return false;
      }

      // 消息过滤
      if (filter.message && !filter.message.test(entry.message)) {
        return false;
      }

      // 时间范围过滤
      if (filter.timeRange) {
        if (entry.timestamp < filter.timeRange.start || entry.timestamp > filter.timeRange.end) {
          return false;
        }
      }

      // 标签过滤
      if (filter.tags && filter.tags.length > 0) {
        const entryTags = (entry as any).tags || [];
        if (!filter.tags.some(tag => entryTags.includes(tag))) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * 获取日志统计
   */
  getStats(): LogStats {
    const stats: LogStats = {
      total: this.logs.length,
      byLevel: {
        debug: 0,
        info: 0,
        warn: 0,
        error: 0,
      },
      timeRange: {
        start: this.logs.length > 0 ? this.logs[0].timestamp : 0,
        end: this.logs.length > 0 ? this.logs[this.logs.length - 1].timestamp : 0,
      },
      topMessages: [],
    };

    // 统计各级别日志数量
    this.logs.forEach(entry => {
      stats.byLevel[entry.level]++;
    });

    // 统计热门消息
    const messageCount = new Map<string, number>();
    this.logs.forEach(entry => {
      const count = messageCount.get(entry.message) || 0;
      messageCount.set(entry.message, count + 1);
    });

    stats.topMessages = Array.from(messageCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    return stats;
  }

  /**
   * 导出日志
   */
  exportLogs(format: 'json' | 'csv' | 'txt' = 'json'): string {
    switch (format) {
      case 'json':
        return JSON.stringify(this.logs, null, 2);

      case 'csv':
        const headers = 'timestamp,level,message,data\n';
        const rows = this.logs.map(entry =>
          `${entry.timestamp},${entry.level},"${entry.message}","${JSON.stringify(entry.data || '')}"`
        ).join('\n');
        return headers + rows;

      case 'txt':
        return this.logs.map(entry =>
          `[${new Date(entry.timestamp).toISOString()}] ${entry.level.toUpperCase()}: ${entry.message}${entry.data ? ' ' + JSON.stringify(entry.data) : ''}`
        ).join('\n');

      default:
        return JSON.stringify(this.logs, null, 2);
    }
  }

  /**
   * 清除日志
   */
  clear() {
    this.logs = [];
    this.saveToStorage();
  }

  /**
   * 保存到本地存储
   */
  private async saveToStorage() {
    try {
      await storage.set('debug_logs', this.logs.slice(-100)); // 只保存最近100条
    } catch (e) {
      console.error('Failed to save logs to storage:', e);
    }
  }

  /**
   * 从本地存储加载
   */
  async loadFromStorage() {
    try {
      const logs = await storage.get('debug_logs');
      if (Array.isArray(logs)) {
        this.logs = logs;
      }
    } catch (e) {
      console.error('Failed to load logs from storage:', e);
    }
  }

  /**
   * 获取所有标签
   */
  getTags(): string[] {
    return Array.from(this.tags);
  }

  /**
   * 获取所有日志
   */
  getAllLogs(): LogEntry[] {
    return [...this.logs];
  }
}

/**
 * 全局高级日志管理器实例
 */
export const advancedLogger = new AdvancedLogger();

/**
 * 带标签的日志函数
 */
export function taggedLog(level: LogLevel, message: string, data?: any, tags?: string[]) {
  advancedLogger.addLog(level, message, data, tags);

  // 同时使用原有的日志函数
  switch (level) {
    case 'debug':
      debug(message, data);
      break;
    case 'info':
      info(message, data);
      break;
    case 'warn':
      warn(message, data);
      break;
    case 'error':
      error(message, data);
      break;
  }
}

/**
 * 性能日志
 */
export function perfLog(name: string, duration: number, details?: any) {
  taggedLog('info', `Performance: ${name} took ${duration}ms`, details, ['performance']);
}

/**
 * 用户行为日志
 */
export function userActionLog(action: string, details?: any) {
  taggedLog('info', `User action: ${action}`, details, ['user-action']);
}

/**
 * API 请求日志
 */
export function apiLog(method: string, url: string, status: number, duration: number, details?: any) {
  const level: LogLevel = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info';
  taggedLog(level, `API ${method} ${url} - ${status} (${duration}ms)`, details, ['api']);
}

/**
 * 初始化调试系统
 */
export function initDebugSystem(): void {
  info('Debug system initialized', {
    config: currentConfig,
    platform: process.env.TARO_ENV,
    environment: process.env.NODE_ENV,
  });

  // 加载历史日志
  advancedLogger.loadFromStorage();

  // 在开发环境中暴露调试工具到全局
  if (isDebugMode() && typeof window !== 'undefined') {
    (window as any).__DEBUG__ = {
      logger: { debug, info, warn, error },
      advancedLogger,
      taggedLog,
      perfLog,
      userActionLog,
      apiLog,
      getLogs,
      clearLogs,
      exportLogs,
      debugInfo,
      config: currentConfig,
    };

    info('Debug tools exposed to window.__DEBUG__');
  }
}
