/**
 * 文件工具函数
 * 支持多端兼容的文件处理功能
 */

import Taro from '@tarojs/taro';

/**
 * 文件信息接口
 */
interface FileInfo {
  name: string;
  size: number;
  type: string;
  extension: string;
  lastModified?: number;
}

/**
 * 图片信息接口
 */
interface ImageInfo extends FileInfo {
  width: number;
  height: number;
  aspectRatio: number;
}

/**
 * 压缩选项接口
 */
interface CompressOptions {
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  format?: 'jpeg' | 'png' | 'webp';
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 扩展名
 */
export function getFileExtension(filename: string): string {
  if (!filename) return '';
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex === -1 ? '' : filename.slice(lastDotIndex + 1).toLowerCase();
}

/**
 * 获取文件名（不包含扩展名）
 * @param filename 文件名
 * @returns 文件名
 */
export function getFileName(filename: string): string {
  if (!filename) return '';
  const lastDotIndex = filename.lastIndexOf('.');
  const lastSlashIndex = Math.max(filename.lastIndexOf('/'), filename.lastIndexOf('\\'));
  const start = lastSlashIndex + 1;
  const end = lastDotIndex === -1 ? filename.length : lastDotIndex;
  return filename.slice(start, end);
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的文件大小
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
}

/**
 * 判断是否为图片文件
 * @param filename 文件名或文件类型
 * @returns 是否为图片
 */
export function isImage(filename: string): boolean {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'];
  const extension = getFileExtension(filename);
  return imageExtensions.includes(extension);
}

/**
 * 判断是否为视频文件
 * @param filename 文件名或文件类型
 * @returns 是否为视频
 */
export function isVideo(filename: string): boolean {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'];
  const extension = getFileExtension(filename);
  return videoExtensions.includes(extension);
}

/**
 * 判断是否为音频文件
 * @param filename 文件名或文件类型
 * @returns 是否为音频
 */
export function isAudio(filename: string): boolean {
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'];
  const extension = getFileExtension(filename);
  return audioExtensions.includes(extension);
}

/**
 * 判断是否为文档文件
 * @param filename 文件名或文件类型
 * @returns 是否为文档
 */
export function isDocument(filename: string): boolean {
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'];
  const extension = getFileExtension(filename);
  return documentExtensions.includes(extension);
}

/**
 * 选择图片
 * @param options 选择选项
 * @returns 选择的图片信息
 */
export async function chooseImage(options: {
  count?: number;
  sizeType?: ('original' | 'compressed')[];
  sourceType?: ('album' | 'camera')[];
} = {}): Promise<string[]> {
  try {
    const {
      count = 1,
      sizeType = ['original', 'compressed'],
      sourceType = ['album', 'camera'],
    } = options;
    
    const result = await Taro.chooseImage({
      count,
      sizeType,
      sourceType,
    });
    
    return result.tempFilePaths;
  } catch (error) {
    console.warn('选择图片失败:', error);
    return [];
  }
}

/**
 * 选择视频
 * @param options 选择选项
 * @returns 选择的视频信息
 */
export async function chooseVideo(options: {
  sourceType?: ('album' | 'camera')[];
  maxDuration?: number;
  camera?: 'back' | 'front';
} = {}): Promise<{
  tempFilePath: string;
  duration: number;
  size: number;
  height: number;
  width: number;
} | null> {
  try {
    const {
      sourceType = ['album', 'camera'],
      maxDuration = 60,
      camera = 'back',
    } = options;
    
    const result = await Taro.chooseVideo({
      sourceType,
      maxDuration,
      camera,
    });
    
    return result;
  } catch (error) {
    console.warn('选择视频失败:', error);
    return null;
  }
}

/**
 * 选择文件
 * @param options 选择选项
 * @returns 选择的文件信息
 */
export async function chooseMessageFile(options: {
  count?: number;
  type?: 'all' | 'video' | 'image' | 'file';
  extension?: string[];
} = {}): Promise<Array<{
  path: string;
  size: number;
  name: string;
  type: string;
}>> {
  try {
    const {
      count = 1,
      type = 'all',
      extension,
    } = options;
    
    const result = await Taro.chooseMessageFile({
      count,
      type,
      extension,
    });
    
    return result.tempFiles;
  } catch (error) {
    console.warn('选择文件失败:', error);
    return [];
  }
}

/**
 * 获取图片信息
 * @param src 图片路径
 * @returns 图片信息
 */
export async function getImageInfo(src: string): Promise<ImageInfo | null> {
  try {
    const result = await Taro.getImageInfo({ src });
    
    return {
      name: getFileName(src),
      size: 0, // Taro.getImageInfo 不返回文件大小
      type: 'image/' + getFileExtension(src),
      extension: getFileExtension(src),
      width: result.width,
      height: result.height,
      aspectRatio: result.width / result.height,
    };
  } catch (error) {
    console.warn('获取图片信息失败:', error);
    return null;
  }
}

/**
 * 压缩图片
 * @param src 图片路径
 * @param options 压缩选项
 * @returns 压缩后的图片路径
 */
export async function compressImage(
  src: string,
  options: CompressOptions = {}
): Promise<string | null> {
  try {
    const {
      quality = 0.8,
      maxWidth,
      maxHeight,
      format = 'jpeg',
    } = options;
    
    // 在小程序环境中，可以使用 canvas 进行图片压缩
    if (process.env.TARO_ENV !== 'h5') {
      const result = await Taro.compressImage({
        src,
        quality: quality * 100,
      });
      return result.tempFilePath;
    }
    
    // 在 H5 环境中，需要使用 canvas 进行压缩
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        let { width, height } = img;
        
        // 计算压缩后的尺寸
        if (maxWidth && width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        if (maxHeight && height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              resolve(url);
            } else {
              resolve(null);
            }
          },
          `image/${format}`,
          quality
        );
      };
      
      img.onerror = () => resolve(null);
      img.src = src;
    });
  } catch (error) {
    console.warn('压缩图片失败:', error);
    return null;
  }
}

/**
 * 保存图片到相册
 * @param filePath 图片路径
 * @returns 是否保存成功
 */
export async function saveImageToPhotosAlbum(filePath: string): Promise<boolean> {
  try {
    await Taro.saveImageToPhotosAlbum({ filePath });
    return true;
  } catch (error) {
    console.warn('保存图片到相册失败:', error);
    return false;
  }
}

/**
 * 保存视频到相册
 * @param filePath 视频路径
 * @returns 是否保存成功
 */
export async function saveVideoToPhotosAlbum(filePath: string): Promise<boolean> {
  try {
    await Taro.saveVideoToPhotosAlbum({ filePath });
    return true;
  } catch (error) {
    console.warn('保存视频到相册失败:', error);
    return false;
  }
}

/**
 * 预览图片
 * @param options 预览选项
 */
export function previewImage(options: {
  urls: string[];
  current?: string;
}): void {
  try {
    Taro.previewImage(options);
  } catch (error) {
    console.warn('预览图片失败:', error);
  }
}

/**
 * 下载文件
 * @param url 文件URL
 * @param onProgress 进度回调
 * @returns 下载的文件路径
 */
export async function downloadFile(
  url: string,
  onProgress?: (progress: number) => void
): Promise<string | null> {
  try {
    const downloadTask = Taro.downloadFile({
      url,
      success: () => {},
      fail: () => {},
    });
    
    if (onProgress) {
      downloadTask.onProgressUpdate((res) => {
        const progress = res.totalBytesWritten / res.totalBytesExpectedToWrite;
        onProgress(progress);
      });
    }
    
    const result = await downloadTask;
    return result.tempFilePath;
  } catch (error) {
    console.warn('下载文件失败:', error);
    return null;
  }
}

/**
 * 获取文件系统管理器（仅小程序环境）
 */
export function getFileSystemManager() {
  try {
    return Taro.getFileSystemManager();
  } catch (error) {
    console.warn('获取文件系统管理器失败:', error);
    return null;
  }
}

/**
 * 读取文件内容
 * @param filePath 文件路径
 * @param encoding 编码格式
 * @returns 文件内容
 */
export async function readFile(
  filePath: string,
  encoding: 'ascii' | 'base64' | 'binary' | 'hex' | 'ucs2' | 'ucs-2' | 'utf16le' | 'utf-16le' | 'utf8' | 'utf-8' | 'latin1' = 'utf8'
): Promise<string | null> {
  try {
    const fs = getFileSystemManager();
    if (!fs) return null;
    
    const result = await new Promise<string>((resolve, reject) => {
      fs.readFile({
        filePath,
        encoding,
        success: (res) => resolve(res.data as string),
        fail: reject,
      });
    });
    
    return result;
  } catch (error) {
    console.warn('读取文件失败:', error);
    return null;
  }
}

/**
 * 写入文件
 * @param filePath 文件路径
 * @param data 文件内容
 * @param encoding 编码格式
 * @returns 是否写入成功
 */
export async function writeFile(
  filePath: string,
  data: string,
  encoding: 'ascii' | 'base64' | 'binary' | 'hex' | 'ucs2' | 'ucs-2' | 'utf16le' | 'utf-16le' | 'utf8' | 'utf-8' | 'latin1' = 'utf8'
): Promise<boolean> {
  try {
    const fs = getFileSystemManager();
    if (!fs) return false;
    
    await new Promise<void>((resolve, reject) => {
      fs.writeFile({
        filePath,
        data,
        encoding,
        success: () => resolve(),
        fail: reject,
      });
    });
    
    return true;
  } catch (error) {
    console.warn('写入文件失败:', error);
    return false;
  }
}
