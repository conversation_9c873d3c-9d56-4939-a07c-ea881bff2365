/**
 * 设备工具函数
 * 支持多端兼容的设备信息获取功能
 */

import Taro from '@tarojs/taro';

/**
 * 设备信息接口
 */
interface DeviceInfo {
  platform: Platform;
  os: string;
  osVersion: string;
  brand: string;
  model: string;
  screenWidth: number;
  screenHeight: number;
  pixelRatio: number;
  statusBarHeight: number;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

/**
 * 网络信息接口
 */
interface NetworkInfo {
  type: 'wifi' | 'cellular' | 'none' | 'unknown';
  isConnected: boolean;
  isInternetReachable: boolean;
}

/**
 * 获取设备信息
 */
export async function getDeviceInfo(): Promise<DeviceInfo> {
  try {
    const systemInfo = await Taro.getSystemInfo();
    
    return {
      platform: (process.env.TARO_ENV as Platform) || 'unknown',
      os: systemInfo.platform || 'unknown',
      osVersion: systemInfo.system || 'unknown',
      brand: systemInfo.brand || 'unknown',
      model: systemInfo.model || 'unknown',
      screenWidth: systemInfo.screenWidth || 0,
      screenHeight: systemInfo.screenHeight || 0,
      pixelRatio: systemInfo.pixelRatio || 1,
      statusBarHeight: systemInfo.statusBarHeight || 0,
      safeAreaInsets: {
        top: systemInfo.safeArea?.top || systemInfo.statusBarHeight || 0,
        bottom: systemInfo.screenHeight - (systemInfo.safeArea?.bottom || systemInfo.screenHeight) || 0,
        left: systemInfo.safeArea?.left || 0,
        right: systemInfo.screenWidth - (systemInfo.safeArea?.right || systemInfo.screenWidth) || 0,
      },
    };
  } catch (error) {
    console.warn('获取设备信息失败:', error);
    return {
      platform: (process.env.TARO_ENV as Platform) || 'unknown',
      os: 'unknown',
      osVersion: 'unknown',
      brand: 'unknown',
      model: 'unknown',
      screenWidth: 375,
      screenHeight: 667,
      pixelRatio: 2,
      statusBarHeight: 20,
      safeAreaInsets: {
        top: 20,
        bottom: 0,
        left: 0,
        right: 0,
      },
    };
  }
}

/**
 * 获取网络信息
 */
export async function getNetworkInfo(): Promise<NetworkInfo> {
  try {
    const networkInfo = await Taro.getNetworkType();
    
    const isConnected = networkInfo.networkType !== 'none';
    
    return {
      type: networkInfo.networkType as NetworkInfo['type'],
      isConnected,
      isInternetReachable: isConnected, // 简化处理，实际可能需要更复杂的检测
    };
  } catch (error) {
    console.warn('获取网络信息失败:', error);
    return {
      type: 'unknown',
      isConnected: true,
      isInternetReachable: true,
    };
  }
}

/**
 * 判断是否为移动设备
 */
export function isMobile(): boolean {
  const platform = process.env.TARO_ENV;
  return platform !== 'h5' || (typeof window !== 'undefined' && /Mobi|Android/i.test(navigator.userAgent));
}

/**
 * 判断是否为平板设备
 */
export function isTablet(): boolean {
  if (process.env.TARO_ENV !== 'h5') {
    return false; // 小程序环境暂不支持平板检测
  }
  
  if (typeof window === 'undefined') return false;
  
  const userAgent = navigator.userAgent;
  return /iPad|Android(?!.*Mobile)|Tablet/i.test(userAgent);
}

/**
 * 判断是否为桌面设备
 */
export function isDesktop(): boolean {
  return !isMobile() && !isTablet();
}

/**
 * 判断是否为 iOS 设备
 */
export function isIOS(): boolean {
  const platform = process.env.TARO_ENV;
  
  if (platform === 'weapp' || platform === 'swan' || platform === 'alipay' || platform === 'tt' || platform === 'qq' || platform === 'jd') {
    // 在小程序环境中，可以通过系统信息判断
    return false; // 需要异步获取，这里简化处理
  }
  
  if (platform === 'rn') {
    return false; // RN 环境需要特殊处理
  }
  
  if (typeof window !== 'undefined') {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }
  
  return false;
}

/**
 * 判断是否为 Android 设备
 */
export function isAndroid(): boolean {
  const platform = process.env.TARO_ENV;
  
  if (platform === 'weapp' || platform === 'swan' || platform === 'alipay' || platform === 'tt' || platform === 'qq' || platform === 'jd') {
    return false; // 需要异步获取，这里简化处理
  }
  
  if (platform === 'rn') {
    return false; // RN 环境需要特殊处理
  }
  
  if (typeof window !== 'undefined') {
    return /Android/.test(navigator.userAgent);
  }
  
  return false;
}

/**
 * 判断是否为微信环境
 */
export function isWeChat(): boolean {
  const platform = process.env.TARO_ENV;
  
  if (platform === 'weapp') {
    return true;
  }
  
  if (platform === 'h5' && typeof window !== 'undefined') {
    return /MicroMessenger/i.test(navigator.userAgent);
  }
  
  return false;
}

/**
 * 判断是否为支付宝环境
 */
export function isAlipay(): boolean {
  const platform = process.env.TARO_ENV;
  
  if (platform === 'alipay') {
    return true;
  }
  
  if (platform === 'h5' && typeof window !== 'undefined') {
    return /AlipayClient/i.test(navigator.userAgent);
  }
  
  return false;
}

/**
 * 获取屏幕方向
 */
export function getOrientation(): 'portrait' | 'landscape' {
  if (typeof window === 'undefined') {
    return 'portrait';
  }
  
  if (window.orientation !== undefined) {
    return Math.abs(window.orientation) === 90 ? 'landscape' : 'portrait';
  }
  
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
}

/**
 * 监听屏幕方向变化
 */
export function onOrientationChange(callback: (orientation: 'portrait' | 'landscape') => void): () => void {
  if (typeof window === 'undefined') {
    return () => {};
  }
  
  const handleOrientationChange = () => {
    callback(getOrientation());
  };
  
  window.addEventListener('orientationchange', handleOrientationChange);
  window.addEventListener('resize', handleOrientationChange);
  
  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange);
    window.removeEventListener('resize', handleOrientationChange);
  };
}

/**
 * 监听网络状态变化
 */
export function onNetworkStatusChange(callback: (networkInfo: NetworkInfo) => void): () => void {
  const handleNetworkChange = async () => {
    const networkInfo = await getNetworkInfo();
    callback(networkInfo);
  };
  
  try {
    Taro.onNetworkStatusChange(handleNetworkChange);
    
    return () => {
      Taro.offNetworkStatusChange(handleNetworkChange);
    };
  } catch (error) {
    console.warn('监听网络状态变化失败:', error);
    return () => {};
  }
}

/**
 * 获取电池信息（仅部分平台支持）
 */
export async function getBatteryInfo(): Promise<{
  level: number;
  isCharging: boolean;
} | null> {
  try {
    const batteryInfo = await Taro.getBatteryInfo();
    return {
      level: batteryInfo.level / 100,
      isCharging: batteryInfo.isCharging,
    };
  } catch (error) {
    console.warn('获取电池信息失败:', error);
    return null;
  }
}

/**
 * 获取剪贴板内容
 */
export async function getClipboardData(): Promise<string> {
  try {
    const result = await Taro.getClipboardData();
    return result.data || '';
  } catch (error) {
    console.warn('获取剪贴板内容失败:', error);
    return '';
  }
}

/**
 * 设置剪贴板内容
 */
export async function setClipboardData(data: string): Promise<boolean> {
  try {
    await Taro.setClipboardData({ data });
    return true;
  } catch (error) {
    console.warn('设置剪贴板内容失败:', error);
    return false;
  }
}

/**
 * 震动反馈
 */
export function vibrate(type: 'short' | 'long' = 'short'): void {
  try {
    if (type === 'short') {
      Taro.vibrateShort();
    } else {
      Taro.vibrateLong();
    }
  } catch (error) {
    console.warn('震动反馈失败:', error);
  }
}

/**
 * 获取位置信息
 */
export async function getLocation(): Promise<{
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  speed?: number;
} | null> {
  try {
    const location = await Taro.getLocation({
      type: 'gcj02',
    });
    
    return {
      latitude: location.latitude,
      longitude: location.longitude,
      accuracy: location.accuracy,
      altitude: location.altitude,
      speed: location.speed,
    };
  } catch (error) {
    console.warn('获取位置信息失败:', error);
    return null;
  }
}

/**
 * 打开位置选择器
 */
export async function chooseLocation(): Promise<{
  name: string;
  address: string;
  latitude: number;
  longitude: number;
} | null> {
  try {
    const location = await Taro.chooseLocation();
    return {
      name: location.name,
      address: location.address,
      latitude: location.latitude,
      longitude: location.longitude,
    };
  } catch (error) {
    console.warn('打开位置选择器失败:', error);
    return null;
  }
}

/**
 * 获取设备唯一标识（仅部分平台支持）
 */
export function getDeviceId(): string {
  try {
    // 在不同平台使用不同的方法获取设备标识
    if (typeof window !== 'undefined' && window.localStorage) {
      let deviceId = localStorage.getItem('device_id');
      if (!deviceId) {
        deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        localStorage.setItem('device_id', deviceId);
      }
      return deviceId;
    }
    
    // 其他平台的处理
    return 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  } catch (error) {
    console.warn('获取设备唯一标识失败:', error);
    return 'device_unknown';
  }
}
