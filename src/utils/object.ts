/**
 * 对象工具函数
 * 支持多端兼容的对象处理功能
 */

/**
 * 深拷贝对象
 * @param obj 原对象
 * @returns 深拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 深度合并对象
 * @param target 目标对象
 * @param sources 源对象数组
 * @returns 合并后的对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target;
  
  const source = sources.shift();
  if (!source) return target;
  
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        deepMerge(target[key], source[key]);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }
  
  return deepMerge(target, ...sources);
}

/**
 * 判断是否为对象
 * @param obj 待判断的值
 * @returns 是否为对象
 */
export function isObject(obj: any): obj is Record<string, any> {
  return obj !== null && typeof obj === 'object' && !Array.isArray(obj);
}

/**
 * 判断对象是否为空
 * @param obj 对象
 * @returns 是否为空
 */
export function isEmpty(obj: any): boolean {
  if (obj === null || obj === undefined) return true;
  if (Array.isArray(obj)) return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
}

/**
 * 获取对象指定路径的值
 * @param obj 对象
 * @param path 路径，支持 'a.b.c' 或 ['a', 'b', 'c'] 格式
 * @param defaultValue 默认值
 * @returns 路径对应的值
 */
export function get<T = any>(
  obj: any,
  path: string | string[],
  defaultValue?: T
): T {
  if (!obj || (!path && path !== 0)) return defaultValue as T;
  
  const keys = Array.isArray(path) ? path : path.toString().split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === null || result === undefined) {
      return defaultValue as T;
    }
    result = result[key];
  }
  
  return result === undefined ? defaultValue as T : result;
}

/**
 * 设置对象指定路径的值
 * @param obj 对象
 * @param path 路径，支持 'a.b.c' 或 ['a', 'b', 'c'] 格式
 * @param value 值
 * @returns 修改后的对象
 */
export function set<T extends Record<string, any>>(
  obj: T,
  path: string | string[],
  value: any
): T {
  if (!obj || (!path && path !== 0)) return obj;
  
  const keys = Array.isArray(path) ? path : path.toString().split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  return obj;
}

/**
 * 删除对象指定路径的值
 * @param obj 对象
 * @param path 路径，支持 'a.b.c' 或 ['a', 'b', 'c'] 格式
 * @returns 修改后的对象
 */
export function unset<T extends Record<string, any>>(
  obj: T,
  path: string | string[]
): T {
  if (!obj || (!path && path !== 0)) return obj;
  
  const keys = Array.isArray(path) ? path : path.toString().split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key] || typeof current[key] !== 'object') {
      return obj;
    }
    current = current[key];
  }
  
  delete current[keys[keys.length - 1]];
  return obj;
}

/**
 * 检查对象是否包含指定路径
 * @param obj 对象
 * @param path 路径，支持 'a.b.c' 或 ['a', 'b', 'c'] 格式
 * @returns 是否包含路径
 */
export function has(obj: any, path: string | string[]): boolean {
  if (!obj || (!path && path !== 0)) return false;
  
  const keys = Array.isArray(path) ? path : path.toString().split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return false;
    }
    current = current[key];
  }
  
  return true;
}

/**
 * 从对象中选择指定的键
 * @param obj 原对象
 * @param keys 要选择的键数组
 * @returns 新对象
 */
export function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  if (!obj || !Array.isArray(keys)) return {} as Pick<T, K>;
  
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  
  return result;
}

/**
 * 从对象中排除指定的键
 * @param obj 原对象
 * @param keys 要排除的键数组
 * @returns 新对象
 */
export function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  if (!obj || !Array.isArray(keys)) return obj as Omit<T, K>;
  
  const result = { ...obj } as any;
  keys.forEach(key => {
    delete result[key];
  });
  
  return result;
}

/**
 * 扁平化对象
 * @param obj 原对象
 * @param delimiter 分隔符，默认为 '.'
 * @param prefix 前缀
 * @returns 扁平化后的对象
 */
export function flatten(
  obj: Record<string, any>,
  delimiter: string = '.',
  prefix: string = ''
): Record<string, any> {
  if (!isObject(obj)) return {};
  
  const result: Record<string, any> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}${delimiter}${key}` : key;
      
      if (isObject(obj[key]) && !Array.isArray(obj[key])) {
        Object.assign(result, flatten(obj[key], delimiter, newKey));
      } else {
        result[newKey] = obj[key];
      }
    }
  }
  
  return result;
}

/**
 * 反扁平化对象
 * @param obj 扁平化的对象
 * @param delimiter 分隔符，默认为 '.'
 * @returns 反扁平化后的对象
 */
export function unflatten(
  obj: Record<string, any>,
  delimiter: string = '.'
): Record<string, any> {
  if (!isObject(obj)) return {};
  
  const result: Record<string, any> = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      set(result, key.split(delimiter), obj[key]);
    }
  }
  
  return result;
}

/**
 * 对象键值互换
 * @param obj 原对象
 * @returns 键值互换后的对象
 */
export function invert(obj: Record<string, any>): Record<string, string> {
  if (!isObject(obj)) return {};
  
  const result: Record<string, string> = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[obj[key]] = key;
    }
  }
  
  return result;
}

/**
 * 获取对象的所有键
 * @param obj 对象
 * @returns 键数组
 */
export function keys<T extends Record<string, any>>(obj: T): (keyof T)[] {
  return Object.keys(obj || {}) as (keyof T)[];
}

/**
 * 获取对象的所有值
 * @param obj 对象
 * @returns 值数组
 */
export function values<T extends Record<string, any>>(obj: T): T[keyof T][] {
  return Object.values(obj || {});
}

/**
 * 获取对象的所有键值对
 * @param obj 对象
 * @returns 键值对数组
 */
export function entries<T extends Record<string, any>>(obj: T): [keyof T, T[keyof T]][] {
  return Object.entries(obj || {}) as [keyof T, T[keyof T]][];
}

/**
 * 从键值对数组创建对象
 * @param entries 键值对数组
 * @returns 对象
 */
export function fromEntries<T = any>(entries: [string, T][]): Record<string, T> {
  if (!Array.isArray(entries)) return {};
  
  const result: Record<string, T> = {};
  entries.forEach(([key, value]) => {
    result[key] = value;
  });
  
  return result;
}

/**
 * 映射对象的值
 * @param obj 原对象
 * @param mapper 映射函数
 * @returns 新对象
 */
export function mapValues<T, U>(
  obj: Record<string, T>,
  mapper: (value: T, key: string) => U
): Record<string, U> {
  if (!isObject(obj)) return {};
  
  const result: Record<string, U> = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[key] = mapper(obj[key], key);
    }
  }
  
  return result;
}

/**
 * 映射对象的键
 * @param obj 原对象
 * @param mapper 映射函数
 * @returns 新对象
 */
export function mapKeys<T>(
  obj: Record<string, T>,
  mapper: (key: string, value: T) => string
): Record<string, T> {
  if (!isObject(obj)) return {};
  
  const result: Record<string, T> = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = mapper(key, obj[key]);
      result[newKey] = obj[key];
    }
  }
  
  return result;
}
