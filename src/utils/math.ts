/**
 * 数学工具函数
 * 支持多端兼容的数学计算功能
 */

/**
 * 精确加法
 * @param a 加数
 * @param b 被加数
 * @returns 和
 */
export function add(a: number, b: number): number {
  const aDecimalLength = getDecimalLength(a);
  const bDecimalLength = getDecimalLength(b);
  const maxDecimalLength = Math.max(aDecimalLength, bDecimalLength);
  const multiplier = Math.pow(10, maxDecimalLength);
  
  return (Math.round(a * multiplier) + Math.round(b * multiplier)) / multiplier;
}

/**
 * 精确减法
 * @param a 被减数
 * @param b 减数
 * @returns 差
 */
export function subtract(a: number, b: number): number {
  const aDecimalLength = getDecimalLength(a);
  const bDecimalLength = getDecimalLength(b);
  const maxDecimalLength = Math.max(aDecimalLength, bDecimalLength);
  const multiplier = Math.pow(10, maxDecimalLength);
  
  return (Math.round(a * multiplier) - Math.round(b * multiplier)) / multiplier;
}

/**
 * 精确乘法
 * @param a 乘数
 * @param b 被乘数
 * @returns 积
 */
export function multiply(a: number, b: number): number {
  const aDecimalLength = getDecimalLength(a);
  const bDecimalLength = getDecimalLength(b);
  const multiplier = Math.pow(10, aDecimalLength + bDecimalLength);
  
  return (Math.round(a * Math.pow(10, aDecimalLength)) * Math.round(b * Math.pow(10, bDecimalLength))) / multiplier;
}

/**
 * 精确除法
 * @param a 被除数
 * @param b 除数
 * @returns 商
 */
export function divide(a: number, b: number): number {
  if (b === 0) {
    throw new Error('Division by zero');
  }
  
  const aDecimalLength = getDecimalLength(a);
  const bDecimalLength = getDecimalLength(b);
  const aInteger = Math.round(a * Math.pow(10, aDecimalLength));
  const bInteger = Math.round(b * Math.pow(10, bDecimalLength));
  
  return (aInteger / bInteger) * Math.pow(10, bDecimalLength - aDecimalLength);
}

/**
 * 获取小数位数
 * @param num 数字
 * @returns 小数位数
 */
function getDecimalLength(num: number): number {
  const str = num.toString();
  const decimalIndex = str.indexOf('.');
  return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1;
}

/**
 * 四舍五入到指定小数位
 * @param num 数字
 * @param precision 小数位数
 * @returns 四舍五入后的数字
 */
export function round(num: number, precision: number = 0): number {
  const multiplier = Math.pow(10, precision);
  return Math.round(multiply(num, multiplier)) / multiplier;
}

/**
 * 向上取整到指定小数位
 * @param num 数字
 * @param precision 小数位数
 * @returns 向上取整后的数字
 */
export function ceil(num: number, precision: number = 0): number {
  const multiplier = Math.pow(10, precision);
  return Math.ceil(multiply(num, multiplier)) / multiplier;
}

/**
 * 向下取整到指定小数位
 * @param num 数字
 * @param precision 小数位数
 * @returns 向下取整后的数字
 */
export function floor(num: number, precision: number = 0): number {
  const multiplier = Math.pow(10, precision);
  return Math.floor(multiply(num, multiplier)) / multiplier;
}

/**
 * 生成指定范围内的随机数
 * @param min 最小值
 * @param max 最大值
 * @param integer 是否为整数
 * @returns 随机数
 */
export function random(min: number = 0, max: number = 1, integer: boolean = false): number {
  const randomValue = Math.random() * (max - min) + min;
  return integer ? Math.floor(randomValue) : randomValue;
}

/**
 * 生成指定范围内的随机整数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机整数
 */
export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 限制数字在指定范围内
 * @param num 数字
 * @param min 最小值
 * @param max 最大值
 * @returns 限制后的数字
 */
export function clamp(num: number, min: number, max: number): number {
  return Math.min(Math.max(num, min), max);
}

/**
 * 线性插值
 * @param start 起始值
 * @param end 结束值
 * @param t 插值参数 (0-1)
 * @returns 插值结果
 */
export function lerp(start: number, end: number, t: number): number {
  return start + (end - start) * clamp(t, 0, 1);
}

/**
 * 计算百分比
 * @param value 当前值
 * @param total 总值
 * @param precision 小数位数
 * @returns 百分比
 */
export function percentage(value: number, total: number, precision: number = 2): number {
  if (total === 0) return 0;
  return round(divide(multiply(value, 100), total), precision);
}

/**
 * 角度转弧度
 * @param degrees 角度
 * @returns 弧度
 */
export function degreesToRadians(degrees: number): number {
  return multiply(degrees, Math.PI / 180);
}

/**
 * 弧度转角度
 * @param radians 弧度
 * @returns 角度
 */
export function radiansToDegrees(radians: number): number {
  return multiply(radians, 180 / Math.PI);
}

/**
 * 计算两点之间的距离
 * @param x1 点1的x坐标
 * @param y1 点1的y坐标
 * @param x2 点2的x坐标
 * @param y2 点2的y坐标
 * @returns 距离
 */
export function distance(x1: number, y1: number, x2: number, y2: number): number {
  const dx = subtract(x2, x1);
  const dy = subtract(y2, y1);
  return Math.sqrt(add(multiply(dx, dx), multiply(dy, dy)));
}

/**
 * 计算数组的平均值
 * @param numbers 数字数组
 * @returns 平均值
 */
export function average(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  const sum = numbers.reduce((acc, num) => add(acc, num), 0);
  return divide(sum, numbers.length);
}

/**
 * 计算数组的中位数
 * @param numbers 数字数组
 * @returns 中位数
 */
export function median(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  
  const sorted = [...numbers].sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);
  
  if (sorted.length % 2 === 0) {
    return divide(add(sorted[middle - 1], sorted[middle]), 2);
  } else {
    return sorted[middle];
  }
}

/**
 * 计算数组的众数
 * @param numbers 数字数组
 * @returns 众数数组
 */
export function mode(numbers: number[]): number[] {
  if (numbers.length === 0) return [];
  
  const frequency: Record<number, number> = {};
  let maxFreq = 0;
  
  // 计算频率
  numbers.forEach(num => {
    frequency[num] = (frequency[num] || 0) + 1;
    maxFreq = Math.max(maxFreq, frequency[num]);
  });
  
  // 找出所有众数
  return Object.keys(frequency)
    .filter(key => frequency[Number(key)] === maxFreq)
    .map(Number);
}

/**
 * 计算数组的方差
 * @param numbers 数字数组
 * @returns 方差
 */
export function variance(numbers: number[]): number {
  if (numbers.length === 0) return 0;
  
  const avg = average(numbers);
  const squaredDiffs = numbers.map(num => multiply(subtract(num, avg), subtract(num, avg)));
  return average(squaredDiffs);
}

/**
 * 计算数组的标准差
 * @param numbers 数字数组
 * @returns 标准差
 */
export function standardDeviation(numbers: number[]): number {
  return Math.sqrt(variance(numbers));
}

/**
 * 计算最大公约数
 * @param a 数字a
 * @param b 数字b
 * @returns 最大公约数
 */
export function gcd(a: number, b: number): number {
  a = Math.abs(Math.floor(a));
  b = Math.abs(Math.floor(b));
  
  while (b !== 0) {
    const temp = b;
    b = a % b;
    a = temp;
  }
  
  return a;
}

/**
 * 计算最小公倍数
 * @param a 数字a
 * @param b 数字b
 * @returns 最小公倍数
 */
export function lcm(a: number, b: number): number {
  return Math.abs(a * b) / gcd(a, b);
}

/**
 * 判断是否为质数
 * @param num 数字
 * @returns 是否为质数
 */
export function isPrime(num: number): boolean {
  if (num < 2) return false;
  if (num === 2) return true;
  if (num % 2 === 0) return false;
  
  for (let i = 3; i <= Math.sqrt(num); i += 2) {
    if (num % i === 0) return false;
  }
  
  return true;
}

/**
 * 计算阶乘
 * @param n 数字
 * @returns 阶乘
 */
export function factorial(n: number): number {
  if (n < 0) throw new Error('Factorial is not defined for negative numbers');
  if (n === 0 || n === 1) return 1;
  
  let result = 1;
  for (let i = 2; i <= n; i++) {
    result = multiply(result, i);
  }
  
  return result;
}

/**
 * 计算斐波那契数列第n项
 * @param n 项数
 * @returns 斐波那契数
 */
export function fibonacci(n: number): number {
  if (n < 0) throw new Error('Fibonacci is not defined for negative numbers');
  if (n === 0) return 0;
  if (n === 1) return 1;
  
  let a = 0;
  let b = 1;
  
  for (let i = 2; i <= n; i++) {
    const temp = add(a, b);
    a = b;
    b = temp;
  }
  
  return b;
}

/**
 * 数字格式化（添加千分位分隔符）
 * @param num 数字
 * @param separator 分隔符
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number, separator: string = ','): string {
  const parts = num.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  return parts.join('.');
}

/**
 * 将数字转换为中文数字
 * @param num 数字
 * @returns 中文数字
 */
export function toChineseNumber(num: number): string {
  const digits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿'];
  
  if (num === 0) return '零';
  if (num < 0) return '负' + toChineseNumber(-num);
  
  const str = Math.floor(num).toString();
  let result = '';
  let zeroFlag = false;
  
  for (let i = 0; i < str.length; i++) {
    const digit = parseInt(str[i]);
    const unit = units[str.length - i - 1];
    
    if (digit === 0) {
      zeroFlag = true;
    } else {
      if (zeroFlag && result) {
        result += '零';
      }
      result += digits[digit] + unit;
      zeroFlag = false;
    }
  }
  
  return result;
}
