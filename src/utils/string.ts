/**
 * 字符串工具函数
 * 支持多端兼容的字符串处理功能
 */

/**
 * 截断字符串
 * @param str 原字符串
 * @param length 最大长度
 * @param suffix 后缀，默认为 '...'
 * @returns 截断后的字符串
 */
export function truncate(str: string, length: number, suffix: string = '...'): string {
  if (!str || str.length <= length) {
    return str;
  }
  return str.slice(0, length - suffix.length) + suffix;
}

/**
 * 首字母大写
 * @param str 字符串
 * @returns 首字母大写的字符串
 */
export function capitalize(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * 驼峰命名转换
 * @param str 字符串
 * @returns 驼峰命名的字符串
 */
export function camelCase(str: string): string {
  if (!str) return str;
  return str
    .replace(/[-_\s]+(.)?/g, (_, char) => (char ? char.toUpperCase() : ''))
    .replace(/^[A-Z]/, (char) => char.toLowerCase());
}

/**
 * 帕斯卡命名转换
 * @param str 字符串
 * @returns 帕斯卡命名的字符串
 */
export function pascalCase(str: string): string {
  const camelCased = camelCase(str);
  return camelCased.charAt(0).toUpperCase() + camelCased.slice(1);
}

/**
 * 短横线命名转换
 * @param str 字符串
 * @returns 短横线命名的字符串
 */
export function kebabCase(str: string): string {
  if (!str) return str;
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * 下划线命名转换
 * @param str 字符串
 * @returns 下划线命名的字符串
 */
export function snakeCase(str: string): string {
  if (!str) return str;
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase();
}

/**
 * 移除字符串中的HTML标签
 * @param str 包含HTML的字符串
 * @returns 纯文本字符串
 */
export function stripHtml(str: string): string {
  if (!str) return str;
  return str.replace(/<[^>]*>/g, '');
}

/**
 * 转义HTML特殊字符
 * @param str 字符串
 * @returns 转义后的字符串
 */
export function escapeHtml(str: string): string {
  if (!str) return str;
  const htmlEscapes: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
  };
  return str.replace(/[&<>"']/g, (char) => htmlEscapes[char]);
}

/**
 * 反转义HTML特殊字符
 * @param str 字符串
 * @returns 反转义后的字符串
 */
export function unescapeHtml(str: string): string {
  if (!str) return str;
  const htmlUnescapes: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
  };
  return str.replace(/&(?:amp|lt|gt|quot|#39);/g, (entity) => htmlUnescapes[entity]);
}

/**
 * 高亮关键词
 * @param str 原字符串
 * @param keyword 关键词
 * @param className CSS类名，默认为 'highlight'
 * @param caseSensitive 是否区分大小写，默认为 false
 * @returns 高亮后的HTML字符串
 */
export function highlight(
  str: string,
  keyword: string,
  className: string = 'highlight',
  caseSensitive: boolean = false
): string {
  if (!str || !keyword) return str;
  
  const flags = caseSensitive ? 'g' : 'gi';
  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, flags);
  
  return str.replace(regex, `<span class="${className}">$1</span>`);
}

/**
 * 生成随机字符串
 * @param length 长度
 * @param chars 字符集，默认为字母和数字
 * @returns 随机字符串
 */
export function randomString(
  length: number,
  chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成UUID
 * @returns UUID字符串
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * 计算字符串字节长度（中文字符按2个字节计算）
 * @param str 字符串
 * @returns 字节长度
 */
export function getByteLength(str: string): number {
  if (!str) return 0;
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    if (charCode >= 0x4e00 && charCode <= 0x9fff) {
      // 中文字符
      length += 2;
    } else {
      length += 1;
    }
  }
  return length;
}

/**
 * 按字节长度截断字符串
 * @param str 原字符串
 * @param maxBytes 最大字节数
 * @param suffix 后缀，默认为 '...'
 * @returns 截断后的字符串
 */
export function truncateByBytes(str: string, maxBytes: number, suffix: string = '...'): string {
  if (!str) return str;
  
  let length = 0;
  let result = '';
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charAt(i);
    const charCode = str.charCodeAt(i);
    const charBytes = charCode >= 0x4e00 && charCode <= 0x9fff ? 2 : 1;
    
    if (length + charBytes > maxBytes) {
      if (result.length > 0) {
        result += suffix;
      }
      break;
    }
    
    result += char;
    length += charBytes;
  }
  
  return result;
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数，默认为 2
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(decimals))} ${sizes[i]}`;
}

/**
 * 格式化数字（添加千分位分隔符）
 * @param num 数字
 * @param separator 分隔符，默认为 ','
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number | string, separator: string = ','): string {
  const numStr = num.toString();
  const parts = numStr.split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  return parts.join('.');
}

/**
 * 隐藏手机号中间四位
 * @param phone 手机号
 * @returns 隐藏后的手机号
 */
export function maskPhone(phone: string): string {
  if (!phone || phone.length !== 11) return phone;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 隐藏身份证号中间部分
 * @param idCard 身份证号
 * @returns 隐藏后的身份证号
 */
export function maskIdCard(idCard: string): string {
  if (!idCard) return idCard;
  if (idCard.length === 15) {
    return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2');
  } else if (idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  }
  return idCard;
}

/**
 * 隐藏邮箱地址
 * @param email 邮箱地址
 * @returns 隐藏后的邮箱地址
 */
export function maskEmail(email: string): string {
  if (!email || !email.includes('@')) return email;
  const [username, domain] = email.split('@');
  if (username.length <= 2) {
    return `${username[0]}*@${domain}`;
  }
  const maskedUsername = username[0] + '*'.repeat(username.length - 2) + username[username.length - 1];
  return `${maskedUsername}@${domain}`;
}

/**
 * 检查字符串是否为空（包括空白字符）
 * @param str 字符串
 * @returns 是否为空
 */
export function isEmpty(str: string | null | undefined): boolean {
  return !str || str.trim().length === 0;
}

/**
 * 检查字符串是否不为空
 * @param str 字符串
 * @returns 是否不为空
 */
export function isNotEmpty(str: string | null | undefined): boolean {
  return !isEmpty(str);
}

/**
 * 移除字符串首尾空白字符
 * @param str 字符串
 * @returns 处理后的字符串
 */
export function trim(str: string): string {
  return str ? str.trim() : '';
}

/**
 * 移除字符串左侧空白字符
 * @param str 字符串
 * @returns 处理后的字符串
 */
export function trimStart(str: string): string {
  return str ? str.replace(/^\s+/, '') : '';
}

/**
 * 移除字符串右侧空白字符
 * @param str 字符串
 * @returns 处理后的字符串
 */
export function trimEnd(str: string): string {
  return str ? str.replace(/\s+$/, '') : '';
}
