/**
 * 错误处理工具函数
 * 支持多端兼容的错误处理和监控功能
 */

import Taro from '@tarojs/taro';
import { storage } from './storage';
import { debug, warn, error as logError } from './debug';
import { getApiBaseUrl } from '../config/env';

/**
 * 错误信息接口
 */
interface ErrorInfo {
  code: string | number;
  message: string;
  stack?: string;
  timestamp: number;
  platform: Platform;
  version: string;
  userId?: string;
  extra?: Record<string, any>;
}

/**
 * 错误上下文接口
 */
interface ErrorContext {
  userId?: string;
  sessionId?: string;
  timestamp: number;
  url?: string;
  userAgent?: string;
  platform: Platform;
  version: string;
  extra?: Record<string, any>;
}

/**
 * 错误报告接口
 */
interface ErrorReport {
  error: Error;
  context: ErrorContext;
  stackTrace?: string;
  breadcrumbs?: Array<{
    timestamp: number;
    message: string;
    category: string;
    level: 'info' | 'warning' | 'error';
  }>;
}

/**
 * 面包屑记录
 */
const breadcrumbs: ErrorReport['breadcrumbs'] = [];
const MAX_BREADCRUMBS = 50;

/**
 * 错误监听器
 */
const errorListeners: Array<(errorInfo: ErrorInfo) => void> = [];

/**
 * 添加面包屑
 * @param message 消息
 * @param category 分类
 * @param level 级别
 */
export function addBreadcrumb(
  message: string,
  category: string = 'manual',
  level: 'info' | 'warning' | 'error' = 'info'
): void {
  breadcrumbs.push({
    timestamp: Date.now(),
    message,
    category,
    level,
  });

  // 保持面包屑数量在限制内
  if (breadcrumbs.length > MAX_BREADCRUMBS) {
    breadcrumbs.shift();
  }
}

/**
 * 获取错误上下文
 * @returns 错误上下文
 */
function getErrorContext(): ErrorContext {
  const context: ErrorContext = {
    timestamp: Date.now(),
    platform: (process.env.TARO_ENV as Platform) || 'unknown',
    version: '1.0.0', // 可以从配置中获取
  };

  // 在 H5 环境中添加更多信息
  if (typeof window !== 'undefined') {
    context.url = window.location?.href;
    context.userAgent = navigator?.userAgent;
  }

  // 从存储中获取用户信息
  try {
    const userInfo = storage.getItem('user-storage');
    if (userInfo?.userInfo?.id) {
      context.userId = userInfo.userInfo.id;
    }
  } catch (error) {
    // 忽略获取用户信息的错误
  }

  return context;
}

/**
 * 创建错误信息
 * @param error 错误对象
 * @param extra 额外信息
 * @returns 错误信息
 */
function createErrorInfo(error: Error, extra?: Record<string, any>): ErrorInfo {
  const context = getErrorContext();

  return {
    code: error.name || 'UnknownError',
    message: error.message || '未知错误',
    stack: error.stack,
    timestamp: context.timestamp,
    platform: context.platform,
    version: context.version,
    userId: context.userId,
    extra: {
      ...extra,
      url: context.url,
      userAgent: context.userAgent,
    },
  };
}

/**
 * 记录错误
 * @param error 错误对象或错误信息
 * @param extra 额外信息
 */
export function logError(error: Error | string, extra?: Record<string, any>): void {
  let errorObj: Error;

  if (typeof error === 'string') {
    errorObj = new Error(error);
  } else {
    errorObj = error;
  }

  const errorInfo = createErrorInfo(errorObj, extra);

  // 添加到面包屑
  addBreadcrumb(errorInfo.message, 'error', 'error');

  // 控制台输出
  console.error('Error logged:', errorInfo);

  // 通知监听器
  errorListeners.forEach(listener => {
    try {
      listener(errorInfo);
    } catch (listenerError) {
      console.error('Error in error listener:', listenerError);
    }
  });

  // 存储错误信息
  try {
    const errors = storage.getItem<ErrorInfo[]>('app_errors', []);
    errors.push(errorInfo);

    // 只保留最近的 100 个错误
    if (errors.length > 100) {
      errors.splice(0, errors.length - 100);
    }

    storage.setItem('app_errors', errors);
  } catch (storageError) {
    console.error('Failed to store error:', storageError);
  }
}

/**
 * 添加错误监听器
 * @param listener 监听器函数
 * @returns 移除监听器的函数
 */
export function addErrorListener(listener: (errorInfo: ErrorInfo) => void): () => void {
  errorListeners.push(listener);

  return () => {
    const index = errorListeners.indexOf(listener);
    if (index > -1) {
      errorListeners.splice(index, 1);
    }
  };
}

/**
 * 获取存储的错误列表
 * @returns 错误列表
 */
export function getStoredErrors(): ErrorInfo[] {
  try {
    return storage.getItem<ErrorInfo[]>('app_errors', []);
  } catch (error) {
    console.error('Failed to get stored errors:', error);
    return [];
  }
}

/**
 * 清除存储的错误
 */
export function clearStoredErrors(): void {
  try {
    storage.removeItem('app_errors');
  } catch (error) {
    console.error('Failed to clear stored errors:', error);
  }
}

/**
 * 创建错误边界高阶组件
 * @param WrappedComponent 被包装的组件
 * @param fallbackComponent 错误时显示的组件
 * @returns 错误边界组件
 */
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  fallbackComponent?: React.ComponentType<{ error: Error; retry: () => void }>
) {
  return class ErrorBoundary extends React.Component<P, { hasError: boolean; error?: Error }> {
    constructor(props: P) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      logError(error, {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      });
    }

    retry = () => {
      this.setState({ hasError: false, error: undefined });
    };

    render() {
      if (this.state.hasError && this.state.error) {
        if (fallbackComponent) {
          const FallbackComponent = fallbackComponent;
          return <FallbackComponent error={this.state.error} retry={this.retry} />;
        }

        return (
          <div style={{ padding: '20px', textAlign: 'center' }}>
            <h3>出现了一些问题</h3>
            <p>{this.state.error.message}</p>
            <button onClick={this.retry}>重试</button>
          </div>
        );
      }

      return <WrappedComponent {...this.props} />;
    }
  };
}

/**
 * 异步函数错误处理装饰器
 * @param fn 异步函数
 * @param onError 错误处理函数
 * @returns 包装后的函数
 */
export function withAsyncErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  onError?: (error: Error) => void
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      const errorObj = error instanceof Error ? error : new Error(String(error));
      logError(errorObj);

      if (onError) {
        onError(errorObj);
      } else {
        throw errorObj;
      }
    }
  }) as T;
}

/**
 * Promise 错误处理
 * @param promise Promise 对象
 * @param onError 错误处理函数
 * @returns 处理后的 Promise
 */
export function handlePromiseError<T>(
  promise: Promise<T>,
  onError?: (error: Error) => void
): Promise<T | undefined> {
  return promise.catch(error => {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logError(errorObj);

    if (onError) {
      onError(errorObj);
      return undefined;
    } else {
      throw errorObj;
    }
  });
}

/**
 * 安全执行函数
 * @param fn 要执行的函数
 * @param onError 错误处理函数
 * @returns 执行结果
 */
export function safeExecute<T>(
  fn: () => T,
  onError?: (error: Error) => T
): T | undefined {
  try {
    return fn();
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error(String(error));
    logError(errorObj);

    if (onError) {
      return onError(errorObj);
    }

    return undefined;
  }
}

/**
 * 重试函数
 * @param fn 要重试的函数
 * @param maxRetries 最大重试次数
 * @param delay 重试延迟（毫秒）
 * @returns Promise
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (i === maxRetries) {
        logError(lastError, { retryAttempts: i });
        throw lastError;
      }

      // 等待指定时间后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * 初始化全局错误处理
 */
export function initGlobalErrorHandling(): void {
  // 处理未捕获的 Promise 错误
  if (typeof window !== 'undefined') {
    window.addEventListener('unhandledrejection', (event) => {
      logError(new Error(event.reason), {
        type: 'unhandledrejection',
        reason: event.reason,
      });
    });

    // 处理全局错误
    window.addEventListener('error', (event) => {
      logError(new Error(event.message), {
        type: 'globalError',
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });
  }

  // 在小程序环境中处理错误
  if (process.env.TARO_ENV !== 'h5') {
    // 可以在这里添加小程序特定的错误处理
  }
}

/**
 * 创建自定义错误类
 */
export class AppError extends Error {
  public code: string;
  public statusCode?: number;
  public extra?: Record<string, any>;

  constructor(
    message: string,
    code: string = 'APP_ERROR',
    statusCode?: number,
    extra?: Record<string, any>
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.extra = extra;
  }
}

/**
 * 网络错误类
 */
export class NetworkError extends AppError {
  constructor(message: string, statusCode?: number, extra?: Record<string, any>) {
    super(message, 'NETWORK_ERROR', statusCode, extra);
    this.name = 'NetworkError';
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends AppError {
  constructor(message: string, extra?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', undefined, extra);
    this.name = 'ValidationError';
  }
}

/**
 * 业务错误类
 */
export class BusinessError extends AppError {
  constructor(message: string, code: string, extra?: Record<string, any>) {
    super(message, code, undefined, extra);
    this.name = 'BusinessError';
  }
}

/**
 * 错误监控配置
 */
export interface ErrorMonitorConfig {
  // 是否启用错误监控
  enabled: boolean;
  // 错误上报地址
  reportUrl?: string;
  // 采样率 (0-1)
  sampleRate: number;
  // 最大错误数量
  maxErrors: number;
  // 错误过滤器
  errorFilter?: (error: Error) => boolean;
  // 是否自动上报
  autoReport: boolean;
  // 上报延迟（毫秒）
  reportDelay: number;
  // 是否包含用户信息
  includeUserInfo: boolean;
  // 是否包含设备信息
  includeDeviceInfo: boolean;
  // 自定义标签
  tags?: Record<string, string>;
}

/**
 * 错误监控类
 */
export class ErrorMonitor {
  private config: ErrorMonitorConfig;
  private errorQueue: ErrorReport[] = [];
  private breadcrumbs: Array<{
    timestamp: number;
    message: string;
    category: string;
    level: 'info' | 'warning' | 'error';
    data?: any;
  }> = [];
  private sessionId: string;
  private userId?: string;
  private reportTimer?: NodeJS.Timeout;

  constructor(config: Partial<ErrorMonitorConfig> = {}) {
    this.config = {
      enabled: true,
      sampleRate: 1.0,
      maxErrors: 100,
      autoReport: true,
      reportDelay: 5000,
      includeUserInfo: true,
      includeDeviceInfo: true,
      ...config,
    };

    this.sessionId = this.generateSessionId();
    this.init();
  }

  /**
   * 初始化错误监控
   */
  private init() {
    if (!this.config.enabled) return;

    // 设置全局错误处理
    this.setupGlobalErrorHandlers();

    // 设置定时上报
    if (this.config.autoReport) {
      this.setupAutoReport();
    }
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers() {
    // 处理未捕获的 Promise 错误
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.captureError(new Error(event.reason), {
          type: 'unhandledPromiseRejection',
          promise: event.promise,
        });
      });

      // 处理全局 JavaScript 错误
      window.addEventListener('error', (event) => {
        this.captureError(new Error(event.message), {
          type: 'globalJavaScriptError',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
        });
      });

      // 处理资源加载错误
      window.addEventListener('error', (event) => {
        if (event.target !== window) {
          this.captureError(new Error('Resource loading failed'), {
            type: 'resourceError',
            element: event.target,
            source: (event.target as any)?.src || (event.target as any)?.href,
          });
        }
      }, true);
    }

    // 小程序错误处理
    if (process.env.TARO_ENV !== 'h5') {
      try {
        // 监听小程序错误
        Taro.onError?.((error) => {
          this.captureError(new Error(error), {
            type: 'miniProgramError',
          });
        });

        // 监听页面未找到错误
        Taro.onPageNotFound?.((res) => {
          this.captureError(new Error('Page not found'), {
            type: 'pageNotFound',
            path: res.path,
            query: res.query,
          });
        });
      } catch (e) {
        debug('Failed to setup mini-program error handlers:', e);
      }
    }
  }

  /**
   * 设置自动上报
   */
  private setupAutoReport() {
    this.reportTimer = setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.flushErrors();
      }
    }, this.config.reportDelay);
  }

  /**
   * 生成会话 ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置用户 ID
   */
  setUserId(userId: string) {
    this.userId = userId;
  }

  /**
   * 添加面包屑
   */
  addBreadcrumb(message: string, category: string = 'default', level: 'info' | 'warning' | 'error' = 'info', data?: any) {
    this.breadcrumbs.push({
      timestamp: Date.now(),
      message,
      category,
      level,
      data,
    });

    // 限制面包屑数量
    if (this.breadcrumbs.length > 50) {
      this.breadcrumbs = this.breadcrumbs.slice(-50);
    }
  }

  /**
   * 捕获错误
   */
  captureError(error: Error, extra?: Record<string, any>) {
    if (!this.config.enabled) return;

    // 采样率控制
    if (Math.random() > this.config.sampleRate) return;

    // 错误过滤
    if (this.config.errorFilter && !this.config.errorFilter(error)) return;

    // 创建错误报告
    const errorReport: ErrorReport = {
      error,
      context: this.createErrorContext(extra),
      stackTrace: error.stack,
      breadcrumbs: [...this.breadcrumbs],
    };

    // 添加到队列
    this.errorQueue.push(errorReport);

    // 限制队列大小
    if (this.errorQueue.length > this.config.maxErrors) {
      this.errorQueue = this.errorQueue.slice(-this.config.maxErrors);
    }

    // 记录日志
    logError('Error captured:', error, extra);

    // 立即上报严重错误
    if (this.isCriticalError(error)) {
      this.reportError(errorReport);
    }
  }

  /**
   * 创建错误上下文
   */
  private createErrorContext(extra?: Record<string, any>): ErrorContext {
    const context: ErrorContext = {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      platform: process.env.TARO_ENV as Platform,
      version: process.env.APP_VERSION || '1.0.0',
      extra,
    };

    if (this.config.includeUserInfo && this.userId) {
      context.userId = this.userId;
    }

    if (this.config.includeDeviceInfo) {
      try {
        const systemInfo = Taro.getSystemInfoSync();
        context.extra = {
          ...context.extra,
          deviceInfo: {
            platform: systemInfo.platform,
            system: systemInfo.system,
            version: systemInfo.version,
            model: systemInfo.model,
            brand: systemInfo.brand,
            screenWidth: systemInfo.screenWidth,
            screenHeight: systemInfo.screenHeight,
          },
        };
      } catch (e) {
        debug('Failed to get system info:', e);
      }
    }

    if (typeof window !== 'undefined') {
      context.url = window.location.href;
      context.userAgent = navigator.userAgent;
    }

    return context;
  }

  /**
   * 判断是否为严重错误
   */
  private isCriticalError(error: Error): boolean {
    const criticalPatterns = [
      /network error/i,
      /timeout/i,
      /out of memory/i,
      /stack overflow/i,
      /security/i,
    ];

    return criticalPatterns.some(pattern =>
      pattern.test(error.message) || pattern.test(error.name)
    );
  }

  /**
   * 上报单个错误
   */
  private async reportError(errorReport: ErrorReport) {
    if (!this.config.reportUrl) return;

    try {
      await Taro.request({
        url: this.config.reportUrl,
        method: 'POST',
        data: {
          ...errorReport,
          tags: this.config.tags,
        },
        header: {
          'Content-Type': 'application/json',
        },
      });
    } catch (e) {
      debug('Failed to report error:', e);
    }
  }

  /**
   * 批量上报错误
   */
  async flushErrors() {
    if (this.errorQueue.length === 0 || !this.config.reportUrl) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    try {
      await Taro.request({
        url: this.config.reportUrl,
        method: 'POST',
        data: {
          errors,
          tags: this.config.tags,
          sessionId: this.sessionId,
        },
        header: {
          'Content-Type': 'application/json',
        },
      });
    } catch (e) {
      debug('Failed to flush errors:', e);
      // 重新加入队列
      this.errorQueue.unshift(...errors);
    }
  }

  /**
   * 手动上报错误
   */
  async reportManually() {
    await this.flushErrors();
  }

  /**
   * 清除错误队列
   */
  clearErrors() {
    this.errorQueue = [];
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    return {
      totalErrors: this.errorQueue.length,
      sessionId: this.sessionId,
      userId: this.userId,
      breadcrumbsCount: this.breadcrumbs.length,
    };
  }

  /**
   * 销毁监控器
   */
  destroy() {
    if (this.reportTimer) {
      clearInterval(this.reportTimer);
    }
    this.errorQueue = [];
    this.breadcrumbs = [];
  }
}

/**
 * 错误恢复策略
 */
export interface ErrorRecoveryStrategy {
  name: string;
  condition: (error: Error) => boolean;
  action: (error: Error) => Promise<boolean>;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * 错误恢复管理器
 */
export class ErrorRecoveryManager {
  private strategies: ErrorRecoveryStrategy[] = [];
  private retryCount = new Map<string, number>();

  /**
   * 注册恢复策略
   */
  registerStrategy(strategy: ErrorRecoveryStrategy) {
    this.strategies.push(strategy);
  }

  /**
   * 尝试恢复错误
   */
  async tryRecover(error: Error): Promise<boolean> {
    for (const strategy of this.strategies) {
      if (strategy.condition(error)) {
        const key = `${strategy.name}_${error.message}`;
        const currentRetries = this.retryCount.get(key) || 0;
        const maxRetries = strategy.maxRetries || 3;

        if (currentRetries < maxRetries) {
          try {
            // 延迟重试
            if (strategy.retryDelay && currentRetries > 0) {
              await new Promise(resolve => setTimeout(resolve, strategy.retryDelay));
            }

            const success = await strategy.action(error);

            if (success) {
              this.retryCount.delete(key);
              return true;
            } else {
              this.retryCount.set(key, currentRetries + 1);
            }
          } catch (recoveryError) {
            debug('Error recovery failed:', recoveryError);
            this.retryCount.set(key, currentRetries + 1);
          }
        }
      }
    }

    return false;
  }

  /**
   * 清除重试计数
   */
  clearRetryCount() {
    this.retryCount.clear();
  }
}

/**
 * 全局错误恢复管理器
 */
export const errorRecovery = new ErrorRecoveryManager();

// 注册默认恢复策略
errorRecovery.registerStrategy({
  name: 'networkRetry',
  condition: (error) => error instanceof NetworkError,
  action: async (error) => {
    // 网络错误重试策略
    try {
      const response = await Taro.request({
        url: `${getApiBaseUrl()}/api/health`,
        method: 'GET',
        timeout: 5000,
      });
      return response.statusCode === 200;
    } catch {
      return false;
    }
  },
  maxRetries: 3,
  retryDelay: 2000,
});

errorRecovery.registerStrategy({
  name: 'storageCleanup',
  condition: (error) => error.message.includes('storage') || error.message.includes('quota'),
  action: async (error) => {
    // 存储空间不足时清理缓存
    try {
      await storage.clear();
      return true;
    } catch {
      return false;
    }
  },
  maxRetries: 1,
});

errorRecovery.registerStrategy({
  name: 'pageReload',
  condition: (error) => error.message.includes('chunk') || error.message.includes('loading'),
  action: async (error) => {
    // 资源加载失败时重新加载页面
    if (typeof window !== 'undefined') {
      window.location.reload();
      return true;
    }
    return false;
  },
  maxRetries: 1,
});

/**
 * 全局错误监控实例
 */
export const errorMonitor = new ErrorMonitor({
  reportUrl: `${getApiBaseUrl()}/api/errors`,
  sampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  autoReport: true,
  reportDelay: 10000,
});

/**
 * 增强的错误处理函数
 */
export async function handleError(error: Error, context?: Record<string, any>): Promise<void> {
  // 添加面包屑
  errorMonitor.addBreadcrumb(`Error occurred: ${error.message}`, 'error', 'error', context);

  // 尝试错误恢复
  const recovered = await errorRecovery.tryRecover(error);

  if (recovered) {
    errorMonitor.addBreadcrumb(`Error recovered: ${error.message}`, 'recovery', 'info');
  } else {
    // 捕获错误
    errorMonitor.captureError(error, context);
  }
}

/**
 * 错误边界 Hook（用于 React 组件）
 */
export function useErrorBoundary() {
  return {
    captureError: (error: Error, errorInfo?: any) => {
      handleError(error, { errorInfo });
    },

    withErrorBoundary: <T extends (...args: any[]) => any>(fn: T): T => {
      return ((...args: any[]) => {
        try {
          const result = fn(...args);

          // 处理 Promise 返回值
          if (result && typeof result.catch === 'function') {
            return result.catch((error: Error) => {
              handleError(error, { function: fn.name, args });
              throw error;
            });
          }

          return result;
        } catch (error) {
          handleError(error as Error, { function: fn.name, args });
          throw error;
        }
      }) as T;
    },
  };
}
