/**
 * 存储工具函数
 * 支持多端兼容的本地存储功能
 */

import Taro from '@tarojs/taro';

/**
 * 存储适配器接口
 */
interface StorageAdapter {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

/**
 * 存储项接口
 */
interface StorageItem<T = any> {
  value: T;
  timestamp: number;
  ttl?: number;
}

/**
 * 存储选项
 */
interface StorageOptions {
  prefix?: string;
  ttl?: number;
  serialize?: (value: any) => string;
  deserialize?: (value: string) => any;
}

/**
 * 获取存储适配器
 */
function getStorageAdapter(): StorageAdapter {
  // 在小程序环境中使用 Taro 的存储 API
  if (process.env.TARO_ENV !== 'h5') {
    return {
      getItem: (key: string) => {
        try {
          return Taro.getStorageSync(key);
        } catch {
          return null;
        }
      },
      setItem: (key: string, value: string) => {
        try {
          Taro.setStorageSync(key, value);
        } catch {
          // 忽略存储错误
        }
      },
      removeItem: (key: string) => {
        try {
          Taro.removeStorageSync(key);
        } catch {
          // 忽略移除错误
        }
      },
      clear: () => {
        try {
          Taro.clearStorageSync();
        } catch {
          // 忽略清除错误
        }
      },
    };
  }
  
  // 在 H5 环境中使用 localStorage
  return {
    getItem: (key: string) => {
      try {
        return localStorage.getItem(key);
      } catch {
        return null;
      }
    },
    setItem: (key: string, value: string) => {
      try {
        localStorage.setItem(key, value);
      } catch {
        // 忽略存储错误
      }
    },
    removeItem: (key: string) => {
      try {
        localStorage.removeItem(key);
      } catch {
        // 忽略移除错误
      }
    },
    clear: () => {
      try {
        localStorage.clear();
      } catch {
        // 忽略清除错误
      }
    },
  };
}

/**
 * 存储管理类
 */
class Storage {
  private adapter: StorageAdapter;
  private prefix: string;
  private defaultTTL?: number;
  private serialize: (value: any) => string;
  private deserialize: (value: string) => any;

  constructor(options: StorageOptions = {}) {
    this.adapter = getStorageAdapter();
    this.prefix = options.prefix || '';
    this.defaultTTL = options.ttl;
    this.serialize = options.serialize || JSON.stringify;
    this.deserialize = options.deserialize || JSON.parse;
  }

  /**
   * 获取完整的键名
   */
  private getKey(key: string): string {
    return this.prefix ? `${this.prefix}:${key}` : key;
  }

  /**
   * 检查存储项是否过期
   */
  private isExpired(item: StorageItem): boolean {
    if (!item.ttl) return false;
    return Date.now() - item.timestamp > item.ttl * 1000;
  }

  /**
   * 设置存储项
   */
  setItem<T>(key: string, value: T, ttl?: number): void {
    try {
      const item: StorageItem<T> = {
        value,
        timestamp: Date.now(),
        ttl: ttl || this.defaultTTL,
      };
      
      const serializedValue = this.serialize(item);
      this.adapter.setItem(this.getKey(key), serializedValue);
    } catch (error) {
      console.warn('Storage setItem error:', error);
    }
  }

  /**
   * 获取存储项
   */
  getItem<T>(key: string, defaultValue?: T): T | undefined {
    try {
      const serializedValue = this.adapter.getItem(this.getKey(key));
      if (!serializedValue) return defaultValue;
      
      const item: StorageItem<T> = this.deserialize(serializedValue);
      
      if (this.isExpired(item)) {
        this.removeItem(key);
        return defaultValue;
      }
      
      return item.value;
    } catch (error) {
      console.warn('Storage getItem error:', error);
      return defaultValue;
    }
  }

  /**
   * 移除存储项
   */
  removeItem(key: string): void {
    try {
      this.adapter.removeItem(this.getKey(key));
    } catch (error) {
      console.warn('Storage removeItem error:', error);
    }
  }

  /**
   * 检查存储项是否存在
   */
  hasItem(key: string): boolean {
    return this.getItem(key) !== undefined;
  }

  /**
   * 清除所有存储项
   */
  clear(): void {
    try {
      if (this.prefix) {
        // 如果有前缀，只清除带前缀的项
        const keys = this.getKeys();
        keys.forEach(key => this.removeItem(key));
      } else {
        this.adapter.clear();
      }
    } catch (error) {
      console.warn('Storage clear error:', error);
    }
  }

  /**
   * 获取所有键名
   */
  getKeys(): string[] {
    try {
      // 在小程序环境中，无法直接获取所有键名
      // 这里返回空数组，实际使用中可以维护一个键名列表
      if (process.env.TARO_ENV !== 'h5') {
        return [];
      }
      
      // 在 H5 环境中
      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          if (this.prefix) {
            if (key.startsWith(`${this.prefix}:`)) {
              keys.push(key.substring(this.prefix.length + 1));
            }
          } else {
            keys.push(key);
          }
        }
      }
      return keys;
    } catch (error) {
      console.warn('Storage getKeys error:', error);
      return [];
    }
  }

  /**
   * 获取存储大小（仅 H5 环境）
   */
  getSize(): number {
    try {
      if (process.env.TARO_ENV !== 'h5') {
        return 0;
      }
      
      let size = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          if (value) {
            size += key.length + value.length;
          }
        }
      }
      return size;
    } catch (error) {
      console.warn('Storage getSize error:', error);
      return 0;
    }
  }

  /**
   * 清除过期的存储项
   */
  clearExpired(): void {
    try {
      const keys = this.getKeys();
      keys.forEach(key => {
        const item = this.getItem(key);
        // getItem 方法会自动清除过期项
      });
    } catch (error) {
      console.warn('Storage clearExpired error:', error);
    }
  }
}

// 创建默认存储实例
export const storage = new Storage();

// 创建带前缀的存储实例
export const createStorage = (options: StorageOptions) => new Storage(options);

// 便捷方法
export const setItem = <T>(key: string, value: T, ttl?: number) => storage.setItem(key, value, ttl);
export const getItem = <T>(key: string, defaultValue?: T) => storage.getItem(key, defaultValue);
export const removeItem = (key: string) => storage.removeItem(key);
export const hasItem = (key: string) => storage.hasItem(key);
export const clear = () => storage.clear();
export const getKeys = () => storage.getKeys();
export const getSize = () => storage.getSize();
export const clearExpired = () => storage.clearExpired();

// 会话存储（仅在当前会话有效）
class SessionStorage {
  private storage = new Map<string, any>();

  setItem<T>(key: string, value: T): void {
    this.storage.set(key, value);
  }

  getItem<T>(key: string, defaultValue?: T): T | undefined {
    return this.storage.has(key) ? this.storage.get(key) : defaultValue;
  }

  removeItem(key: string): void {
    this.storage.delete(key);
  }

  hasItem(key: string): boolean {
    return this.storage.has(key);
  }

  clear(): void {
    this.storage.clear();
  }

  getKeys(): string[] {
    return Array.from(this.storage.keys());
  }

  getSize(): number {
    return this.storage.size;
  }
}

export const sessionStorage = new SessionStorage();

// 缓存存储（带过期时间的内存缓存）
class CacheStorage {
  private cache = new Map<string, StorageItem>();

  setItem<T>(key: string, value: T, ttl: number = 300): void {
    const item: StorageItem<T> = {
      value,
      timestamp: Date.now(),
      ttl,
    };
    this.cache.set(key, item);
  }

  getItem<T>(key: string, defaultValue?: T): T | undefined {
    const item = this.cache.get(key);
    if (!item) return defaultValue;

    if (item.ttl && Date.now() - item.timestamp > item.ttl * 1000) {
      this.cache.delete(key);
      return defaultValue;
    }

    return item.value;
  }

  removeItem(key: string): void {
    this.cache.delete(key);
  }

  hasItem(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    if (item.ttl && Date.now() - item.timestamp > item.ttl * 1000) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  clear(): void {
    this.cache.clear();
  }

  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  getSize(): number {
    return this.cache.size;
  }

  clearExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.ttl && now - item.timestamp > item.ttl * 1000) {
        this.cache.delete(key);
      }
    }
  }
}

export const cacheStorage = new CacheStorage();

// 定期清理过期缓存
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    cacheStorage.clearExpired();
  }, 60000); // 每分钟清理一次
}
