/**
 * 日期工具函数单元测试
 */

import {
  formatDate,
  parseDate,
  getRelativeTime,
  addDays,
  subDays,
  addHours,
  subHours,
  addMinutes,
  subMinutes,
  isToday,
  isYesterday,
  isTomorrow,
  isThisWeek,
  isThisMonth,
  isThisYear,
  getStartOfDay,
  getEndOfDay,
  getStartOfWeek,
  getEndOfWeek,
  getStartOfMonth,
  getEndOfMonth,
  getStartOfYear,
  getEndOfYear,
  getDaysBetween,
  getHoursBetween,
  getMinutesBetween,
  isWeekend,
  isWeekday,
  getWeekNumber,
  getQuarter,
  isLeapYear,
  getDaysInMonth,
} from '../date';

describe('Date Utils', () => {
  const testDate = new Date('2024-01-15T10:30:45.123Z');
  const testTimestamp = testDate.getTime();

  describe('formatDate', () => {
    it('formats date with YYYY-MM-DD pattern', () => {
      expect(formatDate(testDate, 'YYYY-MM-DD')).toBe('2024-01-15');
    });

    it('formats date with YYYY年MM月DD日 pattern', () => {
      expect(formatDate(testDate, 'YYYY年MM月DD日')).toBe('2024年01月15日');
    });

    it('formats date with HH:mm:ss pattern', () => {
      expect(formatDate(testDate, 'HH:mm:ss')).toBe('10:30:45');
    });

    it('formats date with full datetime pattern', () => {
      expect(formatDate(testDate, 'YYYY-MM-DD HH:mm:ss')).toBe('2024-01-15 10:30:45');
    });

    it('handles string input', () => {
      expect(formatDate('2024-01-15', 'YYYY-MM-DD')).toBe('2024-01-15');
    });

    it('handles timestamp input', () => {
      expect(formatDate(testTimestamp, 'YYYY-MM-DD')).toBe('2024-01-15');
    });

    it('returns empty string for invalid date', () => {
      expect(formatDate('invalid-date', 'YYYY-MM-DD')).toBe('');
    });
  });

  describe('parseDate', () => {
    it('parses date string correctly', () => {
      const result = parseDate('2024-01-15');
      expect(result).toBeInstanceOf(Date);
      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(0); // 0-based
      expect(result.getDate()).toBe(15);
    });

    it('parses datetime string correctly', () => {
      const result = parseDate('2024-01-15 10:30:45');
      expect(result.getHours()).toBe(10);
      expect(result.getMinutes()).toBe(30);
      expect(result.getSeconds()).toBe(45);
    });

    it('returns null for invalid date string', () => {
      expect(parseDate('invalid-date')).toBeNull();
    });

    it('returns null for empty string', () => {
      expect(parseDate('')).toBeNull();
    });
  });

  describe('getRelativeTime', () => {
    const now = new Date();

    it('returns "刚刚" for current time', () => {
      expect(getRelativeTime(now)).toBe('刚刚');
    });

    it('returns "1分钟前" for 1 minute ago', () => {
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      expect(getRelativeTime(oneMinuteAgo)).toBe('1分钟前');
    });

    it('returns "1小时前" for 1 hour ago', () => {
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      expect(getRelativeTime(oneHourAgo)).toBe('1小时前');
    });

    it('returns "1天前" for 1 day ago', () => {
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      expect(getRelativeTime(oneDayAgo)).toBe('1天前');
    });

    it('returns formatted date for dates more than 7 days ago', () => {
      const eightDaysAgo = new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000);
      const result = getRelativeTime(eightDaysAgo);
      expect(result).toMatch(/\d{4}-\d{2}-\d{2}/);
    });
  });

  describe('addDays / subDays', () => {
    it('adds days correctly', () => {
      const result = addDays(testDate, 5);
      expect(result.getDate()).toBe(20);
    });

    it('subtracts days correctly', () => {
      const result = subDays(testDate, 5);
      expect(result.getDate()).toBe(10);
    });

    it('handles month boundary when adding days', () => {
      const endOfMonth = new Date('2024-01-31');
      const result = addDays(endOfMonth, 1);
      expect(result.getMonth()).toBe(1); // February
      expect(result.getDate()).toBe(1);
    });

    it('handles month boundary when subtracting days', () => {
      const startOfMonth = new Date('2024-02-01');
      const result = subDays(startOfMonth, 1);
      expect(result.getMonth()).toBe(0); // January
      expect(result.getDate()).toBe(31);
    });
  });

  describe('addHours / subHours', () => {
    it('adds hours correctly', () => {
      const result = addHours(testDate, 5);
      expect(result.getHours()).toBe(15);
    });

    it('subtracts hours correctly', () => {
      const result = subHours(testDate, 5);
      expect(result.getHours()).toBe(5);
    });

    it('handles day boundary when adding hours', () => {
      const lateEvening = new Date('2024-01-15T23:00:00');
      const result = addHours(lateEvening, 2);
      expect(result.getDate()).toBe(16);
      expect(result.getHours()).toBe(1);
    });
  });

  describe('addMinutes / subMinutes', () => {
    it('adds minutes correctly', () => {
      const result = addMinutes(testDate, 30);
      expect(result.getMinutes()).toBe(0); // 30 + 30 = 60, so 0 minutes and +1 hour
      expect(result.getHours()).toBe(11);
    });

    it('subtracts minutes correctly', () => {
      const result = subMinutes(testDate, 15);
      expect(result.getMinutes()).toBe(15);
    });
  });

  describe('isToday / isYesterday / isTomorrow', () => {
    const today = new Date();
    const yesterday = subDays(today, 1);
    const tomorrow = addDays(today, 1);

    it('identifies today correctly', () => {
      expect(isToday(today)).toBe(true);
      expect(isToday(yesterday)).toBe(false);
      expect(isToday(tomorrow)).toBe(false);
    });

    it('identifies yesterday correctly', () => {
      expect(isYesterday(yesterday)).toBe(true);
      expect(isYesterday(today)).toBe(false);
      expect(isYesterday(tomorrow)).toBe(false);
    });

    it('identifies tomorrow correctly', () => {
      expect(isTomorrow(tomorrow)).toBe(true);
      expect(isTomorrow(today)).toBe(false);
      expect(isTomorrow(yesterday)).toBe(false);
    });
  });

  describe('isThisWeek / isThisMonth / isThisYear', () => {
    const now = new Date();
    const lastWeek = subDays(now, 7);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const lastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());

    it('identifies this week correctly', () => {
      expect(isThisWeek(now)).toBe(true);
      expect(isThisWeek(lastWeek)).toBe(false);
    });

    it('identifies this month correctly', () => {
      expect(isThisMonth(now)).toBe(true);
      expect(isThisMonth(lastMonth)).toBe(false);
    });

    it('identifies this year correctly', () => {
      expect(isThisYear(now)).toBe(true);
      expect(isThisYear(lastYear)).toBe(false);
    });
  });

  describe('getStartOfDay / getEndOfDay', () => {
    it('gets start of day correctly', () => {
      const result = getStartOfDay(testDate);
      expect(result.getHours()).toBe(0);
      expect(result.getMinutes()).toBe(0);
      expect(result.getSeconds()).toBe(0);
      expect(result.getMilliseconds()).toBe(0);
    });

    it('gets end of day correctly', () => {
      const result = getEndOfDay(testDate);
      expect(result.getHours()).toBe(23);
      expect(result.getMinutes()).toBe(59);
      expect(result.getSeconds()).toBe(59);
      expect(result.getMilliseconds()).toBe(999);
    });
  });

  describe('getDaysBetween / getHoursBetween / getMinutesBetween', () => {
    const date1 = new Date('2024-01-15');
    const date2 = new Date('2024-01-20');

    it('calculates days between dates correctly', () => {
      expect(getDaysBetween(date1, date2)).toBe(5);
      expect(getDaysBetween(date2, date1)).toBe(5);
    });

    it('calculates hours between dates correctly', () => {
      const hour1 = new Date('2024-01-15T10:00:00');
      const hour2 = new Date('2024-01-15T15:00:00');
      expect(getHoursBetween(hour1, hour2)).toBe(5);
    });

    it('calculates minutes between dates correctly', () => {
      const minute1 = new Date('2024-01-15T10:00:00');
      const minute2 = new Date('2024-01-15T10:30:00');
      expect(getMinutesBetween(minute1, minute2)).toBe(30);
    });
  });

  describe('isWeekend / isWeekday', () => {
    const saturday = new Date('2024-01-13'); // Saturday
    const sunday = new Date('2024-01-14'); // Sunday
    const monday = new Date('2024-01-15'); // Monday

    it('identifies weekend correctly', () => {
      expect(isWeekend(saturday)).toBe(true);
      expect(isWeekend(sunday)).toBe(true);
      expect(isWeekend(monday)).toBe(false);
    });

    it('identifies weekday correctly', () => {
      expect(isWeekday(monday)).toBe(true);
      expect(isWeekday(saturday)).toBe(false);
      expect(isWeekday(sunday)).toBe(false);
    });
  });

  describe('getWeekNumber', () => {
    it('gets week number correctly', () => {
      const date = new Date('2024-01-15');
      const weekNumber = getWeekNumber(date);
      expect(typeof weekNumber).toBe('number');
      expect(weekNumber).toBeGreaterThan(0);
      expect(weekNumber).toBeLessThanOrEqual(53);
    });
  });

  describe('getQuarter', () => {
    it('gets quarter correctly', () => {
      expect(getQuarter(new Date('2024-01-15'))).toBe(1);
      expect(getQuarter(new Date('2024-04-15'))).toBe(2);
      expect(getQuarter(new Date('2024-07-15'))).toBe(3);
      expect(getQuarter(new Date('2024-10-15'))).toBe(4);
    });
  });

  describe('isLeapYear', () => {
    it('identifies leap year correctly', () => {
      expect(isLeapYear(2024)).toBe(true);
      expect(isLeapYear(2023)).toBe(false);
      expect(isLeapYear(2000)).toBe(true);
      expect(isLeapYear(1900)).toBe(false);
    });
  });

  describe('getDaysInMonth', () => {
    it('gets days in month correctly', () => {
      expect(getDaysInMonth(2024, 1)).toBe(31); // January
      expect(getDaysInMonth(2024, 2)).toBe(29); // February (leap year)
      expect(getDaysInMonth(2023, 2)).toBe(28); // February (non-leap year)
      expect(getDaysInMonth(2024, 4)).toBe(30); // April
    });
  });

  describe('Edge cases', () => {
    it('handles null input gracefully', () => {
      expect(formatDate(null as any, 'YYYY-MM-DD')).toBe('');
      expect(parseDate(null as any)).toBeNull();
    });

    it('handles undefined input gracefully', () => {
      expect(formatDate(undefined as any, 'YYYY-MM-DD')).toBe('');
      expect(parseDate(undefined as any)).toBeNull();
    });

    it('handles invalid date objects', () => {
      const invalidDate = new Date('invalid');
      expect(formatDate(invalidDate, 'YYYY-MM-DD')).toBe('');
    });
  });
});
