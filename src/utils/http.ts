/**
 * 高级网络请求封装
 * 支持重试、缓存、并发控制、拦截器等高级功能
 */

import Taro from '@tarojs/taro';
import { useUserStore } from '../stores/userStore';
import { useAppStore } from '../stores/appStore';
import { useCacheStore } from '../stores/cacheStore';
import { useNotificationStore } from '../stores/notificationStore';
import { getApiBaseUrl } from '../config/env';
import { logError, NetworkError } from './error';
import { debug, warn, error as logErrorMessage } from './debug';
import { markStart, markEnd } from './performance';
import { generateUUID } from './string';

/**
 * 高级请求配置接口
 */
export interface HttpConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retry?: number;
  cache?: boolean | number; // true 使用默认缓存时间，number 为自定义缓存时间（秒）
  loading?: boolean;
  silent?: boolean; // 静默请求，不显示错误提示
  priority?: 'low' | 'normal' | 'high';
  signal?: AbortSignal;
  onProgress?: (progress: number) => void;
  transform?: (data: any) => any;
  validate?: (data: any) => boolean;
  debounce?: number; // 防抖时间（毫秒）
  throttle?: number; // 节流时间（毫秒）
}

/**
 * 响应数据接口
 */
export interface HttpResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
  requestId?: string;
}

/**
 * 请求拦截器接口
 */
export interface HttpInterceptor {
  onRequest?: (config: HttpConfig) => HttpConfig | Promise<HttpConfig>;
  onRequestError?: (error: any) => any;
  onResponse?: (response: any) => any | Promise<any>;
  onResponseError?: (error: any) => any;
}

/**
 * 上传配置接口
 */
export interface UploadConfig {
  url: string;
  filePath: string;
  name: string;
  formData?: Record<string, any>;
  headers?: Record<string, string>;
  onProgress?: (progress: number) => void;
  timeout?: number;
  retry?: number;
}

/**
 * 下载配置接口
 */
export interface DownloadConfig {
  url: string;
  headers?: Record<string, string>;
  onProgress?: (progress: number) => void;
  timeout?: number;
  retry?: number;
}

/**
 * 请求队列项
 */
interface RequestQueueItem {
  config: HttpConfig;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
  priority: 'low' | 'normal' | 'high';
  timestamp: number;
}

/**
 * 防抖/节流缓存
 */
interface ThrottleCache {
  [key: string]: {
    timer?: NodeJS.Timeout;
    lastCall?: number;
    promise?: Promise<any>;
  };
}

/**
 * 高级 HTTP 客户端类
 */
class HttpClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;
  private interceptors: HttpInterceptor[];
  private requestQueue: RequestQueueItem[];
  private activeRequests: Set<string>;
  private maxConcurrentRequests: number;
  private retryConfig: {
    times: number;
    delay: number;
    condition: (error: any) => boolean;
  };
  private cacheEnabled: boolean;
  private throttleCache: ThrottleCache;

  constructor() {
    this.baseURL = getApiBaseUrl();
    this.timeout = 10000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    this.interceptors = [];
    this.requestQueue = [];
    this.activeRequests = new Set();
    this.maxConcurrentRequests = 6;
    this.retryConfig = {
      times: 3,
      delay: 1000,
      condition: (error) => {
        // 只对网络错误和 5xx 错误重试
        return error.code >= 500 || error.code === 'NETWORK_ERROR';
      },
    };
    this.cacheEnabled = true;
    this.throttleCache = {};
  }

  /**
   * 添加请求拦截器
   */
  addInterceptor(interceptor: HttpInterceptor): void {
    this.interceptors.push(interceptor);
  }

  /**
   * 移除请求拦截器
   */
  removeInterceptor(interceptor: HttpInterceptor): void {
    const index = this.interceptors.indexOf(interceptor);
    if (index > -1) {
      this.interceptors.splice(index, 1);
    }
  }

  /**
   * 设置基础 URL
   */
  setBaseURL(baseURL: string): void {
    this.baseURL = baseURL;
  }

  /**
   * 设置默认超时时间
   */
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  /**
   * 设置默认请求头
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers };
  }

  /**
   * 设置最大并发请求数
   */
  setMaxConcurrentRequests(max: number): void {
    this.maxConcurrentRequests = max;
  }

  /**
   * 启用/禁用缓存
   */
  setCacheEnabled(enabled: boolean): void {
    this.cacheEnabled = enabled;
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    return generateUUID();
  }

  /**
   * 构建完整 URL
   */
  private buildUrl(url: string, params?: Record<string, any>): string {
    let fullUrl = url.startsWith('http') ? url : this.baseURL + url;

    if (params && Object.keys(params).length > 0) {
      const queryString = Object.entries(params)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
        .join('&');
      fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString;
    }

    return fullUrl;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(config: HttpConfig): string {
    const { url, data, params } = config;
    const key = `${url}_${JSON.stringify(data || {})}_${JSON.stringify(params || {})}`;
    return btoa(key).replace(/[^a-zA-Z0-9]/g, '');
  }

  /**
   * 生成防抖/节流键
   */
  private generateThrottleKey(config: HttpConfig): string {
    return `${config.method || 'GET'}_${config.url}_${JSON.stringify(config.data || {})}`;
  }

  /**
   * 检查缓存
   */
  private checkCache(config: HttpConfig): any {
    if (!this.cacheEnabled || !config.cache || config.method !== 'GET') {
      return null;
    }

    const cacheKey = this.generateCacheKey(config);
    return useCacheStore.getState().getApiCache(cacheKey);
  }

  /**
   * 设置缓存
   */
  private setCache(config: HttpConfig, data: any): void {
    if (!this.cacheEnabled || !config.cache || config.method !== 'GET') {
      return;
    }

    const cacheKey = this.generateCacheKey(config);
    const ttl = typeof config.cache === 'number' ? config.cache : 300; // 默认5分钟
    useCacheStore.getState().setApiCache(cacheKey, data, ttl);
  }

  /**
   * 防抖处理
   */
  private async handleDebounce<T>(config: HttpConfig): Promise<HttpResponse<T>> {
    if (!config.debounce) {
      return this.executeRequest<T>(config);
    }

    const key = this.generateThrottleKey(config);
    const cache = this.throttleCache[key] || {};

    if (cache.timer) {
      clearTimeout(cache.timer);
    }

    return new Promise((resolve, reject) => {
      cache.timer = setTimeout(async () => {
        try {
          const result = await this.executeRequest<T>(config);
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          delete this.throttleCache[key];
        }
      }, config.debounce);

      this.throttleCache[key] = cache;
    });
  }

  /**
   * 节流处理
   */
  private async handleThrottle<T>(config: HttpConfig): Promise<HttpResponse<T>> {
    if (!config.throttle) {
      return this.executeRequest<T>(config);
    }

    const key = this.generateThrottleKey(config);
    const cache = this.throttleCache[key] || {};
    const now = Date.now();

    if (cache.lastCall && now - cache.lastCall < config.throttle) {
      if (cache.promise) {
        return cache.promise;
      }
    }

    cache.lastCall = now;
    cache.promise = this.executeRequest<T>(config);
    this.throttleCache[key] = cache;

    try {
      const result = await cache.promise;
      return result;
    } finally {
      // 延迟清除缓存，确保节流期间的请求能够复用
      setTimeout(() => {
        if (this.throttleCache[key] === cache) {
          delete this.throttleCache[key];
        }
      }, config.throttle);
    }
  }

  /**
   * 请求预处理
   */
  private async preprocessRequest(config: HttpConfig): Promise<HttpConfig> {
    const { token } = useUserStore.getState();
    const { platform, version } = useAppStore.getState();

    // 合并默认配置
    const processedConfig: HttpConfig = {
      timeout: this.timeout,
      retry: this.retryConfig.times,
      cache: false,
      loading: true,
      silent: false,
      priority: 'normal',
      ...config,
    };

    // 构建请求头
    processedConfig.headers = {
      ...this.defaultHeaders,
      'X-Platform': platform,
      'X-Version': version,
      'X-Request-ID': this.generateRequestId(),
      'X-Timestamp': Date.now().toString(),
      ...processedConfig.headers,
    };

    // 添加认证信息
    if (token) {
      processedConfig.headers['Authorization'] = `Bearer ${token}`;
    }

    // 构建完整 URL
    processedConfig.url = this.buildUrl(processedConfig.url, processedConfig.params);

    // 执行请求拦截器
    let finalConfig = processedConfig;
    for (const interceptor of this.interceptors) {
      if (interceptor.onRequest) {
        try {
          finalConfig = await interceptor.onRequest(finalConfig);
        } catch (error) {
          if (interceptor.onRequestError) {
            interceptor.onRequestError(error);
          }
          throw error;
        }
      }
    }

    return finalConfig;
  }

  /**
   * 显示加载状态
   */
  private showLoading(config: HttpConfig): void {
    if (config.loading !== false) {
      try {
        Taro.showLoading({
          title: '加载中...',
          mask: true,
        });
      } catch (error) {
        debug('Failed to show loading:', error);
      }
    }
  }

  /**
   * 隐藏加载状态
   */
  private hideLoading(config: HttpConfig): void {
    if (config.loading !== false) {
      try {
        Taro.hideLoading();
      } catch (error) {
        debug('Failed to hide loading:', error);
      }
    }
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(message: string): void {
    const userStore = useUserStore.getState();
    const notificationStore = useNotificationStore.getState();

    // 清除用户信息
    userStore.logout();

    // 显示错误提示
    notificationStore.error('认证失败', message);

    // 延迟跳转到登录页
    setTimeout(() => {
      try {
        Taro.reLaunch({ url: '/pages/login/index' });
      } catch (error) {
        debug('Failed to navigate to login page:', error);
      }
    }, 1500);
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(message: string): void {
    try {
      Taro.showToast({
        title: message,
        icon: 'none',
        duration: 2000,
      });
    } catch (error) {
      debug('Failed to show error message:', error);
    }
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(statusCode: number): string {
    const errorMap: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '请求资源不存在',
      405: '请求方法不允许',
      408: '请求超时',
      413: '请求体过大',
      429: '请求过于频繁',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时',
    };

    return errorMap[statusCode] || `请求失败(${statusCode})`;
  }

  /**
   * 响应后处理
   */
  private async postprocessResponse<T>(
    response: any,
    config: HttpConfig
  ): Promise<HttpResponse<T>> {
    this.hideLoading(config);

    const { statusCode, data, header } = response;

    // 执行响应拦截器
    let processedResponse = response;
    for (const interceptor of this.interceptors) {
      if (interceptor.onResponse) {
        try {
          processedResponse = await interceptor.onResponse(processedResponse);
        } catch (error) {
          if (interceptor.onResponseError) {
            interceptor.onResponseError(error);
          }
          throw error;
        }
      }
    }

    // 检查 HTTP 状态码
    if (statusCode !== 200) {
      const error = new NetworkError(
        this.getErrorMessage(statusCode),
        statusCode,
        { url: config.url, method: config.method }
      );
      throw error;
    }

    // 检查业务状态码
    const responseData = processedResponse.data;
    if (responseData.code !== 0 && responseData.code !== 200) {
      if (responseData.code === 401) {
        // 处理认证失败
        this.handleAuthError(responseData.message || '登录已过期');
        throw new NetworkError('认证失败', 401);
      }

      const error = new NetworkError(
        responseData.message || '请求失败',
        responseData.code,
        { url: config.url, method: config.method }
      );

      if (!config.silent) {
        this.showErrorMessage(error.message);
      }

      throw error;
    }

    // 数据转换
    let finalData = responseData;
    if (config.transform) {
      finalData = config.transform(finalData);
    }

    // 数据验证
    if (config.validate && !config.validate(finalData)) {
      throw new NetworkError('响应数据验证失败', 'VALIDATION_ERROR');
    }

    // 设置缓存
    this.setCache(config, finalData);

    return finalData;
  }

  /**
   * 重试请求
   */
  private async retryRequest<T>(
    config: HttpConfig,
    error: any,
    retryCount: number = 0
  ): Promise<HttpResponse<T>> {
    const maxRetries = config.retry || this.retryConfig.times;

    if (retryCount >= maxRetries) {
      throw error;
    }

    // 检查是否满足重试条件
    if (this.retryConfig.condition && !this.retryConfig.condition(error)) {
      throw error;
    }

    // 计算延迟时间（指数退避）
    const delay = this.retryConfig.delay * Math.pow(2, retryCount);

    warn(`Request failed, retrying in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`, {
      url: config.url,
      error: error.message,
    });

    await new Promise(resolve => setTimeout(resolve, delay));

    return this.executeRequest<T>({ ...config, retry: maxRetries - retryCount - 1 });
  }

  /**
   * 并发控制
   */
  private async controlConcurrency<T>(config: HttpConfig): Promise<HttpResponse<T>> {
    return new Promise((resolve, reject) => {
      const queueItem: RequestQueueItem = {
        config,
        resolve,
        reject,
        priority: config.priority || 'normal',
        timestamp: Date.now(),
      };

      this.requestQueue.push(queueItem);
      this.processQueue();
    });
  }

  /**
   * 处理请求队列
   */
  private processQueue(): void {
    if (this.activeRequests.size >= this.maxConcurrentRequests) {
      return;
    }

    // 按优先级和时间排序
    this.requestQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
    });

    const item = this.requestQueue.shift();
    if (!item) return;

    const requestId = this.generateRequestId();
    this.activeRequests.add(requestId);

    this.executeRequest(item.config)
      .then(item.resolve)
      .catch(item.reject)
      .finally(() => {
        this.activeRequests.delete(requestId);
        this.processQueue();
      });
  }

  /**
   * 执行请求
   */
  private async executeRequest<T>(config: HttpConfig): Promise<HttpResponse<T>> {
    const requestId = this.generateRequestId();

    try {
      // 性能监控开始
      markStart(`request_${requestId}`);

      // 预处理请求
      const processedConfig = await this.preprocessRequest(config);

      // 检查缓存
      const cachedData = this.checkCache(processedConfig);
      if (cachedData) {
        debug('Request cache hit:', { url: processedConfig.url });
        return cachedData;
      }

      // 显示加载状态
      this.showLoading(processedConfig);

      // 发送请求
      const response = await Taro.request({
        url: processedConfig.url,
        method: processedConfig.method || 'GET',
        data: processedConfig.data,
        header: processedConfig.headers,
        timeout: processedConfig.timeout,
      });

      // 后处理响应
      const result = await this.postprocessResponse<T>(response, processedConfig);

      // 性能监控结束
      markEnd(`request_${requestId}`, 'api', {
        url: processedConfig.url,
        method: processedConfig.method,
        success: true,
      });

      return result;

    } catch (error) {
      // 隐藏加载状态
      this.hideLoading(config);

      // 性能监控结束
      markEnd(`request_${requestId}`, 'api', {
        url: config.url,
        method: config.method,
        success: false,
        error: error.message,
      });

      // 记录错误
      logError(error, {
        context: 'HttpClient.executeRequest',
        url: config.url,
        method: config.method,
      });

      throw error;
    }
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(config: HttpConfig): Promise<HttpResponse<T>> {
    try {
      // 防抖处理
      if (config.debounce) {
        return this.handleDebounce<T>(config);
      }

      // 节流处理
      if (config.throttle) {
        return this.handleThrottle<T>(config);
      }

      // 并发控制
      if (this.activeRequests.size >= this.maxConcurrentRequests) {
        return this.controlConcurrency<T>(config);
      }

      return await this.executeRequest<T>(config);

    } catch (error) {
      // 重试逻辑
      if (config.retry && config.retry > 0) {
        return this.retryRequest<T>(config, error);
      }

      throw error;
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(
    url: string,
    params?: Record<string, any>,
    config?: Omit<HttpConfig, 'url' | 'method' | 'params'>
  ): Promise<HttpResponse<T>> {
    return this.request<T>({
      ...config,
      url,
      method: 'GET',
      params,
    });
  }

  /**
   * POST 请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: Omit<HttpConfig, 'url' | 'method' | 'data'>
  ): Promise<HttpResponse<T>> {
    return this.request<T>({
      ...config,
      url,
      method: 'POST',
      data,
    });
  }

  /**
   * PUT 请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: Omit<HttpConfig, 'url' | 'method' | 'data'>
  ): Promise<HttpResponse<T>> {
    return this.request<T>({
      ...config,
      url,
      method: 'PUT',
      data,
    });
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(
    url: string,
    config?: Omit<HttpConfig, 'url' | 'method'>
  ): Promise<HttpResponse<T>> {
    return this.request<T>({
      ...config,
      url,
      method: 'DELETE',
    });
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: Omit<HttpConfig, 'url' | 'method' | 'data'>
  ): Promise<HttpResponse<T>> {
    return this.request<T>({
      ...config,
      url,
      method: 'PATCH',
      data,
    });
  }

  /**
   * 上传文件
   */
  async upload<T = any>(config: UploadConfig): Promise<HttpResponse<T>> {
    const requestId = this.generateRequestId();

    try {
      markStart(`upload_${requestId}`);

      const uploadTask = Taro.uploadFile({
        url: config.url.startsWith('http') ? config.url : this.baseURL + config.url,
        filePath: config.filePath,
        name: config.name,
        formData: config.formData,
        header: {
          ...this.defaultHeaders,
          ...config.headers,
        },
        timeout: config.timeout || this.timeout,
      });

      // 监听上传进度
      if (config.onProgress) {
        uploadTask.onProgressUpdate((res) => {
          const progress = (res.totalBytesSent / res.totalBytesExpectedToSend) * 100;
          config.onProgress!(Math.round(progress));
        });
      }

      const response = await uploadTask;

      markEnd(`upload_${requestId}`, 'api', {
        url: config.url,
        success: true,
        type: 'upload',
      });

      // 解析响应数据
      let data;
      try {
        data = JSON.parse(response.data);
      } catch {
        data = response.data;
      }

      return {
        code: response.statusCode === 200 ? 0 : response.statusCode,
        message: 'success',
        data,
      };

    } catch (error) {
      markEnd(`upload_${requestId}`, 'api', {
        url: config.url,
        success: false,
        error: error.message,
        type: 'upload',
      });

      logError(error, {
        context: 'HttpClient.upload',
        url: config.url,
      });

      // 重试逻辑
      if (config.retry && config.retry > 0) {
        return this.upload<T>({ ...config, retry: config.retry - 1 });
      }

      throw error;
    }
  }

  /**
   * 下载文件
   */
  async download(config: DownloadConfig): Promise<string> {
    const requestId = this.generateRequestId();

    try {
      markStart(`download_${requestId}`);

      const downloadTask = Taro.downloadFile({
        url: config.url,
        header: config.headers,
        timeout: config.timeout || this.timeout,
      });

      // 监听下载进度
      if (config.onProgress) {
        downloadTask.onProgressUpdate((res) => {
          const progress = (res.totalBytesWritten / res.totalBytesExpectedToWrite) * 100;
          config.onProgress!(Math.round(progress));
        });
      }

      const response = await downloadTask;

      markEnd(`download_${requestId}`, 'api', {
        url: config.url,
        success: true,
        type: 'download',
      });

      return response.tempFilePath;

    } catch (error) {
      markEnd(`download_${requestId}`, 'api', {
        url: config.url,
        success: false,
        error: error.message,
        type: 'download',
      });

      logError(error, {
        context: 'HttpClient.download',
        url: config.url,
      });

      // 重试逻辑
      if (config.retry && config.retry > 0) {
        return this.download({ ...config, retry: config.retry - 1 });
      }

      throw error;
    }
  }

  /**
   * 取消所有请求
   */
  cancelAllRequests(): void {
    this.requestQueue.length = 0;
    this.activeRequests.clear();
    this.throttleCache = {};
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    if (this.cacheEnabled) {
      useCacheStore.getState().clearApiCache();
    }
  }

  /**
   * 获取请求统计信息
   */
  getStats(): {
    activeRequests: number;
    queuedRequests: number;
    cacheSize: number;
  } {
    return {
      activeRequests: this.activeRequests.size,
      queuedRequests: this.requestQueue.length,
      cacheSize: useCacheStore.getState().getCacheSize().api,
    };
  }
}

// 创建默认实例
const httpClient = new HttpClient();

// 导出默认实例的方法
export const http = {
  // 配置方法
  setBaseURL: httpClient.setBaseURL.bind(httpClient),
  setTimeout: httpClient.setTimeout.bind(httpClient),
  setDefaultHeaders: httpClient.setDefaultHeaders.bind(httpClient),
  setMaxConcurrentRequests: httpClient.setMaxConcurrentRequests.bind(httpClient),
  setCacheEnabled: httpClient.setCacheEnabled.bind(httpClient),
  addInterceptor: httpClient.addInterceptor.bind(httpClient),
  removeInterceptor: httpClient.removeInterceptor.bind(httpClient),

  // 请求方法
  request: httpClient.request.bind(httpClient),
  get: httpClient.get.bind(httpClient),
  post: httpClient.post.bind(httpClient),
  put: httpClient.put.bind(httpClient),
  delete: httpClient.delete.bind(httpClient),
  patch: httpClient.patch.bind(httpClient),

  // 文件操作
  upload: httpClient.upload.bind(httpClient),
  download: httpClient.download.bind(httpClient),

  // 工具方法
  cancelAllRequests: httpClient.cancelAllRequests.bind(httpClient),
  clearCache: httpClient.clearCache.bind(httpClient),
  getStats: httpClient.getStats.bind(httpClient),
};

// 导出类型
export { HttpClient };

// 导出默认实例
export default http;
