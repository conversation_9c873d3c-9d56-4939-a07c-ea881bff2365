/**
 * URL 工具函数
 * 支持多端兼容的 URL 处理功能
 */

/**
 * 解析后的 URL 接口
 */
interface ParsedUrl {
  protocol: string;
  host: string;
  hostname: string;
  port: string;
  pathname: string;
  search: string;
  hash: string;
  params: Record<string, string>;
}

/**
 * 构建 URL 选项接口
 */
interface BuildUrlOptions {
  baseUrl?: string;
  path?: string;
  params?: Record<string, any>;
  hash?: string;
}

/**
 * 解析 URL
 * @param url URL 字符串
 * @returns 解析后的 URL 对象
 */
export function parseUrl(url: string): ParsedUrl {
  try {
    const urlObj = new URL(url);
    const params: Record<string, string> = {};
    
    // 解析查询参数
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });
    
    return {
      protocol: urlObj.protocol,
      host: urlObj.host,
      hostname: urlObj.hostname,
      port: urlObj.port,
      pathname: urlObj.pathname,
      search: urlObj.search,
      hash: urlObj.hash,
      params,
    };
  } catch (error) {
    // 如果 URL 构造函数不可用或解析失败，使用正则表达式解析
    const match = url.match(/^(https?:)\/\/(([^:/?#]*)(?::([0-9]+))?)([^?#]*)(\?[^#]*)?(#.*)?$/);
    
    if (!match) {
      return {
        protocol: '',
        host: '',
        hostname: '',
        port: '',
        pathname: '',
        search: '',
        hash: '',
        params: {},
      };
    }
    
    const [, protocol, host, hostname, port = '', pathname = '', search = '', hash = ''] = match;
    const params = parseQueryString(search);
    
    return {
      protocol,
      host,
      hostname,
      port,
      pathname,
      search,
      hash,
      params,
    };
  }
}

/**
 * 构建 URL
 * @param options 构建选项
 * @returns 构建后的 URL
 */
export function buildUrl(options: BuildUrlOptions): string {
  const { baseUrl = '', path = '', params = {}, hash = '' } = options;
  
  let url = baseUrl;
  
  // 添加路径
  if (path) {
    if (url && !url.endsWith('/') && !path.startsWith('/')) {
      url += '/';
    }
    url += path;
  }
  
  // 添加查询参数
  const queryString = buildQueryString(params);
  if (queryString) {
    url += (url.includes('?') ? '&' : '?') + queryString;
  }
  
  // 添加哈希
  if (hash) {
    url += (hash.startsWith('#') ? '' : '#') + hash;
  }
  
  return url;
}

/**
 * 解析查询字符串
 * @param queryString 查询字符串
 * @returns 参数对象
 */
export function parseQueryString(queryString: string): Record<string, string> {
  const params: Record<string, string> = {};
  
  if (!queryString) return params;
  
  // 移除开头的 ?
  const cleanQuery = queryString.startsWith('?') ? queryString.slice(1) : queryString;
  
  if (!cleanQuery) return params;
  
  cleanQuery.split('&').forEach(pair => {
    const [key, value = ''] = pair.split('=');
    if (key) {
      params[decodeURIComponent(key)] = decodeURIComponent(value);
    }
  });
  
  return params;
}

/**
 * 构建查询字符串
 * @param params 参数对象
 * @returns 查询字符串
 */
export function buildQueryString(params: Record<string, any>): string {
  const pairs: string[] = [];
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`);
    }
  });
  
  return pairs.join('&');
}

/**
 * 添加查询参数到 URL
 * @param url 原 URL
 * @param params 要添加的参数
 * @returns 新的 URL
 */
export function addQueryParams(url: string, params: Record<string, any>): string {
  if (!params || Object.keys(params).length === 0) {
    return url;
  }
  
  const queryString = buildQueryString(params);
  if (!queryString) return url;
  
  return url + (url.includes('?') ? '&' : '?') + queryString;
}

/**
 * 移除 URL 中的查询参数
 * @param url 原 URL
 * @param keys 要移除的参数键名数组
 * @returns 新的 URL
 */
export function removeQueryParams(url: string, keys: string[]): string {
  const parsed = parseUrl(url);
  
  keys.forEach(key => {
    delete parsed.params[key];
  });
  
  return buildUrl({
    baseUrl: `${parsed.protocol}//${parsed.host}${parsed.pathname}`,
    params: parsed.params,
    hash: parsed.hash.replace('#', ''),
  });
}

/**
 * 获取 URL 中指定的查询参数值
 * @param url URL 字符串
 * @param key 参数键名
 * @returns 参数值
 */
export function getQueryParam(url: string, key: string): string | null {
  const parsed = parseUrl(url);
  return parsed.params[key] || null;
}

/**
 * 判断是否为绝对 URL
 * @param url URL 字符串
 * @returns 是否为绝对 URL
 */
export function isAbsoluteUrl(url: string): boolean {
  return /^https?:\/\//.test(url);
}

/**
 * 判断是否为相对 URL
 * @param url URL 字符串
 * @returns 是否为相对 URL
 */
export function isRelativeUrl(url: string): boolean {
  return !isAbsoluteUrl(url);
}

/**
 * 将相对 URL 转换为绝对 URL
 * @param relativeUrl 相对 URL
 * @param baseUrl 基础 URL
 * @returns 绝对 URL
 */
export function resolveUrl(relativeUrl: string, baseUrl: string): string {
  if (isAbsoluteUrl(relativeUrl)) {
    return relativeUrl;
  }
  
  try {
    return new URL(relativeUrl, baseUrl).href;
  } catch (error) {
    // 如果 URL 构造函数不可用，使用简单的字符串拼接
    if (baseUrl.endsWith('/') && relativeUrl.startsWith('/')) {
      return baseUrl + relativeUrl.slice(1);
    } else if (!baseUrl.endsWith('/') && !relativeUrl.startsWith('/')) {
      return baseUrl + '/' + relativeUrl;
    } else {
      return baseUrl + relativeUrl;
    }
  }
}

/**
 * 获取 URL 的域名
 * @param url URL 字符串
 * @returns 域名
 */
export function getDomain(url: string): string {
  const parsed = parseUrl(url);
  return parsed.hostname;
}

/**
 * 获取 URL 的根域名
 * @param url URL 字符串
 * @returns 根域名
 */
export function getRootDomain(url: string): string {
  const domain = getDomain(url);
  const parts = domain.split('.');
  
  if (parts.length <= 2) {
    return domain;
  }
  
  // 简单处理，返回最后两个部分
  return parts.slice(-2).join('.');
}

/**
 * 判断两个 URL 是否同域
 * @param url1 URL 1
 * @param url2 URL 2
 * @returns 是否同域
 */
export function isSameDomain(url1: string, url2: string): boolean {
  return getDomain(url1) === getDomain(url2);
}

/**
 * 获取文件名从 URL
 * @param url URL 字符串
 * @returns 文件名
 */
export function getFilenameFromUrl(url: string): string {
  const parsed = parseUrl(url);
  const pathname = parsed.pathname;
  const lastSlashIndex = pathname.lastIndexOf('/');
  
  if (lastSlashIndex === -1) {
    return pathname;
  }
  
  return pathname.slice(lastSlashIndex + 1);
}

/**
 * 获取文件扩展名从 URL
 * @param url URL 字符串
 * @returns 文件扩展名
 */
export function getExtensionFromUrl(url: string): string {
  const filename = getFilenameFromUrl(url);
  const lastDotIndex = filename.lastIndexOf('.');
  
  if (lastDotIndex === -1) {
    return '';
  }
  
  return filename.slice(lastDotIndex + 1).toLowerCase();
}

/**
 * 清理 URL（移除多余的斜杠等）
 * @param url URL 字符串
 * @returns 清理后的 URL
 */
export function cleanUrl(url: string): string {
  return url
    .replace(/([^:]\/)\/+/g, '$1') // 移除多余的斜杠
    .replace(/\/+$/, ''); // 移除末尾的斜杠
}

/**
 * 编码 URL 组件
 * @param str 字符串
 * @returns 编码后的字符串
 */
export function encodeUrlComponent(str: string): string {
  return encodeURIComponent(str);
}

/**
 * 解码 URL 组件
 * @param str 编码的字符串
 * @returns 解码后的字符串
 */
export function decodeUrlComponent(str: string): string {
  try {
    return decodeURIComponent(str);
  } catch (error) {
    return str;
  }
}

/**
 * 生成带时间戳的 URL（防止缓存）
 * @param url 原 URL
 * @param paramName 时间戳参数名
 * @returns 带时间戳的 URL
 */
export function addTimestamp(url: string, paramName: string = '_t'): string {
  return addQueryParams(url, { [paramName]: Date.now() });
}

/**
 * 验证 URL 格式
 * @param url URL 字符串
 * @returns 是否为有效的 URL
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (error) {
    // 如果 URL 构造函数不可用，使用正则表达式验证
    const urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
    return urlRegex.test(url);
  }
}

/**
 * 获取当前页面的 URL（仅 H5 环境）
 * @returns 当前页面 URL
 */
export function getCurrentUrl(): string {
  if (typeof window !== 'undefined' && window.location) {
    return window.location.href;
  }
  return '';
}

/**
 * 获取当前页面的查询参数（仅 H5 环境）
 * @returns 查询参数对象
 */
export function getCurrentQueryParams(): Record<string, string> {
  if (typeof window !== 'undefined' && window.location) {
    return parseQueryString(window.location.search);
  }
  return {};
}
