/**
 * 验证工具函数
 * 支持多端兼容的数据验证功能
 */

/**
 * 验证手机号
 * @param phone 手机号
 * @returns 是否有效
 */
export function isValidPhone(phone: string): boolean {
  if (!phone) return false;
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}

/**
 * 验证邮箱
 * @param email 邮箱地址
 * @returns 是否有效
 */
export function isValidEmail(email: string): boolean {
  if (!email) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证身份证号
 * @param idCard 身份证号
 * @returns 是否有效
 */
export function isValidIdCard(idCard: string): boolean {
  if (!idCard) return false;
  
  // 18位身份证号验证
  if (idCard.length === 18) {
    const regex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    if (!regex.test(idCard)) return false;
    
    // 校验码验证
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * weights[i];
    }
    
    const checkCode = checkCodes[sum % 11];
    return idCard[17].toUpperCase() === checkCode;
  }
  
  // 15位身份证号验证
  if (idCard.length === 15) {
    const regex = /^[1-9]\d{5}\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}$/;
    return regex.test(idCard);
  }
  
  return false;
}

/**
 * 验证密码强度
 * @param password 密码
 * @param options 验证选项
 * @returns 验证结果
 */
export function validatePassword(
  password: string,
  options: {
    minLength?: number;
    maxLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSymbols?: boolean;
  } = {}
): {
  valid: boolean;
  strength: 'weak' | 'medium' | 'strong';
  errors: string[];
} {
  const {
    minLength = 8,
    maxLength = 20,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSymbols = false,
  } = options;
  
  const errors: string[] = [];
  let score = 0;
  
  if (!password) {
    errors.push('密码不能为空');
    return { valid: false, strength: 'weak', errors };
  }
  
  if (password.length < minLength) {
    errors.push(`密码长度不能少于${minLength}位`);
  } else {
    score += 1;
  }
  
  if (password.length > maxLength) {
    errors.push(`密码长度不能超过${maxLength}位`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  } else if (/[A-Z]/.test(password)) {
    score += 1;
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  } else if (/[a-z]/.test(password)) {
    score += 1;
  }
  
  if (requireNumbers && !/\d/.test(password)) {
    errors.push('密码必须包含数字');
  } else if (/\d/.test(password)) {
    score += 1;
  }
  
  if (requireSymbols && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含特殊字符');
  } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1;
  }
  
  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  if (score >= 4) {
    strength = 'strong';
  } else if (score >= 2) {
    strength = 'medium';
  }
  
  return {
    valid: errors.length === 0,
    strength,
    errors,
  };
}

/**
 * 验证URL
 * @param url URL地址
 * @returns 是否有效
 */
export function isValidUrl(url: string): boolean {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 验证IP地址
 * @param ip IP地址
 * @returns 是否有效
 */
export function isValidIP(ip: string): boolean {
  if (!ip) return false;
  const ipv4Regex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * 验证数字
 * @param value 值
 * @param options 验证选项
 * @returns 是否有效
 */
export function isValidNumber(
  value: any,
  options: {
    min?: number;
    max?: number;
    integer?: boolean;
    positive?: boolean;
  } = {}
): boolean {
  const { min, max, integer = false, positive = false } = options;
  
  const num = Number(value);
  if (isNaN(num)) return false;
  
  if (integer && !Number.isInteger(num)) return false;
  if (positive && num <= 0) return false;
  if (typeof min === 'number' && num < min) return false;
  if (typeof max === 'number' && num > max) return false;
  
  return true;
}

/**
 * 验证字符串长度
 * @param str 字符串
 * @param options 验证选项
 * @returns 是否有效
 */
export function isValidLength(
  str: string,
  options: {
    min?: number;
    max?: number;
    exact?: number;
  } = {}
): boolean {
  if (typeof str !== 'string') return false;
  
  const { min, max, exact } = options;
  const length = str.length;
  
  if (typeof exact === 'number') return length === exact;
  if (typeof min === 'number' && length < min) return false;
  if (typeof max === 'number' && length > max) return false;
  
  return true;
}

/**
 * 验证正则表达式
 * @param value 值
 * @param pattern 正则表达式
 * @returns 是否匹配
 */
export function isValidPattern(value: string, pattern: RegExp): boolean {
  if (typeof value !== 'string') return false;
  return pattern.test(value);
}

/**
 * 验证银行卡号
 * @param cardNumber 银行卡号
 * @returns 是否有效
 */
export function isValidBankCard(cardNumber: string): boolean {
  if (!cardNumber) return false;
  
  // 移除空格和连字符
  const cleanNumber = cardNumber.replace(/[\s-]/g, '');
  
  // 检查是否只包含数字
  if (!/^\d+$/.test(cleanNumber)) return false;
  
  // 检查长度（一般为13-19位）
  if (cleanNumber.length < 13 || cleanNumber.length > 19) return false;
  
  // Luhn算法验证
  let sum = 0;
  let isEven = false;
  
  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber[i]);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
}

/**
 * 验证中文姓名
 * @param name 姓名
 * @returns 是否有效
 */
export function isValidChineseName(name: string): boolean {
  if (!name) return false;
  const chineseNameRegex = /^[\u4e00-\u9fa5]{2,4}$/;
  return chineseNameRegex.test(name);
}

/**
 * 验证车牌号
 * @param plateNumber 车牌号
 * @returns 是否有效
 */
export function isValidPlateNumber(plateNumber: string): boolean {
  if (!plateNumber) return false;
  
  // 普通车牌号
  const normalPlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$/;
  
  // 新能源车牌号
  const newEnergyPlateRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9DF]$/;
  
  return normalPlateRegex.test(plateNumber) || newEnergyPlateRegex.test(plateNumber);
}

/**
 * 通用验证函数
 * @param value 值
 * @param rules 验证规则
 * @returns 验证结果
 */
export function validate(
  value: any,
  rules: Array<{
    type: 'required' | 'email' | 'phone' | 'url' | 'number' | 'length' | 'pattern' | 'custom';
    message?: string;
    options?: any;
    validator?: (value: any) => boolean;
  }>
): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  for (const rule of rules) {
    let isValid = true;
    let defaultMessage = '';
    
    switch (rule.type) {
      case 'required':
        isValid = value !== null && value !== undefined && value !== '';
        defaultMessage = '此字段为必填项';
        break;
        
      case 'email':
        isValid = !value || isValidEmail(value);
        defaultMessage = '请输入有效的邮箱地址';
        break;
        
      case 'phone':
        isValid = !value || isValidPhone(value);
        defaultMessage = '请输入有效的手机号';
        break;
        
      case 'url':
        isValid = !value || isValidUrl(value);
        defaultMessage = '请输入有效的URL地址';
        break;
        
      case 'number':
        isValid = !value || isValidNumber(value, rule.options);
        defaultMessage = '请输入有效的数字';
        break;
        
      case 'length':
        isValid = !value || isValidLength(value, rule.options);
        defaultMessage = '字符长度不符合要求';
        break;
        
      case 'pattern':
        isValid = !value || isValidPattern(value, rule.options);
        defaultMessage = '格式不正确';
        break;
        
      case 'custom':
        isValid = !value || (rule.validator ? rule.validator(value) : true);
        defaultMessage = '验证失败';
        break;
    }
    
    if (!isValid) {
      errors.push(rule.message || defaultMessage);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
