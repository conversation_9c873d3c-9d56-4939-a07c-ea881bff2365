/**
 * 数组工具函数
 * 支持多端兼容的数组处理功能
 */

/**
 * 数组去重
 * @param arr 原数组
 * @param key 对象数组的去重键名
 * @returns 去重后的数组
 */
export function unique<T>(arr: T[], key?: keyof T): T[] {
  if (!Array.isArray(arr)) return [];
  
  if (key) {
    const seen = new Set();
    return arr.filter(item => {
      const value = item[key];
      if (seen.has(value)) {
        return false;
      }
      seen.add(value);
      return true;
    });
  }
  
  return [...new Set(arr)];
}

/**
 * 数组分组
 * @param arr 原数组
 * @param key 分组键名或分组函数
 * @returns 分组后的对象
 */
export function groupBy<T>(
  arr: T[],
  key: keyof T | ((item: T) => string | number)
): Record<string, T[]> {
  if (!Array.isArray(arr)) return {};
  
  return arr.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key];
    const groupKeyStr = String(groupKey);
    
    if (!groups[groupKeyStr]) {
      groups[groupKeyStr] = [];
    }
    groups[groupKeyStr].push(item);
    
    return groups;
  }, {} as Record<string, T[]>);
}

/**
 * 数组排序
 * @param arr 原数组
 * @param key 排序键名或排序函数
 * @param order 排序顺序，'asc' 或 'desc'
 * @returns 排序后的新数组
 */
export function sortBy<T>(
  arr: T[],
  key: keyof T | ((item: T) => any),
  order: 'asc' | 'desc' = 'asc'
): T[] {
  if (!Array.isArray(arr)) return [];
  
  const sorted = [...arr].sort((a, b) => {
    const aValue = typeof key === 'function' ? key(a) : a[key];
    const bValue = typeof key === 'function' ? key(b) : b[key];
    
    if (aValue < bValue) return order === 'asc' ? -1 : 1;
    if (aValue > bValue) return order === 'asc' ? 1 : -1;
    return 0;
  });
  
  return sorted;
}

/**
 * 数组分块
 * @param arr 原数组
 * @param size 每块的大小
 * @returns 分块后的二维数组
 */
export function chunk<T>(arr: T[], size: number): T[][] {
  if (!Array.isArray(arr) || size <= 0) return [];
  
  const chunks: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  
  return chunks;
}

/**
 * 数组扁平化
 * @param arr 原数组
 * @param depth 扁平化深度，默认为 1
 * @returns 扁平化后的数组
 */
export function flatten<T>(arr: any[], depth: number = 1): T[] {
  if (!Array.isArray(arr)) return [];
  
  return depth > 0
    ? arr.reduce((acc, val) => 
        acc.concat(Array.isArray(val) ? flatten(val, depth - 1) : val), [])
    : arr.slice();
}

/**
 * 深度扁平化数组
 * @param arr 原数组
 * @returns 完全扁平化后的数组
 */
export function flattenDeep<T>(arr: any[]): T[] {
  if (!Array.isArray(arr)) return [];
  
  return arr.reduce((acc, val) => 
    acc.concat(Array.isArray(val) ? flattenDeep(val) : val), []);
}

/**
 * 数组交集
 * @param arr1 数组1
 * @param arr2 数组2
 * @returns 交集数组
 */
export function intersection<T>(arr1: T[], arr2: T[]): T[] {
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return [];
  
  const set2 = new Set(arr2);
  return arr1.filter(item => set2.has(item));
}

/**
 * 数组并集
 * @param arr1 数组1
 * @param arr2 数组2
 * @returns 并集数组
 */
export function union<T>(arr1: T[], arr2: T[]): T[] {
  if (!Array.isArray(arr1)) return Array.isArray(arr2) ? [...arr2] : [];
  if (!Array.isArray(arr2)) return [...arr1];
  
  return unique([...arr1, ...arr2]);
}

/**
 * 数组差集
 * @param arr1 数组1
 * @param arr2 数组2
 * @returns 差集数组（arr1 中有但 arr2 中没有的元素）
 */
export function difference<T>(arr1: T[], arr2: T[]): T[] {
  if (!Array.isArray(arr1)) return [];
  if (!Array.isArray(arr2)) return [...arr1];
  
  const set2 = new Set(arr2);
  return arr1.filter(item => !set2.has(item));
}

/**
 * 数组随机打乱
 * @param arr 原数组
 * @returns 打乱后的新数组
 */
export function shuffle<T>(arr: T[]): T[] {
  if (!Array.isArray(arr)) return [];
  
  const shuffled = [...arr];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  
  return shuffled;
}

/**
 * 数组随机取样
 * @param arr 原数组
 * @param count 取样数量
 * @returns 随机取样的数组
 */
export function sample<T>(arr: T[], count: number = 1): T[] {
  if (!Array.isArray(arr) || count <= 0) return [];
  
  const shuffled = shuffle(arr);
  return shuffled.slice(0, Math.min(count, arr.length));
}

/**
 * 数组求和
 * @param arr 数字数组
 * @returns 总和
 */
export function sum(arr: number[]): number {
  if (!Array.isArray(arr)) return 0;
  return arr.reduce((total, num) => total + (typeof num === 'number' ? num : 0), 0);
}

/**
 * 数组平均值
 * @param arr 数字数组
 * @returns 平均值
 */
export function average(arr: number[]): number {
  if (!Array.isArray(arr) || arr.length === 0) return 0;
  return sum(arr) / arr.length;
}

/**
 * 数组最大值
 * @param arr 数字数组
 * @returns 最大值
 */
export function max(arr: number[]): number {
  if (!Array.isArray(arr) || arr.length === 0) return 0;
  return Math.max(...arr.filter(num => typeof num === 'number'));
}

/**
 * 数组最小值
 * @param arr 数字数组
 * @returns 最小值
 */
export function min(arr: number[]): number {
  if (!Array.isArray(arr) || arr.length === 0) return 0;
  return Math.min(...arr.filter(num => typeof num === 'number'));
}

/**
 * 数组分页
 * @param arr 原数组
 * @param page 页码（从1开始）
 * @param pageSize 每页大小
 * @returns 分页结果
 */
export function paginate<T>(
  arr: T[],
  page: number,
  pageSize: number
): {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
} {
  if (!Array.isArray(arr)) {
    return {
      data: [],
      total: 0,
      page: 1,
      pageSize,
      totalPages: 0,
    };
  }
  
  const total = arr.length;
  const totalPages = Math.ceil(total / pageSize);
  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const data = arr.slice(startIndex, endIndex);
  
  return {
    data,
    total,
    page: currentPage,
    pageSize,
    totalPages,
  };
}

/**
 * 数组转树形结构
 * @param arr 原数组
 * @param idKey ID键名
 * @param parentIdKey 父ID键名
 * @param childrenKey 子节点键名
 * @returns 树形结构数组
 */
export function arrayToTree<T extends Record<string, any>>(
  arr: T[],
  idKey: string = 'id',
  parentIdKey: string = 'parentId',
  childrenKey: string = 'children'
): T[] {
  if (!Array.isArray(arr)) return [];
  
  const map = new Map<any, T>();
  const roots: T[] = [];
  
  // 创建映射
  arr.forEach(item => {
    map.set(item[idKey], { ...item, [childrenKey]: [] });
  });
  
  // 构建树形结构
  arr.forEach(item => {
    const node = map.get(item[idKey]);
    const parentId = item[parentIdKey];
    
    if (parentId && map.has(parentId)) {
      const parent = map.get(parentId);
      parent[childrenKey].push(node);
    } else {
      roots.push(node);
    }
  });
  
  return roots;
}

/**
 * 树形结构转数组
 * @param tree 树形结构数组
 * @param childrenKey 子节点键名
 * @returns 扁平化数组
 */
export function treeToArray<T extends Record<string, any>>(
  tree: T[],
  childrenKey: string = 'children'
): T[] {
  if (!Array.isArray(tree)) return [];
  
  const result: T[] = [];
  
  function traverse(nodes: T[]) {
    nodes.forEach(node => {
      const { [childrenKey]: children, ...rest } = node;
      result.push(rest as T);
      
      if (Array.isArray(children) && children.length > 0) {
        traverse(children);
      }
    });
  }
  
  traverse(tree);
  return result;
}

/**
 * 检查数组是否为空
 * @param arr 数组
 * @returns 是否为空
 */
export function isEmpty<T>(arr: T[]): boolean {
  return !Array.isArray(arr) || arr.length === 0;
}

/**
 * 检查数组是否不为空
 * @param arr 数组
 * @returns 是否不为空
 */
export function isNotEmpty<T>(arr: T[]): boolean {
  return Array.isArray(arr) && arr.length > 0;
}
