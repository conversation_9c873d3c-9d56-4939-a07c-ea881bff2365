/**
 * 日期工具函数
 * 支持多端兼容的日期处理功能
 */

/**
 * 格式化日期
 * @param date 日期对象或时间戳
 * @param format 格式字符串，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: Date | number | string,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }

  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hour = d.getHours();
  const minute = d.getMinutes();
  const second = d.getSeconds();
  const millisecond = d.getMilliseconds();

  const formatMap: Record<string, string> = {
    'YYYY': year.toString(),
    'YY': year.toString().slice(-2),
    'MM': month.toString().padStart(2, '0'),
    'M': month.toString(),
    'DD': day.toString().padStart(2, '0'),
    'D': day.toString(),
    'HH': hour.toString().padStart(2, '0'),
    'H': hour.toString(),
    'mm': minute.toString().padStart(2, '0'),
    'm': minute.toString(),
    'ss': second.toString().padStart(2, '0'),
    's': second.toString(),
    'SSS': millisecond.toString().padStart(3, '0'),
  };

  return format.replace(/YYYY|YY|MM|M|DD|D|HH|H|mm|m|ss|s|SSS/g, (match) => {
    return formatMap[match] || match;
  });
}

/**
 * 获取相对时间描述
 * @param date 日期对象或时间戳
 * @param baseDate 基准日期，默认为当前时间
 * @returns 相对时间描述，如 '刚刚'、'5分钟前'、'2小时前'
 */
export function getRelativeTime(
  date: Date | number | string,
  baseDate: Date | number | string = new Date()
): string {
  const targetDate = new Date(date);
  const base = new Date(baseDate);
  
  if (isNaN(targetDate.getTime()) || isNaN(base.getTime())) {
    return '';
  }

  const diff = base.getTime() - targetDate.getTime();
  const absDiff = Math.abs(diff);
  const isFuture = diff < 0;

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  const year = 365 * day;

  if (absDiff < minute) {
    return '刚刚';
  } else if (absDiff < hour) {
    const minutes = Math.floor(absDiff / minute);
    return isFuture ? `${minutes}分钟后` : `${minutes}分钟前`;
  } else if (absDiff < day) {
    const hours = Math.floor(absDiff / hour);
    return isFuture ? `${hours}小时后` : `${hours}小时前`;
  } else if (absDiff < week) {
    const days = Math.floor(absDiff / day);
    return isFuture ? `${days}天后` : `${days}天前`;
  } else if (absDiff < month) {
    const weeks = Math.floor(absDiff / week);
    return isFuture ? `${weeks}周后` : `${weeks}周前`;
  } else if (absDiff < year) {
    const months = Math.floor(absDiff / month);
    return isFuture ? `${months}个月后` : `${months}个月前`;
  } else {
    const years = Math.floor(absDiff / year);
    return isFuture ? `${years}年后` : `${years}年前`;
  }
}

/**
 * 判断是否为今天
 * @param date 日期对象或时间戳
 * @returns 是否为今天
 */
export function isToday(date: Date | number | string): boolean {
  const targetDate = new Date(date);
  const today = new Date();
  
  return (
    targetDate.getFullYear() === today.getFullYear() &&
    targetDate.getMonth() === today.getMonth() &&
    targetDate.getDate() === today.getDate()
  );
}

/**
 * 判断是否为昨天
 * @param date 日期对象或时间戳
 * @returns 是否为昨天
 */
export function isYesterday(date: Date | number | string): boolean {
  const targetDate = new Date(date);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  return (
    targetDate.getFullYear() === yesterday.getFullYear() &&
    targetDate.getMonth() === yesterday.getMonth() &&
    targetDate.getDate() === yesterday.getDate()
  );
}

/**
 * 判断是否为明天
 * @param date 日期对象或时间戳
 * @returns 是否为明天
 */
export function isTomorrow(date: Date | number | string): boolean {
  const targetDate = new Date(date);
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  return (
    targetDate.getFullYear() === tomorrow.getFullYear() &&
    targetDate.getMonth() === tomorrow.getMonth() &&
    targetDate.getDate() === tomorrow.getDate()
  );
}

/**
 * 判断是否为本周
 * @param date 日期对象或时间戳
 * @param startOfWeek 一周的开始，0为周日，1为周一，默认为1
 * @returns 是否为本周
 */
export function isThisWeek(date: Date | number | string, startOfWeek: number = 1): boolean {
  const targetDate = new Date(date);
  const today = new Date();
  
  // 获取本周的开始和结束日期
  const dayOfWeek = today.getDay();
  const diff = dayOfWeek - startOfWeek;
  const weekStart = new Date(today);
  weekStart.setDate(today.getDate() - (diff >= 0 ? diff : diff + 7));
  weekStart.setHours(0, 0, 0, 0);
  
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);
  weekEnd.setHours(23, 59, 59, 999);
  
  return targetDate >= weekStart && targetDate <= weekEnd;
}

/**
 * 判断是否为本月
 * @param date 日期对象或时间戳
 * @returns 是否为本月
 */
export function isThisMonth(date: Date | number | string): boolean {
  const targetDate = new Date(date);
  const today = new Date();
  
  return (
    targetDate.getFullYear() === today.getFullYear() &&
    targetDate.getMonth() === today.getMonth()
  );
}

/**
 * 判断是否为本年
 * @param date 日期对象或时间戳
 * @returns 是否为本年
 */
export function isThisYear(date: Date | number | string): boolean {
  const targetDate = new Date(date);
  const today = new Date();
  
  return targetDate.getFullYear() === today.getFullYear();
}

/**
 * 获取日期范围内的所有日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期数组
 */
export function getDateRange(
  startDate: Date | number | string,
  endDate: Date | number | string
): Date[] {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const dates: Date[] = [];
  
  if (start > end) {
    return dates;
  }
  
  const current = new Date(start);
  while (current <= end) {
    dates.push(new Date(current));
    current.setDate(current.getDate() + 1);
  }
  
  return dates;
}

/**
 * 获取月份的天数
 * @param year 年份
 * @param month 月份（0-11）
 * @returns 天数
 */
export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate();
}

/**
 * 获取月份的第一天是星期几
 * @param year 年份
 * @param month 月份（0-11）
 * @returns 星期几（0-6，0为周日）
 */
export function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month, 1).getDay();
}

/**
 * 判断是否为闰年
 * @param year 年份
 * @returns 是否为闰年
 */
export function isLeapYear(year: number): boolean {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
}

/**
 * 添加时间
 * @param date 日期对象或时间戳
 * @param amount 数量
 * @param unit 单位
 * @returns 新的日期对象
 */
export function addTime(
  date: Date | number | string,
  amount: number,
  unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond'
): Date {
  const result = new Date(date);
  
  switch (unit) {
    case 'year':
      result.setFullYear(result.getFullYear() + amount);
      break;
    case 'month':
      result.setMonth(result.getMonth() + amount);
      break;
    case 'day':
      result.setDate(result.getDate() + amount);
      break;
    case 'hour':
      result.setHours(result.getHours() + amount);
      break;
    case 'minute':
      result.setMinutes(result.getMinutes() + amount);
      break;
    case 'second':
      result.setSeconds(result.getSeconds() + amount);
      break;
    case 'millisecond':
      result.setMilliseconds(result.getMilliseconds() + amount);
      break;
  }
  
  return result;
}

/**
 * 减去时间
 * @param date 日期对象或时间戳
 * @param amount 数量
 * @param unit 单位
 * @returns 新的日期对象
 */
export function subtractTime(
  date: Date | number | string,
  amount: number,
  unit: 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond'
): Date {
  return addTime(date, -amount, unit);
}
