/**
 * 性能监控工具函数
 * 支持多端兼容的性能监控功能
 */

import { storage } from './storage';

/**
 * 性能数据接口
 */
interface PerformanceData {
  type: 'page' | 'api' | 'component' | 'custom';
  name: string;
  duration: number;
  timestamp: number;
  platform: Platform;
  extra?: Record<string, any>;
}

/**
 * 性能标记接口
 */
interface PerformanceMark {
  name: string;
  startTime: number;
  duration?: number;
}

/**
 * 内存信息接口
 */
interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

/**
 * FPS 信息接口
 */
interface FPSInfo {
  current: number;
  average: number;
  min: number;
  max: number;
}

/**
 * 性能标记存储
 */
const performanceMarks = new Map<string, PerformanceMark>();

/**
 * 性能数据存储
 */
const performanceData: PerformanceData[] = [];
const MAX_PERFORMANCE_DATA = 1000;

/**
 * 性能监听器
 */
const performanceListeners: Array<(data: PerformanceData) => void> = [];

/**
 * 开始性能标记
 * @param name 标记名称
 */
export function markStart(name: string): void {
  const startTime = Date.now();
  performanceMarks.set(name, {
    name,
    startTime,
  });
}

/**
 * 结束性能标记
 * @param name 标记名称
 * @param type 性能类型
 * @param extra 额外信息
 * @returns 持续时间
 */
export function markEnd(
  name: string,
  type: PerformanceData['type'] = 'custom',
  extra?: Record<string, any>
): number {
  const endTime = Date.now();
  const mark = performanceMarks.get(name);

  if (!mark) {
    console.warn(`Performance mark "${name}" not found`);
    return 0;
  }

  const duration = endTime - mark.startTime;
  mark.duration = duration;

  // 记录性能数据
  const data: PerformanceData = {
    type,
    name,
    duration,
    timestamp: endTime,
    platform: (process.env.TARO_ENV as Platform) || 'unknown',
    extra,
  };

  recordPerformanceData(data);

  // 清除标记
  performanceMarks.delete(name);

  return duration;
}

/**
 * 测量函数执行时间
 * @param fn 要测量的函数
 * @param name 测量名称
 * @param type 性能类型
 * @returns 函数执行结果和持续时间
 */
export async function measure<T>(
  fn: () => T | Promise<T>,
  name: string,
  type: PerformanceData['type'] = 'custom'
): Promise<{ result: T; duration: number }> {
  markStart(name);

  try {
    const result = await fn();
    const duration = markEnd(name, type);
    return { result, duration };
  } catch (error) {
    markEnd(name, type, { error: true });
    throw error;
  }
}

/**
 * 记录性能数据
 * @param data 性能数据
 */
function recordPerformanceData(data: PerformanceData): void {
  performanceData.push(data);

  // 保持数据量在限制内
  if (performanceData.length > MAX_PERFORMANCE_DATA) {
    performanceData.shift();
  }

  // 通知监听器
  performanceListeners.forEach(listener => {
    try {
      listener(data);
    } catch (error) {
      console.error('Error in performance listener:', error);
    }
  });

  // 存储到本地
  try {
    const storedData = storage.getItem<PerformanceData[]>('app_performance', []);
    storedData.push(data);

    // 只保留最近的 500 条记录
    if (storedData.length > 500) {
      storedData.splice(0, storedData.length - 500);
    }

    storage.setItem('app_performance', storedData);
  } catch (error) {
    console.error('Failed to store performance data:', error);
  }
}

/**
 * 添加性能监听器
 * @param listener 监听器函数
 * @returns 移除监听器的函数
 */
export function addPerformanceListener(
  listener: (data: PerformanceData) => void
): () => void {
  performanceListeners.push(listener);

  return () => {
    const index = performanceListeners.indexOf(listener);
    if (index > -1) {
      performanceListeners.splice(index, 1);
    }
  };
}

/**
 * 获取性能数据
 * @param type 性能类型过滤
 * @returns 性能数据数组
 */
export function getPerformanceData(type?: PerformanceData['type']): PerformanceData[] {
  if (type) {
    return performanceData.filter(data => data.type === type);
  }
  return [...performanceData];
}

/**
 * 获取存储的性能数据
 * @returns 性能数据数组
 */
export function getStoredPerformanceData(): PerformanceData[] {
  try {
    return storage.getItem<PerformanceData[]>('app_performance', []);
  } catch (error) {
    console.error('Failed to get stored performance data:', error);
    return [];
  }
}

/**
 * 清除性能数据
 */
export function clearPerformanceData(): void {
  performanceData.length = 0;
  performanceMarks.clear();

  try {
    storage.removeItem('app_performance');
  } catch (error) {
    console.error('Failed to clear stored performance data:', error);
  }
}

/**
 * 获取内存信息（仅 H5 环境）
 * @returns 内存信息
 */
export function getMemoryInfo(): MemoryInfo | null {
  if (typeof window === 'undefined' || !window.performance || !(window.performance as any).memory) {
    return null;
  }

  const memory = (window.performance as any).memory;
  return {
    usedJSHeapSize: memory.usedJSHeapSize,
    totalJSHeapSize: memory.totalJSHeapSize,
    jsHeapSizeLimit: memory.jsHeapSizeLimit,
  };
}

/**
 * FPS 监控类
 */
class FPSMonitor {
  private frames: number[] = [];
  private lastTime = 0;
  private isRunning = false;
  private animationId?: number;

  start(): void {
    if (this.isRunning || typeof requestAnimationFrame === 'undefined') {
      return;
    }

    this.isRunning = true;
    this.lastTime = performance.now();
    this.tick();
  }

  stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }

  private tick = (): void => {
    if (!this.isRunning) return;

    const now = performance.now();
    const delta = now - this.lastTime;
    this.lastTime = now;

    const fps = 1000 / delta;
    this.frames.push(fps);

    // 只保留最近 60 帧的数据
    if (this.frames.length > 60) {
      this.frames.shift();
    }

    this.animationId = requestAnimationFrame(this.tick);
  };

  getFPS(): FPSInfo {
    if (this.frames.length === 0) {
      return { current: 0, average: 0, min: 0, max: 0 };
    }

    const current = this.frames[this.frames.length - 1] || 0;
    const average = this.frames.reduce((sum, fps) => sum + fps, 0) / this.frames.length;
    const min = Math.min(...this.frames);
    const max = Math.max(...this.frames);

    return { current, average, min, max };
  }
}

/**
 * FPS 监控实例
 */
export const fpsMonitor = new FPSMonitor();

/**
 * 页面加载性能监控
 */
export function measurePageLoad(): void {
  if (typeof window === 'undefined' || !window.performance) {
    return;
  }

  const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

  if (navigation) {
    const data: PerformanceData = {
      type: 'page',
      name: 'page_load',
      duration: navigation.loadEventEnd - navigation.navigationStart,
      timestamp: Date.now(),
      platform: 'h5',
      extra: {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
        firstPaint: 0,
        firstContentfulPaint: 0,
      },
    };

    // 获取 First Paint 和 First Contentful Paint
    const paintEntries = window.performance.getEntriesByType('paint');
    paintEntries.forEach(entry => {
      if (entry.name === 'first-paint') {
        data.extra!.firstPaint = entry.startTime;
      } else if (entry.name === 'first-contentful-paint') {
        data.extra!.firstContentfulPaint = entry.startTime;
      }
    });

    recordPerformanceData(data);
  }
}

/**
 * API 请求性能监控装饰器
 * @param apiName API 名称
 * @returns 装饰器函数
 */
export function withApiPerformance(apiName: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ) {
    const method = descriptor.value!;

    descriptor.value = (async function (this: any, ...args: any[]) {
      const startTime = Date.now();

      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;

        recordPerformanceData({
          type: 'api',
          name: apiName,
          duration,
          timestamp: Date.now(),
          platform: (process.env.TARO_ENV as Platform) || 'unknown',
          extra: { success: true },
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;

        recordPerformanceData({
          type: 'api',
          name: apiName,
          duration,
          timestamp: Date.now(),
          platform: (process.env.TARO_ENV as Platform) || 'unknown',
          extra: { success: false, error: true },
        });

        throw error;
      }
    }) as any;

    return descriptor;
  };
}

/**
 * 组件渲染性能监控 Hook
 * @param componentName 组件名称
 */
export function usePerformanceMonitor(componentName: string): void {
  React.useEffect(() => {
    markStart(`component_${componentName}`);

    return () => {
      markEnd(`component_${componentName}`, 'component');
    };
  }, [componentName]);
}

/**
 * 获取性能统计信息
 * @param type 性能类型
 * @returns 统计信息
 */
export function getPerformanceStats(type?: PerformanceData['type']): {
  count: number;
  average: number;
  min: number;
  max: number;
  total: number;
} {
  const data = getPerformanceData(type);

  if (data.length === 0) {
    return { count: 0, average: 0, min: 0, max: 0, total: 0 };
  }

  const durations = data.map(item => item.duration);
  const total = durations.reduce((sum, duration) => sum + duration, 0);
  const average = total / durations.length;
  const min = Math.min(...durations);
  const max = Math.max(...durations);

  return {
    count: data.length,
    average,
    min,
    max,
    total,
  };
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
  // 是否启用性能监控
  enabled: boolean;
  // 采样率 (0-1)
  sampleRate: number;
  // 最大性能数据条数
  maxEntries: number;
  // 是否自动上报
  autoReport: boolean;
  // 上报间隔（毫秒）
  reportInterval: number;
  // 上报地址
  reportUrl?: string;
  // 性能阈值
  thresholds: {
    pageLoad: number;
    apiResponse: number;
    componentRender: number;
    fps: number;
    memory: number;
  };
}

/**
 * 性能警告信息
 */
interface PerformanceWarning {
  type: string;
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
  suggestions: string[];
}

/**
 * 高级性能监控器
 */
export class AdvancedPerformanceMonitor {
  private config: PerformanceMonitorConfig;
  private performanceData: PerformanceData[] = [];
  private warnings: PerformanceWarning[] = [];
  private observers: PerformanceObserver[] = [];
  private reportTimer?: NodeJS.Timeout;

  constructor(config: Partial<PerformanceMonitorConfig> = {}) {
    this.config = {
      enabled: true,
      sampleRate: 1.0,
      maxEntries: 1000,
      autoReport: false,
      reportInterval: 60000,
      thresholds: {
        pageLoad: 3000,
        apiResponse: 2000,
        componentRender: 100,
        fps: 30,
        memory: 50 * 1024 * 1024, // 50MB
      },
      ...config,
    };

    this.init();
  }

  /**
   * 初始化监控器
   */
  private init() {
    if (!this.config.enabled) return;

    this.setupPerformanceObservers();
    this.setupAutoReport();
    this.monitorLongTasks();
    this.monitorResourceTiming();
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObservers() {
    if (typeof PerformanceObserver === 'undefined') return;

    try {
      // 监控导航时间
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.handleNavigationTiming(entry as PerformanceNavigationTiming);
          }
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // 监控资源加载时间
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.handleResourceTiming(entry as PerformanceResourceTiming);
          }
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // 监控用户交互
      const measureObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'measure') {
            this.handleMeasure(entry as PerformanceMeasure);
          }
        }
      });
      measureObserver.observe({ entryTypes: ['measure'] });
      this.observers.push(measureObserver);

    } catch (e) {
      console.warn('Failed to setup performance observers:', e);
    }
  }

  /**
   * 处理导航时间
   */
  private handleNavigationTiming(entry: PerformanceNavigationTiming) {
    const loadTime = entry.loadEventEnd - entry.navigationStart;

    this.addPerformanceData({
      type: 'page',
      name: 'page_load',
      duration: loadTime,
      timestamp: Date.now(),
      platform: process.env.TARO_ENV as Platform,
      extra: {
        domContentLoaded: entry.domContentLoadedEventEnd - entry.navigationStart,
        firstPaint: entry.responseStart - entry.navigationStart,
        domInteractive: entry.domInteractive - entry.navigationStart,
        loadComplete: entry.loadEventEnd - entry.navigationStart,
      },
    });

    // 检查性能阈值
    if (loadTime > this.config.thresholds.pageLoad) {
      this.addWarning({
        type: 'page_load',
        message: `Page load time (${loadTime}ms) exceeds threshold (${this.config.thresholds.pageLoad}ms)`,
        value: loadTime,
        threshold: this.config.thresholds.pageLoad,
        timestamp: Date.now(),
        suggestions: [
          'Optimize images and assets',
          'Reduce bundle size',
          'Use code splitting',
          'Enable compression',
          'Optimize critical rendering path',
        ],
      });
    }
  }

  /**
   * 处理资源时间
   */
  private handleResourceTiming(entry: PerformanceResourceTiming) {
    const loadTime = entry.responseEnd - entry.startTime;

    this.addPerformanceData({
      type: 'custom',
      name: 'resource_load',
      duration: loadTime,
      timestamp: Date.now(),
      platform: process.env.TARO_ENV as Platform,
      extra: {
        name: entry.name,
        size: entry.transferSize,
        type: this.getResourceType(entry.name),
        cached: entry.transferSize === 0,
      },
    });
  }

  /**
   * 处理测量数据
   */
  private handleMeasure(entry: PerformanceMeasure) {
    this.addPerformanceData({
      type: 'custom',
      name: entry.name,
      duration: entry.duration,
      timestamp: Date.now(),
      platform: process.env.TARO_ENV as Platform,
    });
  }

  /**
   * 监控长任务
   */
  private monitorLongTasks() {
    if (typeof PerformanceObserver === 'undefined') return;

    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.addWarning({
            type: 'long_task',
            message: `Long task detected: ${entry.duration}ms`,
            value: entry.duration,
            threshold: 50,
            timestamp: Date.now(),
            suggestions: [
              'Break up long-running tasks',
              'Use web workers for heavy computation',
              'Implement time slicing',
              'Optimize algorithms',
            ],
          });
        }
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);
    } catch (e) {
      console.warn('Long task monitoring not supported:', e);
    }
  }

  /**
   * 监控资源时间
   */
  private monitorResourceTiming() {
    if (typeof window === 'undefined') return;

    // 监控图片加载
    const images = document.querySelectorAll('img');
    images.forEach((img) => {
      if (!img.complete) {
        const startTime = performance.now();
        img.addEventListener('load', () => {
          const loadTime = performance.now() - startTime;
          this.addPerformanceData({
            type: 'custom',
            name: 'image_load',
            duration: loadTime,
            timestamp: Date.now(),
            platform: 'h5',
            extra: {
              src: img.src,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight,
            },
          });
        });
      }
    });
  }

  /**
   * 设置自动上报
   */
  private setupAutoReport() {
    if (!this.config.autoReport || !this.config.reportUrl) return;

    this.reportTimer = setInterval(() => {
      this.reportPerformanceData();
    }, this.config.reportInterval);
  }

  /**
   * 添加性能数据
   */
  addPerformanceData(data: PerformanceData) {
    // 采样率控制
    if (Math.random() > this.config.sampleRate) return;

    this.performanceData.push(data);

    // 限制数据量
    if (this.performanceData.length > this.config.maxEntries) {
      this.performanceData = this.performanceData.slice(-this.config.maxEntries);
    }
  }

  /**
   * 添加性能警告
   */
  addWarning(warning: PerformanceWarning) {
    this.warnings.push(warning);

    // 限制警告数量
    if (this.warnings.length > 100) {
      this.warnings = this.warnings.slice(-100);
    }

    console.warn('Performance warning:', warning);
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
      return 'image';
    } else if (['js', 'mjs'].includes(extension || '')) {
      return 'script';
    } else if (extension === 'css') {
      return 'stylesheet';
    } else if (['woff', 'woff2', 'ttf', 'otf'].includes(extension || '')) {
      return 'font';
    } else {
      return 'other';
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    const stats = {
      totalEntries: this.performanceData.length,
      totalWarnings: this.warnings.length,
      averagePageLoad: 0,
      averageApiResponse: 0,
      memoryUsage: this.getCurrentMemoryUsage(),
      byType: {} as Record<string, number>,
      recentWarnings: this.warnings.slice(-10),
    };

    // 计算平均值
    const pageLoads = this.performanceData.filter(d => d.type === 'page');
    if (pageLoads.length > 0) {
      stats.averagePageLoad = pageLoads.reduce((sum, d) => sum + d.duration, 0) / pageLoads.length;
    }

    const apiCalls = this.performanceData.filter(d => d.type === 'api');
    if (apiCalls.length > 0) {
      stats.averageApiResponse = apiCalls.reduce((sum, d) => sum + d.duration, 0) / apiCalls.length;
    }

    // 按类型统计
    this.performanceData.forEach(data => {
      stats.byType[data.type] = (stats.byType[data.type] || 0) + 1;
    });

    return stats;
  }

  /**
   * 获取当前内存使用情况
   */
  private getCurrentMemoryUsage(): MemoryInfo | null {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      return (performance as any).memory;
    }
    return null;
  }

  /**
   * 上报性能数据
   */
  async reportPerformanceData() {
    if (!this.config.reportUrl || this.performanceData.length === 0) return;

    try {
      const data = {
        performanceData: [...this.performanceData],
        warnings: [...this.warnings],
        stats: this.getPerformanceStats(),
        timestamp: Date.now(),
      };

      // 这里应该使用实际的网络请求库
      console.log('Reporting performance data:', data);

      // 清空已上报的数据
      this.performanceData = [];
      this.warnings = [];
    } catch (e) {
      console.error('Failed to report performance data:', e);
    }
  }

  /**
   * 销毁监控器
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];

    if (this.reportTimer) {
      clearInterval(this.reportTimer);
    }
  }
}

/**
 * 全局性能监控实例
 */
export const advancedPerformanceMonitor = new AdvancedPerformanceMonitor();

/**
 * 初始化性能监控
 */
export function initPerformanceMonitoring(): void {
  // 在 H5 环境中监控页面加载性能
  if (typeof window !== 'undefined') {
    if (document.readyState === 'complete') {
      measurePageLoad();
    } else {
      window.addEventListener('load', measurePageLoad);
    }

    // 启动 FPS 监控
    fpsMonitor.start();
  }

  // 定期记录内存使用情况
  if (typeof setInterval !== 'undefined') {
    setInterval(() => {
      const memoryInfo = getMemoryInfo();
      if (memoryInfo) {
        recordPerformanceData({
          type: 'custom',
          name: 'memory_usage',
          duration: memoryInfo.usedJSHeapSize,
          timestamp: Date.now(),
          platform: 'h5',
          extra: memoryInfo,
        });
      }
    }, 30000); // 每 30 秒记录一次
  }
}
