import type { UserConfigExport } from "@tarojs/cli"

export default {
  mini: {
    // 小程序优化配置
    optimizeMainPackage: {
      enable: true,
      exclude: [
        // 排除不需要在主包中的页面
        'pages/profile/**',
        'pages/settings/**',
      ]
    },

    // 分包配置
    subpackages: [
      {
        root: 'pages/profile',
        pages: [
          'index',
          'edit',
          'settings'
        ]
      },
      {
        root: 'pages/settings',
        pages: [
          'index',
          'about',
          'privacy'
        ]
      }
    ],

    // 预下载分包
    preloadRule: {
      'pages/index/index': {
        network: 'all',
        packages: ['pages/profile']
      }
    },

    // 压缩配置
    minifyXML: {
      collapseWhitespace: true,
      keepClosingSlash: true,
      removeComments: true,
      removeEmptyAttributes: true,
      removeRedundantAttributes: true,
      removeScriptTypeAttributes: true,
      removeStyleLinkTypeAttributes: true,
      useShortDoctype: true,
      minifyCSS: true,
      minifyJS: true
    }
  },

  h5: {
    // 启用 gzip 压缩
    enableExtract: true,

    // 资源压缩和优化
    miniCssExtractPluginOption: {
      ignoreOrder: true,
      filename: 'css/[name].[contenthash:8].css',
      chunkFilename: 'css/[name].[contenthash:8].css'
    },

    // 输出文件名配置
    output: {
      filename: 'js/[name].[contenthash:8].js',
      chunkFilename: 'js/[name].[contenthash:8].js'
    },

    // Vite 配置
    vitePlugins: [
      // 可以在这里添加 Vite 插件
    ],

    // 构建优化
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          // 第三方库单独打包
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all'
          },
          // 公共代码单独打包
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            chunks: 'all',
            reuseExistingChunk: true
          },
          // Taro 相关代码单独打包
          taro: {
            name: 'taro',
            test: /[\\/]node_modules[\\/]@tarojs[\\/]/,
            priority: 15,
            chunks: 'all'
          },
          // React 相关代码单独打包
          react: {
            name: 'react',
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            priority: 20,
            chunks: 'all'
          }
        }
      },
      // 运行时代码单独打包
      runtimeChunk: {
        name: 'runtime'
      }
    },

    /**
     * Vite 配置
     */
    vite: {
      build: {
        // 启用 CSS 代码分割
        cssCodeSplit: true,

        // 资源内联阈值
        assetsInlineLimit: 4096,

        // 压缩配置
        minify: 'terser',
        terserOptions: {
          compress: {
            // 移除 console
            drop_console: true,
            // 移除 debugger
            drop_debugger: true,
            // 移除无用代码
            dead_code: true,
            // 移除无用变量
            unused: true
          },
          mangle: {
            // 混淆变量名
            toplevel: true
          }
        },

        // 构建目标
        target: ['es2015', 'chrome58', 'firefox57', 'safari11'],

        // 启用 source map（可选）
        sourcemap: false,

        // 构建报告
        reportCompressedSize: true,

        // chunk 大小警告限制
        chunkSizeWarningLimit: 1000,

        // Rollup 配置
        rollupOptions: {
          output: {
            // 手动分包
            manualChunks: {
              // 将 React 相关库打包到一起
              'react-vendor': ['react', 'react-dom'],
              // 将 Taro 相关库打包到一起
              'taro-vendor': ['@tarojs/taro', '@tarojs/components', '@tarojs/runtime'],
              // 将工具库打包到一起
              'utils-vendor': ['lodash', 'dayjs', 'classnames']
            }
          }
        }
      },

      // 依赖优化
      optimizeDeps: {
        include: [
          'react',
          'react-dom',
          '@tarojs/taro',
          '@tarojs/components',
          '@tarojs/runtime'
        ]
      },

      // 服务器配置
      server: {
        // 预热常用文件
        warmup: {
          clientFiles: [
            './src/app.tsx',
            './src/pages/index/index.tsx'
          ]
        }
      }
    },

    /**
     * WebpackChain 插件配置（如果使用 webpack）
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    webpackChain(chain) {
      // 生产环境优化
      if (process.env.NODE_ENV === 'production') {
        // 启用 bundle 分析（可选）
        if (process.env.ANALYZE) {
          const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
          chain.plugin('analyzer').use(BundleAnalyzerPlugin, [{
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: 'bundle-report.html'
          }])
        }

        // 压缩图片
        chain.module
          .rule('images')
          .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
          .use('image-webpack-loader')
          .loader('image-webpack-loader')
          .options({
            mozjpeg: { progressive: true, quality: 80 },
            optipng: { enabled: true },
            pngquant: { quality: [0.65, 0.8], speed: 4 },
            gifsicle: { interlaced: false },
            webp: { quality: 80 }
          })

        // 预加载关键资源
        chain.plugin('preload').use(require('@vue/preload-webpack-plugin'), [{
          rel: 'preload',
          include: 'initial',
          fileBlacklist: [/\.map$/, /hot-update\.js$/]
        }])

        // 预获取其他资源
        chain.plugin('prefetch').use(require('@vue/preload-webpack-plugin'), [{
          rel: 'prefetch',
          include: 'asyncChunks'
        }])
      }
    }
  }
} satisfies UserConfigExport<'vite'>
