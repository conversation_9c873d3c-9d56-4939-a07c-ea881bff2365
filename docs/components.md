# 组件文档

NoeMo App 提供了一套完整的 UI 组件库，支持多端兼容和主题定制。

## 基础组件

### Button 按钮

用于触发操作的按钮组件。

#### 基础用法

```tsx
import { Button } from '@/components';

function Example() {
  return (
    <div>
      <Button>默认按钮</Button>
      <Button type="primary">主要按钮</Button>
      <Button type="secondary">次要按钮</Button>
      <Button type="danger">危险按钮</Button>
    </div>
  );
}
```

#### 不同尺寸

```tsx
<div>
  <Button size="sm">小按钮</Button>
  <Button size="md">中按钮</Button>
  <Button size="lg">大按钮</Button>
</div>
```

#### 加载状态

```tsx
<Button loading>加载中...</Button>
```

#### 禁用状态

```tsx
<Button disabled>禁用按钮</Button>
```

#### API

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| type | 按钮类型 | `'default' \| 'primary' \| 'secondary' \| 'danger' \| 'ghost'` | `'default'` |
| size | 按钮尺寸 | `'sm' \| 'md' \| 'lg'` | `'md'` |
| loading | 加载状态 | `boolean` | `false` |
| disabled | 禁用状态 | `boolean` | `false` |
| block | 块级按钮 | `boolean` | `false` |
| round | 圆角按钮 | `boolean` | `false` |
| onClick | 点击事件 | `(event: any) => void` | - |

### Input 输入框

用于数据输入的表单组件。

#### 基础用法

```tsx
import { Input } from '@/components';

function Example() {
  const [value, setValue] = useState('');
  
  return (
    <Input
      value={value}
      onChange={setValue}
      placeholder="请输入内容"
    />
  );
}
```

#### 不同类型

```tsx
<div>
  <Input type="text" placeholder="文本输入" />
  <Input type="password" placeholder="密码输入" />
  <Input type="number" placeholder="数字输入" />
  <Input type="textarea" placeholder="多行文本" />
</div>
```

#### 带图标

```tsx
<Input
  placeholder="搜索"
  prefix={<Icon name="search" />}
  suffix={<Icon name="close" />}
/>
```

#### 验证状态

```tsx
<div>
  <Input status="success" placeholder="验证成功" />
  <Input status="warning" placeholder="验证警告" />
  <Input status="error" placeholder="验证失败" />
</div>
```

#### API

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| type | 输入框类型 | `'text' \| 'password' \| 'number' \| 'textarea'` | `'text'` |
| value | 输入值 | `string` | - |
| placeholder | 占位符 | `string` | - |
| disabled | 禁用状态 | `boolean` | `false` |
| readonly | 只读状态 | `boolean` | `false` |
| maxLength | 最大长度 | `number` | - |
| prefix | 前缀图标 | `ReactNode` | - |
| suffix | 后缀图标 | `ReactNode` | - |
| status | 验证状态 | `'success' \| 'warning' \| 'error'` | - |
| onChange | 值变化回调 | `(value: string) => void` | - |

### Modal 模态框

用于显示重要信息或进行用户交互的模态框组件。

#### 基础用法

```tsx
import { Modal, Button } from '@/components';

function Example() {
  const [visible, setVisible] = useState(false);
  
  return (
    <div>
      <Button onClick={() => setVisible(true)}>打开模态框</Button>
      <Modal
        visible={visible}
        title="模态框标题"
        onClose={() => setVisible(false)}
      >
        <p>模态框内容</p>
      </Modal>
    </div>
  );
}
```

#### 不同尺寸

```tsx
<Modal size="sm" title="小模态框">内容</Modal>
<Modal size="md" title="中模态框">内容</Modal>
<Modal size="lg" title="大模态框">内容</Modal>
```

#### 自定义底部

```tsx
<Modal
  title="确认操作"
  footer={
    <div>
      <Button onClick={onCancel}>取消</Button>
      <Button type="primary" onClick={onConfirm}>确认</Button>
    </div>
  }
>
  确定要执行此操作吗？
</Modal>
```

#### API

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| visible | 是否显示 | `boolean` | `false` |
| title | 标题 | `string \| ReactNode` | - |
| size | 尺寸 | `'sm' \| 'md' \| 'lg'` | `'md'` |
| closable | 是否显示关闭按钮 | `boolean` | `true` |
| maskClosable | 点击遮罩是否关闭 | `boolean` | `true` |
| footer | 底部内容 | `ReactNode` | - |
| onClose | 关闭回调 | `() => void` | - |

## 布局组件

### Container 容器

用于页面布局的容器组件。

#### 基础用法

```tsx
import { Container } from '@/components';

function Example() {
  return (
    <Container>
      <h1>页面内容</h1>
    </Container>
  );
}
```

#### 不同尺寸

```tsx
<Container size="sm">小容器</Container>
<Container size="md">中容器</Container>
<Container size="lg">大容器</Container>
<Container fluid>流式容器</Container>
```

#### 安全区域适配

```tsx
<Container safeArea="top">顶部安全区域</Container>
<Container safeArea="bottom">底部安全区域</Container>
<Container safeArea>全部安全区域</Container>
```

### Row/Col 网格系统

基于 12 列的网格布局系统。

#### 基础用法

```tsx
import { Row, Col } from '@/components';

function Example() {
  return (
    <Row>
      <Col span={12}>全宽</Col>
      <Col span={6}>半宽</Col>
      <Col span={6}>半宽</Col>
      <Col span={4}>1/3宽</Col>
      <Col span={4}>1/3宽</Col>
      <Col span={4}>1/3宽</Col>
    </Row>
  );
}
```

#### 响应式布局

```tsx
<Row>
  <Col 
    span={24} 
    responsive={{
      sm: 12,
      md: 8,
      lg: 6
    }}
  >
    响应式列
  </Col>
</Row>
```

#### 间距

```tsx
<Row gutter={16}>
  <Col span={12}>列1</Col>
  <Col span={12}>列2</Col>
</Row>
```

### Space 间距

用于设置组件之间的间距。

#### 基础用法

```tsx
import { Space, Button } from '@/components';

function Example() {
  return (
    <Space>
      <Button>按钮1</Button>
      <Button>按钮2</Button>
      <Button>按钮3</Button>
    </Space>
  );
}
```

#### 不同方向

```tsx
<Space direction="vertical">
  <Button>按钮1</Button>
  <Button>按钮2</Button>
</Space>
```

#### 不同尺寸

```tsx
<Space size="lg">
  <Button>按钮1</Button>
  <Button>按钮2</Button>
</Space>
```

## 动画组件

### Transition 过渡动画

用于元素进入和离开的过渡动画。

#### 基础用法

```tsx
import { Transition, Button } from '@/components';

function Example() {
  const [visible, setVisible] = useState(false);
  
  return (
    <div>
      <Button onClick={() => setVisible(!visible)}>
        切换显示
      </Button>
      <Transition visible={visible} type="fade">
        <div>动画内容</div>
      </Transition>
    </div>
  );
}
```

#### 不同动画类型

```tsx
<Transition type="fade">淡入淡出</Transition>
<Transition type="slide-up">向上滑入</Transition>
<Transition type="zoom">缩放</Transition>
<Transition type="bounce">弹跳</Transition>
```

### Loading 加载动画

用于显示加载状态的动画组件。

#### 基础用法

```tsx
import { Loading } from '@/components';

function Example() {
  return <Loading />;
}
```

#### 不同类型

```tsx
<Loading type="spinner" />
<Loading type="dots" />
<Loading type="pulse" />
<Loading type="wave" />
```

#### 带文本

```tsx
<Loading text="加载中..." />
```

#### 全屏加载

```tsx
<Loading fullscreen text="正在加载..." />
```

## 图标组件

### Icon 图标

统一的图标组件，支持多种图标库。

#### 基础用法

```tsx
import { Icon } from '@/components';

function Example() {
  return (
    <div>
      <Icon name="home" />
      <Icon name="user" />
      <Icon name="settings" />
    </div>
  );
}
```

#### 不同尺寸

```tsx
<Icon name="star" size="sm" />
<Icon name="star" size="md" />
<Icon name="star" size="lg" />
```

#### 自定义颜色

```tsx
<Icon name="heart" color="#ff4757" />
```

#### 旋转动画

```tsx
<Icon name="loading" spin />
```

#### 带徽章

```tsx
<Icon name="bell" badge badgeContent="5" />
```

## 主题定制

### 使用 CSS 变量

```css
:root {
  --color-primary: #007AFF;
  --color-success: #34C759;
  --font-size-base: 14px;
  --spacing-md: 16px;
}
```

### 使用 Less 变量

```less
@color-primary: #007AFF;
@color-success: #34C759;
@font-size-base: 14px;
@spacing-md: 16px;
```

### 暗色主题

```tsx
import { useTheme } from '@/stores/appStore';

function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <Button onClick={toggleTheme}>
      {theme === 'light' ? '切换到暗色' : '切换到亮色'}
    </Button>
  );
}
```

## 最佳实践

### 组件命名

- 使用 PascalCase 命名组件
- 组件名应该清晰表达其功能
- 避免使用缩写

### 属性设计

- 使用 TypeScript 定义属性类型
- 提供合理的默认值
- 保持 API 的一致性

### 样式规范

- 使用 BEM 命名规范
- 避免使用内联样式
- 合理使用 CSS 变量

### 性能优化

- 使用 React.memo 优化组件渲染
- 合理使用 useMemo 和 useCallback
- 避免在渲染函数中创建对象
