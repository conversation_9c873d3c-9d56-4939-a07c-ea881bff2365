# API 文档

NoeMo App 提供了丰富的工具函数和服务接口，帮助开发者快速构建应用。

## 工具函数

### 日期工具 (Date Utils)

#### formatDate

格式化日期为指定格式。

```typescript
import { formatDate } from '@/utils/date';

formatDate(new Date(), 'YYYY-MM-DD'); // '2024-01-01'
formatDate(new Date(), 'YYYY年MM月DD日'); // '2024年01月01日'
formatDate(new Date(), 'HH:mm:ss'); // '14:30:25'
```

**参数:**
- `date: Date | string | number` - 日期对象、日期字符串或时间戳
- `format: string` - 格式化模板

**返回值:** `string` - 格式化后的日期字符串

#### parseDate

解析日期字符串为 Date 对象。

```typescript
import { parseDate } from '@/utils/date';

parseDate('2024-01-01'); // Date object
parseDate('2024/01/01 14:30:25'); // Date object
```

#### getRelativeTime

获取相对时间描述。

```typescript
import { getRelativeTime } from '@/utils/date';

getRelativeTime(new Date()); // '刚刚'
getRelativeTime(new Date(Date.now() - 60000)); // '1分钟前'
getRelativeTime(new Date(Date.now() - 3600000)); // '1小时前'
```

#### addDays / subDays

日期加减操作。

```typescript
import { addDays, subDays } from '@/utils/date';

addDays(new Date(), 7); // 7天后
subDays(new Date(), 3); // 3天前
```

### 字符串工具 (String Utils)

#### capitalize

首字母大写。

```typescript
import { capitalize } from '@/utils/string';

capitalize('hello world'); // 'Hello world'
capitalize('HELLO WORLD'); // 'Hello world'
```

#### truncate

截断字符串。

```typescript
import { truncate } from '@/utils/string';

truncate('这是一个很长的字符串', 10); // '这是一个很长的字...'
truncate('short', 10); // 'short'
```

#### slugify

生成 URL 友好的字符串。

```typescript
import { slugify } from '@/utils/string';

slugify('Hello World!'); // 'hello-world'
slugify('中文标题'); // 'zhong-wen-biao-ti'
```

#### camelCase / kebabCase / snakeCase

字符串格式转换。

```typescript
import { camelCase, kebabCase, snakeCase } from '@/utils/string';

camelCase('hello-world'); // 'helloWorld'
kebabCase('helloWorld'); // 'hello-world'
snakeCase('helloWorld'); // 'hello_world'
```

### 数组工具 (Array Utils)

#### unique

数组去重。

```typescript
import { unique } from '@/utils/array';

unique([1, 2, 2, 3, 3, 4]); // [1, 2, 3, 4]
unique(['a', 'b', 'b', 'c']); // ['a', 'b', 'c']

// 对象数组去重
unique(users, 'id'); // 根据 id 字段去重
```

#### groupBy

数组分组。

```typescript
import { groupBy } from '@/utils/array';

const users = [
  { name: 'Alice', role: 'admin' },
  { name: 'Bob', role: 'user' },
  { name: 'Charlie', role: 'admin' }
];

groupBy(users, 'role');
// {
//   admin: [{ name: 'Alice', role: 'admin' }, { name: 'Charlie', role: 'admin' }],
//   user: [{ name: 'Bob', role: 'user' }]
// }
```

#### chunk

数组分块。

```typescript
import { chunk } from '@/utils/array';

chunk([1, 2, 3, 4, 5, 6], 2); // [[1, 2], [3, 4], [5, 6]]
chunk([1, 2, 3, 4, 5], 3); // [[1, 2, 3], [4, 5]]
```

#### sortBy

数组排序。

```typescript
import { sortBy } from '@/utils/array';

const users = [
  { name: 'Charlie', age: 25 },
  { name: 'Alice', age: 30 },
  { name: 'Bob', age: 20 }
];

sortBy(users, 'age'); // 按年龄升序
sortBy(users, 'name'); // 按姓名升序
sortBy(users, ['age', 'name']); // 多字段排序
```

### 对象工具 (Object Utils)

#### pick

选择对象属性。

```typescript
import { pick } from '@/utils/object';

const user = { id: 1, name: 'Alice', email: '<EMAIL>', password: '123' };
pick(user, ['id', 'name', 'email']); // { id: 1, name: 'Alice', email: '<EMAIL>' }
```

#### omit

排除对象属性。

```typescript
import { omit } from '@/utils/object';

const user = { id: 1, name: 'Alice', email: '<EMAIL>', password: '123' };
omit(user, ['password']); // { id: 1, name: 'Alice', email: '<EMAIL>' }
```

#### deepClone

深度克隆对象。

```typescript
import { deepClone } from '@/utils/object';

const original = { a: 1, b: { c: 2 } };
const cloned = deepClone(original);
cloned.b.c = 3; // 不会影响原对象
```

#### merge

合并对象。

```typescript
import { merge } from '@/utils/object';

const obj1 = { a: 1, b: { c: 2 } };
const obj2 = { b: { d: 3 }, e: 4 };
merge(obj1, obj2); // { a: 1, b: { c: 2, d: 3 }, e: 4 }
```

### 验证工具 (Validation Utils)

#### isEmail

验证邮箱格式。

```typescript
import { isEmail } from '@/utils/validation';

isEmail('<EMAIL>'); // true
isEmail('invalid-email'); // false
```

#### isPhone

验证手机号格式。

```typescript
import { isPhone } from '@/utils/validation';

isPhone('13800138000'); // true
isPhone('1380013800'); // false
```

#### isUrl

验证 URL 格式。

```typescript
import { isUrl } from '@/utils/validation';

isUrl('https://example.com'); // true
isUrl('not-a-url'); // false
```

#### isIdCard

验证身份证号格式。

```typescript
import { isIdCard } from '@/utils/validation';

isIdCard('110101199003077777'); // true
isIdCard('invalid-id'); // false
```

## 存储服务

### storage

统一的存储服务，支持多端兼容。

#### 基础用法

```typescript
import { storage } from '@/utils/storage';

// 存储数据
await storage.set('user', { id: 1, name: 'Alice' });
await storage.set('token', 'abc123', 3600); // 1小时过期

// 获取数据
const user = await storage.get('user');
const token = await storage.get('token');

// 删除数据
await storage.remove('token');

// 清空所有数据
await storage.clear();

// 检查是否存在
const hasUser = await storage.has('user');

// 获取所有键
const keys = await storage.keys();
```

#### API

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `set(key, value, ttl?)` | 存储数据 | `key: string, value: any, ttl?: number` | `Promise<void>` |
| `get(key, defaultValue?)` | 获取数据 | `key: string, defaultValue?: any` | `Promise<any>` |
| `remove(key)` | 删除数据 | `key: string` | `Promise<void>` |
| `clear()` | 清空所有数据 | - | `Promise<void>` |
| `has(key)` | 检查是否存在 | `key: string` | `Promise<boolean>` |
| `keys()` | 获取所有键 | - | `Promise<string[]>` |

## 网络服务

### http

HTTP 请求客户端，支持拦截器、重试、缓存等功能。

#### 基础用法

```typescript
import { http } from '@/utils/http';

// GET 请求
const users = await http.get('/api/users');

// POST 请求
const newUser = await http.post('/api/users', {
  name: 'Alice',
  email: '<EMAIL>'
});

// PUT 请求
const updatedUser = await http.put('/api/users/1', {
  name: 'Alice Smith'
});

// DELETE 请求
await http.delete('/api/users/1');
```

#### 高级配置

```typescript
// 带配置的请求
const response = await http.get('/api/users', {
  params: { page: 1, size: 10 },
  timeout: 5000,
  retry: 3,
  cache: true
});

// 上传文件
const result = await http.upload('/api/upload', {
  filePath: 'path/to/file',
  name: 'file',
  formData: { userId: 1 }
});

// 下载文件
await http.download('/api/download/file.pdf', {
  onProgress: (progress) => {
    console.log(`下载进度: ${progress}%`);
  }
});
```

#### 拦截器

```typescript
// 请求拦截器
http.addInterceptor({
  onRequest: (config) => {
    // 添加认证头
    config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  onResponse: (response) => {
    // 处理响应数据
    return response.data;
  },
  onResponseError: (error) => {
    // 处理错误
    if (error.code === 401) {
      // 跳转到登录页
      Taro.navigateTo({ url: '/pages/login/index' });
    }
    throw error;
  }
});
```

## 状态管理

### useUserStore

用户状态管理。

```typescript
import { useUserStore } from '@/stores/userStore';

function Profile() {
  const {
    user,
    isLoggedIn,
    login,
    logout,
    updateProfile
  } = useUserStore();

  const handleLogin = async () => {
    await login('username', 'password');
  };

  const handleUpdateProfile = async () => {
    await updateProfile({ name: 'New Name' });
  };

  return (
    <div>
      {isLoggedIn ? (
        <div>
          <h1>欢迎, {user?.name}</h1>
          <button onClick={logout}>退出登录</button>
        </div>
      ) : (
        <button onClick={handleLogin}>登录</button>
      )}
    </div>
  );
}
```

### useAppStore

应用状态管理。

```typescript
import { useAppStore } from '@/stores/appStore';

function App() {
  const {
    theme,
    language,
    loading,
    setTheme,
    setLanguage,
    setLoading
  } = useAppStore();

  return (
    <div className={`app app--${theme}`}>
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        切换主题
      </button>
      <button onClick={() => setLanguage(language === 'zh' ? 'en' : 'zh')}>
        切换语言
      </button>
    </div>
  );
}
```

## 错误处理

### errorMonitor

错误监控服务。

```typescript
import { errorMonitor, handleError } from '@/utils/error';

// 手动捕获错误
try {
  // 可能出错的代码
  throw new Error('Something went wrong');
} catch (error) {
  handleError(error, { context: 'user-action' });
}

// 添加面包屑
errorMonitor.addBreadcrumb('用户点击了按钮', 'user-action');

// 设置用户ID
errorMonitor.setUserId('user123');

// 获取错误统计
const stats = errorMonitor.getErrorStats();
console.log('错误数量:', stats.totalErrors);
```

## 性能监控

### performance

性能监控服务。

```typescript
import { markStart, markEnd, measurePerformance } from '@/utils/performance';

// 标记性能开始和结束
markStart('api-call');
await fetchData();
markEnd('api-call');

// 测量组件渲染性能
const renderTime = measurePerformance(() => {
  // 组件渲染逻辑
});

// 获取性能数据
const performanceData = getPerformanceData();
console.log('页面加载时间:', performanceData.pageLoadTime);
```

## 调试工具

### debug

调试日志服务。

```typescript
import { debug, info, warn, error } from '@/utils/debug';

// 不同级别的日志
debug('调试信息', { data: 'debug data' });
info('普通信息', { data: 'info data' });
warn('警告信息', { data: 'warning data' });
error('错误信息', { data: 'error data' });

// 带标签的日志
import { taggedLog } from '@/utils/debug';

taggedLog('info', '用户登录', { userId: 123 }, ['auth', 'user']);
```

## 类型定义

### 通用类型

```typescript
// 平台类型
type Platform = 'weapp' | 'h5' | 'alipay' | 'swan' | 'tt' | 'qq' | 'jd';

// 主题类型
type Theme = 'light' | 'dark';

// 语言类型
type Language = 'zh' | 'en';

// 响应数据类型
interface ResponseData<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp?: number;
}

// 分页数据类型
interface PaginationData<T = any> {
  list: T[];
  total: number;
  page: number;
  size: number;
}

// 用户类型
interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}
```

## 环境配置

### 开发环境

```typescript
// src/config/env.ts
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';

export function getApiBaseUrl(): string {
  if (isDevelopment) {
    return 'https://dev-api.example.com';
  }
  return 'https://api.example.com';
}
```

### 平台检测

```typescript
import { getCurrentPlatform, isPlatform } from '@/utils/platform';

const platform = getCurrentPlatform(); // 'weapp' | 'h5' | ...

if (isPlatform('weapp')) {
  // 微信小程序特定逻辑
}

if (isPlatform('h5')) {
  // H5 特定逻辑
}
```
