# 最佳实践指南

本指南提供了在 NoeMo App 中开发的最佳实践和编码规范。

## 项目结构

### 文件组织

```
src/
├── components/          # 可复用组件
│   ├── Button/         # 组件文件夹
│   │   ├── index.tsx   # 组件主文件
│   │   ├── index.less  # 组件样式
│   │   └── types.ts    # 类型定义
│   └── index.ts        # 统一导出
├── pages/              # 页面组件
├── stores/             # 状态管理
├── utils/              # 工具函数
├── services/           # API 服务
├── types/              # 全局类型定义
└── styles/             # 全局样式
```

### 命名规范

#### 文件命名

- **组件文件**: 使用 PascalCase，如 `Button.tsx`
- **页面文件**: 使用 kebab-case，如 `user-profile.tsx`
- **工具文件**: 使用 camelCase，如 `dateUtils.ts`
- **样式文件**: 与对应的组件文件同名，如 `Button.less`

#### 变量命名

```typescript
// ✅ 好的命名
const userName = 'Alice';
const isLoggedIn = true;
const userList = [];
const API_BASE_URL = 'https://api.example.com';

// ❌ 不好的命名
const u = 'Alice';
const flag = true;
const data = [];
const url = 'https://api.example.com';
```

## 组件开发

### 组件设计原则

1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 设计通用的、可配置的组件
3. **可组合性**: 组件应该能够很好地组合使用
4. **一致性**: 保持 API 设计的一致性

### 组件结构

```typescript
// Button/index.tsx
import React from 'react';
import { View, Text } from '@tarojs/components';
import classNames from 'classnames';
import { ButtonProps } from './types';
import './index.less';

const Button: React.FC<ButtonProps> = ({
  type = 'default',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  className,
  onClick,
  ...rest
}) => {
  const buttonClass = classNames(
    'button',
    `button--${type}`,
    `button--${size}`,
    {
      'button--loading': loading,
      'button--disabled': disabled,
    },
    className
  );

  const handleClick = (event: any) => {
    if (disabled || loading) return;
    onClick?.(event);
  };

  return (
    <View
      className={buttonClass}
      onClick={handleClick}
      {...rest}
    >
      {loading && <View className="button__loading" />}
      <Text className="button__text">{children}</Text>
    </View>
  );
};

export default Button;
```

### 类型定义

```typescript
// Button/types.ts
export interface ButtonProps {
  /**
   * 按钮类型
   */
  type?: 'default' | 'primary' | 'secondary' | 'danger' | 'ghost';
  
  /**
   * 按钮尺寸
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * 加载状态
   */
  loading?: boolean;
  
  /**
   * 禁用状态
   */
  disabled?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 点击事件
   */
  onClick?: (event: any) => void;
  
  /**
   * 子元素
   */
  children?: React.ReactNode;
}
```

### 样式规范

```less
// Button/index.less
@import '../../styles/variables.less';

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: @border-radius-base;
  font-size: @font-size-base;
  font-weight: @font-weight-medium;
  cursor: pointer;
  transition: all @duration-base @ease-in-out;
  position: relative;
  overflow: hidden;

  // 类型变体
  &--default {
    background-color: @color-white;
    color: @color-text-primary;
    border: 1px solid @color-border;

    &:hover {
      border-color: @color-primary;
      color: @color-primary;
    }
  }

  &--primary {
    background-color: @color-primary;
    color: @color-white;

    &:hover {
      background-color: @color-primary-dark;
    }
  }

  // 尺寸变体
  &--sm {
    height: @button-height-sm;
    padding: 0 @spacing-3;
    font-size: @font-size-sm;
  }

  &--md {
    height: @button-height-md;
    padding: 0 @spacing-4;
  }

  &--lg {
    height: @button-height-lg;
    padding: 0 @spacing-6;
    font-size: @font-size-lg;
  }

  // 状态变体
  &--loading {
    pointer-events: none;
  }

  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  // 加载动画
  &__loading {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: button-loading 1s linear infinite;
    margin-right: @spacing-2;
  }

  &__text {
    flex: 1;
  }
}

@keyframes button-loading {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

## 状态管理

### Store 设计

```typescript
// stores/userStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface UserState {
  user: User | null;
  isLoggedIn: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoggedIn: false,

      login: async (email: string, password: string) => {
        try {
          // API 调用
          const response = await api.post('/auth/login', { email, password });
          const user = response.data;
          
          set({ user, isLoggedIn: true });
        } catch (error) {
          throw error;
        }
      },

      logout: () => {
        set({ user: null, isLoggedIn: false });
      },

      updateProfile: async (data: Partial<User>) => {
        const { user } = get();
        if (!user) return;

        try {
          const response = await api.put(`/users/${user.id}`, data);
          const updatedUser = response.data;
          
          set({ user: updatedUser });
        } catch (error) {
          throw error;
        }
      },
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({ user: state.user, isLoggedIn: state.isLoggedIn }),
    }
  )
);
```

### 在组件中使用

```typescript
// 组件中使用 store
function UserProfile() {
  const { user, updateProfile } = useUserStore();
  const [loading, setLoading] = useState(false);

  const handleUpdate = async (data: Partial<User>) => {
    setLoading(true);
    try {
      await updateProfile(data);
      // 显示成功提示
    } catch (error) {
      // 显示错误提示
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return <div>请先登录</div>;
  }

  return (
    <div>
      <h1>{user.name}</h1>
      {/* 其他内容 */}
    </div>
  );
}
```

## API 服务

### 服务层设计

```typescript
// services/userService.ts
import { http } from '@/utils/http';

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface UpdateProfileRequest {
  name?: string;
  avatar?: string;
}

export const userService = {
  /**
   * 用户登录
   */
  login: (data: LoginRequest) => 
    http.post<User>('/auth/login', data),

  /**
   * 获取用户信息
   */
  getProfile: () => 
    http.get<User>('/user/profile'),

  /**
   * 更新用户信息
   */
  updateProfile: (data: UpdateProfileRequest) => 
    http.put<User>('/user/profile', data),

  /**
   * 用户登出
   */
  logout: () => 
    http.post('/auth/logout'),
};
```

### 错误处理

```typescript
// 在组件中处理 API 错误
import { handleError } from '@/utils/error';

async function fetchUserData() {
  try {
    const user = await userService.getProfile();
    setUser(user);
  } catch (error) {
    // 统一错误处理
    handleError(error, { context: 'fetch-user-data' });
    
    // 显示用户友好的错误信息
    showToast('获取用户信息失败，请稍后重试');
  }
}
```

## 性能优化

### 组件优化

```typescript
// 使用 React.memo 优化组件渲染
const UserCard = React.memo<UserCardProps>(({ user, onEdit }) => {
  return (
    <div className="user-card">
      <img src={user.avatar} alt={user.name} />
      <h3>{user.name}</h3>
      <button onClick={() => onEdit(user)}>编辑</button>
    </div>
  );
});

// 使用 useMemo 优化计算
function UserList({ users, searchTerm }) {
  const filteredUsers = useMemo(() => {
    return users.filter(user => 
      user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [users, searchTerm]);

  return (
    <div>
      {filteredUsers.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  );
}

// 使用 useCallback 优化事件处理
function UserForm({ onSubmit }) {
  const [formData, setFormData] = useState({});

  const handleSubmit = useCallback((event) => {
    event.preventDefault();
    onSubmit(formData);
  }, [formData, onSubmit]);

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
}
```

### 代码分割

```typescript
// 页面级代码分割
import { lazy, Suspense } from 'react';

const UserProfile = lazy(() => import('./pages/UserProfile'));
const Settings = lazy(() => import('./pages/Settings'));

function App() {
  return (
    <Suspense fallback={<Loading />}>
      <Routes>
        <Route path="/profile" component={UserProfile} />
        <Route path="/settings" component={Settings} />
      </Routes>
    </Suspense>
  );
}

// 组件级代码分割
const HeavyComponent = lazy(() => import('./HeavyComponent'));

function ParentComponent() {
  const [showHeavy, setShowHeavy] = useState(false);

  return (
    <div>
      <button onClick={() => setShowHeavy(true)}>
        加载重型组件
      </button>
      {showHeavy && (
        <Suspense fallback={<Loading />}>
          <HeavyComponent />
        </Suspense>
      )}
    </div>
  );
}
```

## 测试

### 单元测试

```typescript
// Button.test.tsx
import { render, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    const { getByText } = render(<Button>Click me</Button>);
    expect(getByText('Click me')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    const { getByText } = render(
      <Button onClick={handleClick}>Click me</Button>
    );
    
    fireEvent.click(getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('disables click when loading', () => {
    const handleClick = jest.fn();
    const { getByText } = render(
      <Button loading onClick={handleClick}>Click me</Button>
    );
    
    fireEvent.click(getByText('Click me'));
    expect(handleClick).not.toHaveBeenCalled();
  });
});
```

### 集成测试

```typescript
// UserProfile.test.tsx
import { render, waitFor } from '@testing-library/react';
import { userService } from '@/services/userService';
import UserProfile from './UserProfile';

// Mock API 服务
jest.mock('@/services/userService');

describe('UserProfile', () => {
  it('displays user information', async () => {
    const mockUser = {
      id: '1',
      name: 'Alice',
      email: '<EMAIL>'
    };

    (userService.getProfile as jest.Mock).mockResolvedValue(mockUser);

    const { getByText } = render(<UserProfile />);

    await waitFor(() => {
      expect(getByText('Alice')).toBeInTheDocument();
      expect(getByText('<EMAIL>')).toBeInTheDocument();
    });
  });
});
```

## 代码质量

### ESLint 配置

```json
// .eslintrc.js
module.exports = {
  extends: [
    '@tarojs/eslint-config',
    '@typescript-eslint/recommended'
  ],
  rules: {
    // 强制使用 TypeScript
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/explicit-function-return-type': 'off',
    
    // React 相关
    'react-hooks/exhaustive-deps': 'warn',
    'react/prop-types': 'off',
    
    // 代码风格
    'prefer-const': 'error',
    'no-var': 'error',
    'no-console': 'warn'
  }
};
```

### Git 提交规范

```bash
# 功能开发
git commit -m "feat: 添加用户登录功能"

# 问题修复
git commit -m "fix: 修复登录表单验证问题"

# 文档更新
git commit -m "docs: 更新 API 文档"

# 样式调整
git commit -m "style: 调整按钮组件样式"

# 代码重构
git commit -m "refactor: 重构用户服务层"

# 测试相关
git commit -m "test: 添加按钮组件单元测试"

# 构建相关
git commit -m "chore: 更新构建配置"
```

## 部署和发布

### 环境配置

```typescript
// config/env.ts
interface EnvConfig {
  apiBaseUrl: string;
  debug: boolean;
  version: string;
}

const configs: Record<string, EnvConfig> = {
  development: {
    apiBaseUrl: 'https://dev-api.example.com',
    debug: true,
    version: process.env.npm_package_version || '1.0.0'
  },
  production: {
    apiBaseUrl: 'https://api.example.com',
    debug: false,
    version: process.env.npm_package_version || '1.0.0'
  }
};

export const config = configs[process.env.NODE_ENV || 'development'];
```

### 构建优化

```bash
# 分析包大小
npm run build:analyze

# 构建优化
npm run build:optimize

# 生产构建
npm run build:prod
```

这些最佳实践将帮助您构建高质量、可维护的 NoeMo App 应用。
