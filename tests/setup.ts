/**
 * Jest 测试设置文件
 */

import '@testing-library/jest-dom';

// 模拟 Taro API
global.wx = {
  getSystemInfoSync: () => ({
    platform: 'devtools',
    system: 'iOS 14.0',
    version: '8.0.5',
    model: 'iPhone 12',
    brand: 'Apple',
    screenWidth: 375,
    screenHeight: 812,
  }),
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateTo: jest.fn(),
  redirectTo: jest.fn(),
  switchTab: jest.fn(),
  navigateBack: jest.fn(),
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  clearStorageSync: jest.fn(),
  request: jest.fn(),
};

// 模拟 window 对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟 IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// 模拟 ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// 模拟 PerformanceObserver
global.PerformanceObserver = class PerformanceObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
};

// 模拟 console 方法（避免测试时输出过多日志）
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// 测试前的全局设置
beforeEach(() => {
  // 清除所有模拟调用
  jest.clearAllMocks();
  
  // 重置 localStorage
  localStorage.clear();
  
  // 重置 sessionStorage
  sessionStorage.clear();
});

// 测试后的清理
afterEach(() => {
  // 清理 DOM
  document.body.innerHTML = '';
  
  // 清理定时器
  jest.clearAllTimers();
});

// 全局测试工具函数
global.testUtils = {
  /**
   * 等待异步操作完成
   */
  waitFor: (ms: number = 0) => new Promise(resolve => setTimeout(resolve, ms)),
  
  /**
   * 模拟用户交互延迟
   */
  userDelay: () => new Promise(resolve => setTimeout(resolve, 100)),
  
  /**
   * 创建模拟事件
   */
  createMockEvent: (type: string, properties: any = {}) => ({
    type,
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
    target: { value: '' },
    ...properties,
  }),
  
  /**
   * 模拟网络请求
   */
  mockRequest: (response: any, delay: number = 0) => {
    return jest.fn().mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve(response), delay))
    );
  },
  
  /**
   * 模拟网络错误
   */
  mockRequestError: (error: any, delay: number = 0) => {
    return jest.fn().mockImplementation(() => 
      new Promise((_, reject) => setTimeout(() => reject(error), delay))
    );
  },
};

// 类型声明
declare global {
  namespace NodeJS {
    interface Global {
      wx: any;
      testUtils: {
        waitFor: (ms?: number) => Promise<void>;
        userDelay: () => Promise<void>;
        createMockEvent: (type: string, properties?: any) => any;
        mockRequest: (response: any, delay?: number) => jest.Mock;
        mockRequestError: (error: any, delay?: number) => jest.Mock;
      };
    }
  }
}
