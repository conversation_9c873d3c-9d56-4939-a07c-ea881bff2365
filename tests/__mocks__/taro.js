/**
 * Taro API 模拟
 */

const Taro = {
  // 界面相关
  showToast: jest.fn(),
  showModal: jest.fn(),
  showLoading: jest.fn(),
  hideLoading: jest.fn(),
  showActionSheet: jest.fn(),
  
  // 导航相关
  navigateTo: jest.fn(),
  redirectTo: jest.fn(),
  switchTab: jest.fn(),
  navigateBack: jest.fn(),
  reLaunch: jest.fn(),
  
  // 存储相关
  setStorage: jest.fn().mockResolvedValue({}),
  getStorage: jest.fn().mockResolvedValue({ data: null }),
  removeStorage: jest.fn().mockResolvedValue({}),
  clearStorage: jest.fn().mockResolvedValue({}),
  setStorageSync: jest.fn(),
  getStorageSync: jest.fn(),
  removeStorageSync: jest.fn(),
  clearStorageSync: jest.fn(),
  
  // 网络请求
  request: jest.fn().mockResolvedValue({
    statusCode: 200,
    data: {},
    header: {},
  }),
  uploadFile: jest.fn().mockResolvedValue({
    statusCode: 200,
    data: '{}',
  }),
  downloadFile: jest.fn().mockResolvedValue({
    statusCode: 200,
    tempFilePath: '/tmp/file',
  }),
  
  // 系统信息
  getSystemInfo: jest.fn().mockResolvedValue({
    platform: 'devtools',
    system: 'iOS 14.0',
    version: '8.0.5',
    model: 'iPhone 12',
    brand: 'Apple',
    screenWidth: 375,
    screenHeight: 812,
    windowWidth: 375,
    windowHeight: 812,
    pixelRatio: 3,
    statusBarHeight: 44,
    safeArea: {
      top: 44,
      left: 0,
      right: 375,
      bottom: 812,
      width: 375,
      height: 768,
    },
  }),
  getSystemInfoSync: jest.fn(() => ({
    platform: 'devtools',
    system: 'iOS 14.0',
    version: '8.0.5',
    model: 'iPhone 12',
    brand: 'Apple',
    screenWidth: 375,
    screenHeight: 812,
    windowWidth: 375,
    windowHeight: 812,
    pixelRatio: 3,
    statusBarHeight: 44,
    safeArea: {
      top: 44,
      left: 0,
      right: 375,
      bottom: 812,
      width: 375,
      height: 768,
    },
  })),
  
  // 设备相关
  getNetworkType: jest.fn().mockResolvedValue({
    networkType: 'wifi',
  }),
  onNetworkStatusChange: jest.fn(),
  offNetworkStatusChange: jest.fn(),
  
  // 位置相关
  getLocation: jest.fn().mockResolvedValue({
    latitude: 39.9042,
    longitude: 116.4074,
    speed: 0,
    accuracy: 65,
    altitude: 0,
    verticalAccuracy: 65,
    horizontalAccuracy: 65,
  }),
  
  // 媒体相关
  chooseImage: jest.fn().mockResolvedValue({
    tempFilePaths: ['/tmp/image1.jpg'],
    tempFiles: [
      {
        path: '/tmp/image1.jpg',
        size: 1024,
      },
    ],
  }),
  previewImage: jest.fn(),
  
  // 文件相关
  saveFile: jest.fn().mockResolvedValue({
    savedFilePath: '/saved/file/path',
  }),
  getSavedFileList: jest.fn().mockResolvedValue({
    fileList: [],
  }),
  removeSavedFile: jest.fn().mockResolvedValue({}),
  
  // 剪贴板
  setClipboardData: jest.fn().mockResolvedValue({}),
  getClipboardData: jest.fn().mockResolvedValue({
    data: '',
  }),
  
  // 振动
  vibrateShort: jest.fn().mockResolvedValue({}),
  vibrateLong: jest.fn().mockResolvedValue({}),
  
  // 屏幕亮度
  setScreenBrightness: jest.fn().mockResolvedValue({}),
  getScreenBrightness: jest.fn().mockResolvedValue({
    value: 0.5,
  }),
  setKeepScreenOn: jest.fn().mockResolvedValue({}),
  
  // 用户信息
  getUserInfo: jest.fn().mockResolvedValue({
    userInfo: {
      nickName: 'Test User',
      avatarUrl: 'https://example.com/avatar.jpg',
      gender: 1,
      city: 'Beijing',
      province: 'Beijing',
      country: 'China',
      language: 'zh_CN',
    },
    rawData: '{}',
    signature: 'signature',
    encryptedData: 'encryptedData',
    iv: 'iv',
  }),
  
  // 登录
  login: jest.fn().mockResolvedValue({
    code: 'test_code',
  }),
  
  // 支付
  requestPayment: jest.fn().mockResolvedValue({}),
  
  // 分享
  showShareMenu: jest.fn().mockResolvedValue({}),
  hideShareMenu: jest.fn().mockResolvedValue({}),
  updateShareMenu: jest.fn().mockResolvedValue({}),
  getShareInfo: jest.fn().mockResolvedValue({
    encryptedData: 'encryptedData',
    iv: 'iv',
  }),
  
  // 生命周期
  onShow: jest.fn(),
  onHide: jest.fn(),
  onError: jest.fn(),
  onPageNotFound: jest.fn(),
  
  // 环境变量
  getEnv: jest.fn(() => 'WEB'),
  ENV_TYPE: {
    WEAPP: 'WEAPP',
    WEB: 'WEB',
    RN: 'RN',
    SWAN: 'SWAN',
    ALIPAY: 'ALIPAY',
    TT: 'TT',
    QQ: 'QQ',
    JD: 'JD',
  },
  
  // 事件中心
  eventCenter: {
    on: jest.fn(),
    off: jest.fn(),
    trigger: jest.fn(),
    once: jest.fn(),
  },
  
  // 页面相关
  getCurrentInstance: jest.fn(() => ({
    page: {
      route: 'pages/index/index',
      options: {},
    },
    router: {
      path: '/pages/index/index',
      params: {},
    },
  })),
  
  // 小程序特有 API
  canIUse: jest.fn(() => true),
  getUpdateManager: jest.fn(() => ({
    onCheckForUpdate: jest.fn(),
    onUpdateReady: jest.fn(),
    onUpdateFailed: jest.fn(),
    applyUpdate: jest.fn(),
  })),
  
  // 性能相关
  reportPerformance: jest.fn(),
  getPerformance: jest.fn(() => ({
    now: jest.fn(() => Date.now()),
  })),
};

module.exports = Taro;
