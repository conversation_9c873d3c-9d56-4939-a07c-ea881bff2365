/**
 * Taro 组件模拟
 */

import React from 'react';

// 基础组件模拟
const View = React.forwardRef(({ children, className, style, onClick, ...props }, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    onClick={onClick}
    {...props}
  >
    {children}
  </div>
));

const Text = React.forwardRef(({ children, className, style, ...props }, ref) => (
  <span
    ref={ref}
    className={className}
    style={style}
    {...props}
  >
    {children}
  </span>
));

const Image = React.forwardRef(({ src, className, style, mode, onLoad, onError, ...props }, ref) => (
  <img
    ref={ref}
    src={src}
    className={className}
    style={style}
    onLoad={onLoad}
    onError={onError}
    {...props}
  />
));

const Input = React.forwardRef(({ 
  value, 
  placeholder, 
  type = 'text', 
  className, 
  style, 
  onInput, 
  onChange,
  onFocus,
  onBlur,
  disabled,
  maxlength,
  ...props 
}, ref) => (
  <input
    ref={ref}
    type={type}
    value={value}
    placeholder={placeholder}
    className={className}
    style={style}
    onChange={(e) => {
      onInput?.(e);
      onChange?.(e);
    }}
    onFocus={onFocus}
    onBlur={onBlur}
    disabled={disabled}
    maxLength={maxlength}
    {...props}
  />
));

const Textarea = React.forwardRef(({ 
  value, 
  placeholder, 
  className, 
  style, 
  onInput, 
  onChange,
  onFocus,
  onBlur,
  disabled,
  maxlength,
  ...props 
}, ref) => (
  <textarea
    ref={ref}
    value={value}
    placeholder={placeholder}
    className={className}
    style={style}
    onChange={(e) => {
      onInput?.(e);
      onChange?.(e);
    }}
    onFocus={onFocus}
    onBlur={onBlur}
    disabled={disabled}
    maxLength={maxlength}
    {...props}
  />
));

const Button = React.forwardRef(({ 
  children, 
  className, 
  style, 
  onClick, 
  disabled,
  loading,
  type = 'default',
  size = 'default',
  ...props 
}, ref) => (
  <button
    ref={ref}
    className={className}
    style={style}
    onClick={onClick}
    disabled={disabled || loading}
    data-type={type}
    data-size={size}
    data-loading={loading}
    {...props}
  >
    {children}
  </button>
));

const ScrollView = React.forwardRef(({ 
  children, 
  className, 
  style, 
  scrollX,
  scrollY,
  onScroll,
  onScrollToUpper,
  onScrollToLower,
  ...props 
}, ref) => (
  <div
    ref={ref}
    className={className}
    style={{
      overflow: scrollX ? 'auto' : scrollY ? 'auto' : 'hidden',
      ...style,
    }}
    onScroll={onScroll}
    {...props}
  >
    {children}
  </div>
));

const Swiper = React.forwardRef(({ 
  children, 
  className, 
  style, 
  current = 0,
  onChange,
  autoplay,
  interval,
  circular,
  ...props 
}, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    data-current={current}
    data-autoplay={autoplay}
    data-interval={interval}
    data-circular={circular}
    {...props}
  >
    {children}
  </div>
));

const SwiperItem = React.forwardRef(({ children, className, style, ...props }, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    {...props}
  >
    {children}
  </div>
));

const Picker = React.forwardRef(({ 
  children, 
  className, 
  style, 
  mode = 'selector',
  range = [],
  value,
  onChange,
  ...props 
}, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    data-mode={mode}
    data-value={value}
    onClick={() => onChange?.({ detail: { value: 0 } })}
    {...props}
  >
    {children}
  </div>
));

const PickerView = React.forwardRef(({ 
  children, 
  className, 
  style, 
  value = [],
  onChange,
  ...props 
}, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    data-value={value}
    {...props}
  >
    {children}
  </div>
));

const PickerViewColumn = React.forwardRef(({ children, className, style, ...props }, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    {...props}
  >
    {children}
  </div>
));

const Switch = React.forwardRef(({ 
  checked, 
  className, 
  style, 
  onChange, 
  disabled,
  color,
  ...props 
}, ref) => (
  <input
    ref={ref}
    type="checkbox"
    checked={checked}
    className={className}
    style={style}
    onChange={(e) => onChange?.({ detail: { value: e.target.checked } })}
    disabled={disabled}
    data-color={color}
    {...props}
  />
));

const Slider = React.forwardRef(({ 
  value = 0, 
  min = 0, 
  max = 100, 
  step = 1,
  className, 
  style, 
  onChange,
  disabled,
  ...props 
}, ref) => (
  <input
    ref={ref}
    type="range"
    value={value}
    min={min}
    max={max}
    step={step}
    className={className}
    style={style}
    onChange={(e) => onChange?.({ detail: { value: Number(e.target.value) } })}
    disabled={disabled}
    {...props}
  />
));

const Radio = React.forwardRef(({ 
  checked, 
  value, 
  className, 
  style, 
  onChange,
  disabled,
  color,
  ...props 
}, ref) => (
  <input
    ref={ref}
    type="radio"
    checked={checked}
    value={value}
    className={className}
    style={style}
    onChange={(e) => onChange?.({ detail: { value: e.target.value } })}
    disabled={disabled}
    data-color={color}
    {...props}
  />
));

const RadioGroup = React.forwardRef(({ children, className, style, onChange, ...props }, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    onChange={onChange}
    {...props}
  >
    {children}
  </div>
));

const Checkbox = React.forwardRef(({ 
  checked, 
  value, 
  className, 
  style, 
  onChange,
  disabled,
  color,
  ...props 
}, ref) => (
  <input
    ref={ref}
    type="checkbox"
    checked={checked}
    value={value}
    className={className}
    style={style}
    onChange={(e) => onChange?.({ detail: { value: e.target.value } })}
    disabled={disabled}
    data-color={color}
    {...props}
  />
));

const CheckboxGroup = React.forwardRef(({ children, className, style, onChange, ...props }, ref) => (
  <div
    ref={ref}
    className={className}
    style={style}
    onChange={onChange}
    {...props}
  >
    {children}
  </div>
));

const Form = React.forwardRef(({ children, className, style, onSubmit, onReset, ...props }, ref) => (
  <form
    ref={ref}
    className={className}
    style={style}
    onSubmit={onSubmit}
    onReset={onReset}
    {...props}
  >
    {children}
  </form>
));

const Label = React.forwardRef(({ children, className, style, htmlFor, ...props }, ref) => (
  <label
    ref={ref}
    className={className}
    style={style}
    htmlFor={htmlFor}
    {...props}
  >
    {children}
  </label>
));

// 导出所有组件
module.exports = {
  View,
  Text,
  Image,
  Input,
  Textarea,
  Button,
  ScrollView,
  Swiper,
  SwiperItem,
  Picker,
  PickerView,
  PickerViewColumn,
  Switch,
  Slider,
  Radio,
  RadioGroup,
  Checkbox,
  CheckboxGroup,
  Form,
  Label,
};
