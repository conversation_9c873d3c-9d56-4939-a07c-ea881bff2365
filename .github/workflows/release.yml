name: Release

on:
  push:
    tags:
      - 'v*'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # 创建发布
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      release_id: ${{ steps.create_release.outputs.id }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT

      - name: Generate changelog
        id: changelog
        run: |
          # 生成变更日志
          if [ $(git tag --list | wc -l) -gt 1 ]; then
            PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD~1)
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD)
          else
            CHANGELOG=$(git log --pretty=format:"- %s (%h)")
          fi
          
          echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.get_version.outputs.VERSION }}
          release_name: Release ${{ steps.get_version.outputs.VERSION }}
          body: |
            ## 🚀 Release ${{ steps.get_version.outputs.VERSION }}
            
            ### 📝 Changes
            ${{ steps.changelog.outputs.CHANGELOG }}
            
            ### 📦 Assets
            - **H5 Build**: Web application build
            - **WeApp Build**: WeChat Mini Program build
            - **Source Code**: Complete source code archive
            
            ### 🔗 Links
            - [Documentation](https://docs.noemo.app)
            - [Demo](https://demo.noemo.app)
            - [Changelog](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md)
          draft: false
          prerelease: ${{ contains(steps.get_version.outputs.VERSION, 'alpha') || contains(steps.get_version.outputs.VERSION, 'beta') || contains(steps.get_version.outputs.VERSION, 'rc') }}

  # 构建所有平台
  build-all-platforms:
    name: Build All Platforms
    runs-on: ubuntu-latest
    needs: create-release
    strategy:
      matrix:
        platform: [h5, weapp, alipay, swan, tt, qq, jd]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build for ${{ matrix.platform }}
        run: pnpm run build:${{ matrix.platform }}
        env:
          NODE_ENV: production

      - name: Create build archive
        run: |
          cd dist
          tar -czf ../noemo-${{ matrix.platform }}-${{ github.ref_name }}.tar.gz .
          cd ..

      - name: Upload build artifact
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./noemo-${{ matrix.platform }}-${{ github.ref_name }}.tar.gz
          asset_name: noemo-${{ matrix.platform }}-${{ github.ref_name }}.tar.gz
          asset_content_type: application/gzip

  # 构建 Docker 镜像
  build-docker:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: create-release
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: noemo/app
          tags: |
            type=ref,event=tag
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署到生产环境
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [create-release, build-all-platforms]
    environment:
      name: production
      url: https://noemo.app
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build for production
        run: pnpm run build:h5
        env:
          NODE_ENV: production

      - name: Deploy to production
        run: |
          echo "Deploying to production..."
          # 这里添加实际的部署脚本
          # 例如：
          # - 上传到 CDN
          # - 部署到服务器
          # - 更新 DNS 记录
          # - 清除缓存

  # 更新文档
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: create-release
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Generate API documentation
        run: |
          # 生成 API 文档
          npx typedoc --out docs/api src/

      - name: Deploy documentation
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs
          destination_dir: ${{ github.ref_name }}

  # 通知相关人员
  notify:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [create-release, build-all-platforms, deploy-production, update-docs]
    if: always()
    steps:
      - name: Get release info
        id: release_info
        run: |
          echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          echo "IS_PRERELEASE=${{ contains(github.ref_name, 'alpha') || contains(github.ref_name, 'beta') || contains(github.ref_name, 'rc') }}" >> $GITHUB_OUTPUT

      - name: Notify success
        if: needs.deploy-production.result == 'success'
        run: |
          echo "🎉 Release ${{ steps.release_info.outputs.VERSION }} deployed successfully!"
          # 这里可以添加通知逻辑：
          # - 发送邮件
          # - Slack 通知
          # - 微信群通知
          # - 钉钉通知

      - name: Notify failure
        if: needs.deploy-production.result == 'failure'
        run: |
          echo "❌ Release ${{ steps.release_info.outputs.VERSION }} deployment failed!"
          # 这里可以添加失败通知逻辑

  # 性能监控
  performance-monitoring:
    name: Performance Monitoring
    runs-on: ubuntu-latest
    needs: deploy-production
    if: needs.deploy-production.result == 'success'
    steps:
      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://noemo.app
            https://noemo.app/examples
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: Monitor Core Web Vitals
        run: |
          # 监控核心网页指标
          echo "Monitoring Core Web Vitals..."
          # 这里可以集成 PageSpeed Insights API
          # 或其他性能监控工具
