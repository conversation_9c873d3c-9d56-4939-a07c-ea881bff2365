name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # 代码格式检查
  format-check:
    name: Format Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Check Prettier formatting
        run: pnpm run format:check

      - name: Check EditorConfig
        uses: editorconfig-checker/action-editorconfig-checker@main

  # 代码规范检查
  lint-check:
    name: Lint Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint
        run: pnpm run lint

      - name: Run Stylelint
        run: pnpm run lint:style

  # 类型检查
  type-check:
    name: Type Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run TypeScript check
        run: pnpm run type-check

  # 依赖检查
  dependency-check:
    name: Dependency Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Check for duplicate dependencies
        run: npx depcheck

      - name: Check for outdated dependencies
        run: npm outdated || true

      - name: Check for security vulnerabilities
        run: npm audit --audit-level=moderate

  # 代码复杂度检查
  complexity-check:
    name: Complexity Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install complexity tools
        run: |
          npm install -g complexity-report
          npm install -g jscpd

      - name: Run complexity analysis
        run: |
          cr --format json --output complexity-report.json src/
          jscpd --format json --output jscpd-report.json src/

      - name: Upload complexity reports
        uses: actions/upload-artifact@v3
        with:
          name: complexity-reports
          path: |
            complexity-report.json
            jscpd-report.json

  # 代码覆盖率检查
  coverage-check:
    name: Coverage Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests with coverage
        run: pnpm run test:coverage

      - name: Check coverage thresholds
        run: |
          # 检查覆盖率是否达到阈值
          node -e "
            const coverage = require('./coverage/coverage-summary.json');
            const thresholds = { lines: 70, functions: 70, branches: 70, statements: 70 };
            
            Object.entries(thresholds).forEach(([key, threshold]) => {
              const actual = coverage.total[key].pct;
              if (actual < threshold) {
                console.error(\`❌ \${key} coverage \${actual}% is below threshold \${threshold}%\`);
                process.exit(1);
              } else {
                console.log(\`✅ \${key} coverage \${actual}% meets threshold \${threshold}%\`);
              }
            });
          "

      - name: Comment coverage on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const coverage = JSON.parse(fs.readFileSync('./coverage/coverage-summary.json', 'utf8'));
            
            const comment = `
            ## 📊 Code Coverage Report
            
            | Metric | Coverage | Threshold | Status |
            |--------|----------|-----------|--------|
            | Lines | ${coverage.total.lines.pct}% | 70% | ${coverage.total.lines.pct >= 70 ? '✅' : '❌'} |
            | Functions | ${coverage.total.functions.pct}% | 70% | ${coverage.total.functions.pct >= 70 ? '✅' : '❌'} |
            | Branches | ${coverage.total.branches.pct}% | 70% | ${coverage.total.branches.pct >= 70 ? '✅' : '❌'} |
            | Statements | ${coverage.total.statements.pct}% | 70% | ${coverage.total.statements.pct >= 70 ? '✅' : '❌'} |
            
            [View detailed coverage report](https://codecov.io/gh/${{ github.repository }}/pull/${{ github.event.number }})
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # 代码质量评分
  quality-score:
    name: Quality Score
    runs-on: ubuntu-latest
    needs: [format-check, lint-check, type-check, dependency-check, complexity-check, coverage-check]
    if: always()
    steps:
      - name: Calculate quality score
        run: |
          # 计算代码质量评分
          score=100
          
          # 检查各个检查的结果
          if [ "${{ needs.format-check.result }}" != "success" ]; then
            score=$((score - 20))
            echo "❌ Format check failed (-20 points)"
          else
            echo "✅ Format check passed"
          fi
          
          if [ "${{ needs.lint-check.result }}" != "success" ]; then
            score=$((score - 25))
            echo "❌ Lint check failed (-25 points)"
          else
            echo "✅ Lint check passed"
          fi
          
          if [ "${{ needs.type-check.result }}" != "success" ]; then
            score=$((score - 20))
            echo "❌ Type check failed (-20 points)"
          else
            echo "✅ Type check passed"
          fi
          
          if [ "${{ needs.dependency-check.result }}" != "success" ]; then
            score=$((score - 15))
            echo "❌ Dependency check failed (-15 points)"
          else
            echo "✅ Dependency check passed"
          fi
          
          if [ "${{ needs.complexity-check.result }}" != "success" ]; then
            score=$((score - 10))
            echo "❌ Complexity check failed (-10 points)"
          else
            echo "✅ Complexity check passed"
          fi
          
          if [ "${{ needs.coverage-check.result }}" != "success" ]; then
            score=$((score - 10))
            echo "❌ Coverage check failed (-10 points)"
          else
            echo "✅ Coverage check passed"
          fi
          
          echo "📊 Final Quality Score: $score/100"
          
          # 设置状态
          if [ $score -ge 90 ]; then
            echo "🏆 Excellent code quality!"
          elif [ $score -ge 80 ]; then
            echo "👍 Good code quality"
          elif [ $score -ge 70 ]; then
            echo "⚠️ Acceptable code quality"
          else
            echo "❌ Poor code quality - needs improvement"
            exit 1
          fi
