{"name": "noemo-app", "version": "1.0.0", "private": true, "description": "", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "React"}, "scripts": {"prepare": "husky", "new": "taro new", "build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch", "build:analyze": "ANALYZE=true npm run build:h5", "build:optimize": "node scripts/build-optimize.js", "build:prod": "NODE_ENV=production npm run build:h5 && npm run build:optimize", "build:all": "npm run build:weapp && npm run build:h5", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "precommit": "lint-staged"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@babel/runtime": "^7.24.4", "@tarojs/components": "4.1.6", "@tarojs/helper": "4.1.6", "@tarojs/plugin-platform-weapp": "4.1.6", "@tarojs/plugin-platform-alipay": "4.1.6", "@tarojs/plugin-platform-tt": "4.1.6", "@tarojs/plugin-platform-swan": "4.1.6", "@tarojs/plugin-platform-jd": "4.1.6", "@tarojs/plugin-platform-qq": "4.1.6", "@tarojs/plugin-platform-h5": "4.1.6", "@tarojs/plugin-platform-harmony-hybrid": "4.1.6", "@tarojs/runtime": "4.1.6", "@tarojs/shared": "4.1.6", "@tarojs/taro": "4.1.6", "@tarojs/plugin-framework-react": "4.1.6", "@tarojs/react": "4.1.6", "react-dom": "^18.0.0", "react": "^18.0.0"}, "devDependencies": {"@tarojs/plugin-generator": "4.1.6", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "lint-staged": "^16.1.2", "husky": "^9.1.7", "stylelint-config-standard": "^38.0.0", "@babel/core": "^7.24.4", "@babel/plugin-transform-class-properties": "7.25.9", "@tarojs/cli": "4.1.6", "@tarojs/vite-runner": "4.1.6", "babel-preset-taro": "4.1.6", "eslint-config-taro": "4.1.6", "eslint": "^8.57.0", "stylelint": "^16.4.0", "terser": "^5.30.4", "vite": "^4.2.0", "@babel/preset-react": "^7.24.1", "@types/react": "^18.0.0", "@vitejs/plugin-react": "^4.3.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.4.0", "react-refresh": "^0.14.0", "less": "^4.2.0", "typescript": "^5.4.5", "postcss": "^8.4.38", "@types/minimatch": "^5"}}